@echo off
REM تشغيل فوري للبرنامج بدون أي تأخير

REM إعداد الترميز للعربية
chcp 65001 >nul 2>&1

REM الانتقال إلى مجلد البرنامج
cd /d "%~dp0"

REM تشغيل البرنامج مباشرة بأسرع طريقة
if exist "main.py" (
    REM محاولة تشغيل بـ pythonw أولاً (الأفضل للتطبيقات الرسومية)
    pythonw main.py >nul 2>&1 && exit
    
    REM إذا فشل pythonw، جرب py launcher
    py main.py >nul 2>&1 && exit
    
    REM إذا فشل py، جرب python عادي
    python main.py >nul 2>&1 && exit
    
    REM إذا فشلت جميع الطرق، عرض رسالة خطأ سريعة
    echo خطأ: فشل في تشغيل البرنامج
    echo تأكد من تثبيت Python
    timeout /t 3 >nul
) else (
    echo خطأ: ملف main.py غير موجود
    timeout /t 3 >nul
)

exit
