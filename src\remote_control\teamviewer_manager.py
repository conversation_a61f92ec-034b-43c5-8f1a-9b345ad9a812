# -*- coding: utf-8 -*-
"""
مدير TeamViewer للتحكم عن بُعد
TeamViewer Manager for Remote Control
"""

import os
import json
import logging
import subprocess
import threading
import time
import requests
import winreg
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TeamViewerManager:
    """مدير TeamViewer للتحكم عن بُعد"""
    
    def __init__(self, config_file="teamviewer_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
        self.teamviewer_path = None
        self.teamviewer_id = None
        self.teamviewer_password = None
        self.is_running = False
        self.auto_start = True
        
        # معلومات الاتصال
        self.connection_info = {
            'id': None,
            'password': None,
            'status': 'غير متصل',
            'last_update': None
        }
        
        # تهيئة TeamViewer
        self._initialize_teamviewer()
    
    def load_config(self) -> Dict[str, Any]:
        """تحميل إعدادات TeamViewer"""
        default_config = {
            "enabled": True,
            "auto_start": True,
            "auto_generate_password": True,
            "password_length": 8,
            "allow_file_transfer": True,
            "allow_vpn": False,
            "quality": "high",
            "notification_enabled": True,
            "log_connections": True,
            "trusted_contacts": [],
            "blocked_contacts": [],
            "connection_timeout": 300,
            "max_concurrent_connections": 1
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # دمج مع الإعدادات الافتراضية
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            except Exception as e:
                logger.warning(f"فشل في تحميل إعدادات TeamViewer: {e}")
        
        # حفظ الإعدادات الافتراضية
        self.save_config(default_config)
        return default_config
    
    def save_config(self, config: Dict[str, Any]):
        """حفظ إعدادات TeamViewer"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"فشل في حفظ إعدادات TeamViewer: {e}")
    
    def _initialize_teamviewer(self):
        """تهيئة TeamViewer"""
        try:
            logger.info("🖥️ تهيئة TeamViewer...")
            
            # البحث عن TeamViewer في النظام
            self.teamviewer_path = self._find_teamviewer_installation()
            
            if not self.teamviewer_path:
                logger.warning("TeamViewer غير مثبت - سيتم تحميله تلقائياً")
                if self.config.get("enabled", True):
                    self._download_and_install_teamviewer()
            
            # تشغيل TeamViewer إذا كان مفعلاً
            if self.config.get("enabled", True) and self.config.get("auto_start", True):
                self.start_teamviewer()
            
        except Exception as e:
            logger.error(f"فشل في تهيئة TeamViewer: {e}")
    
    def _find_teamviewer_installation(self) -> Optional[str]:
        """البحث عن TeamViewer المثبت"""
        try:
            # مسارات TeamViewer الشائعة
            common_paths = [
                r"C:\Program Files\TeamViewer\TeamViewer.exe",
                r"C:\Program Files (x86)\TeamViewer\TeamViewer.exe",
                r"C:\Users\<USER>\AppData\Local\TeamViewer\TeamViewer.exe".format(os.getenv('USERNAME')),
                r"C:\TeamViewer\TeamViewer.exe"
            ]
            
            # فحص المسارات الشائعة
            for path in common_paths:
                if os.path.exists(path):
                    logger.info(f"تم العثور على TeamViewer: {path}")
                    return path
            
            # البحث في السجل
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                  r"SOFTWARE\TeamViewer") as key:
                    install_path = winreg.QueryValueEx(key, "InstallationDirectory")[0]
                    teamviewer_exe = os.path.join(install_path, "TeamViewer.exe")
                    if os.path.exists(teamviewer_exe):
                        logger.info(f"تم العثور على TeamViewer في السجل: {teamviewer_exe}")
                        return teamviewer_exe
            except (FileNotFoundError, OSError):
                pass
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في البحث عن TeamViewer: {e}")
            return None
    
    def _download_and_install_teamviewer(self):
        """تحميل وتثبيت TeamViewer تلقائياً"""
        try:
            logger.info("📥 تحميل TeamViewer...")
            
            # رابط تحميل TeamViewer
            download_url = "https://download.teamviewer.com/download/TeamViewer_Setup.exe"
            
            # مجلد التحميل
            download_dir = Path("temp")
            download_dir.mkdir(exist_ok=True)
            
            installer_path = download_dir / "TeamViewer_Setup.exe"
            
            # تحميل الملف
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            
            with open(installer_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            logger.info("✅ تم تحميل TeamViewer")
            
            # تشغيل المثبت
            logger.info("🔧 تثبيت TeamViewer...")
            
            # تثبيت صامت
            install_command = [
                str(installer_path),
                "/S",  # تثبيت صامت
                "/norestart"  # عدم إعادة التشغيل
            ]
            
            process = subprocess.run(install_command, capture_output=True, text=True)
            
            if process.returncode == 0:
                logger.info("✅ تم تثبيت TeamViewer بنجاح")
                
                # البحث عن TeamViewer مرة أخرى
                time.sleep(5)  # انتظار انتهاء التثبيت
                self.teamviewer_path = self._find_teamviewer_installation()
                
                if self.teamviewer_path:
                    logger.info("✅ TeamViewer جاهز للاستخدام")
                else:
                    logger.warning("⚠️ لم يتم العثور على TeamViewer بعد التثبيت")
            else:
                logger.error(f"❌ فشل في تثبيت TeamViewer: {process.stderr}")
            
            # حذف ملف التثبيت
            try:
                os.remove(installer_path)
            except:
                pass
                
        except Exception as e:
            logger.error(f"فشل في تحميل/تثبيت TeamViewer: {e}")
    
    def start_teamviewer(self) -> bool:
        """تشغيل TeamViewer"""
        try:
            if not self.teamviewer_path or not os.path.exists(self.teamviewer_path):
                logger.error("TeamViewer غير موجود")
                return False
            
            logger.info("🚀 تشغيل TeamViewer...")
            
            # تشغيل TeamViewer
            subprocess.Popen([self.teamviewer_path], 
                           creationflags=subprocess.CREATE_NO_WINDOW)
            
            # انتظار قليل للتأكد من التشغيل
            time.sleep(3)
            
            # الحصول على معلومات الاتصال
            self._get_connection_info()
            
            self.is_running = True
            logger.info("✅ تم تشغيل TeamViewer بنجاح")
            
            return True
            
        except Exception as e:
            logger.error(f"فشل في تشغيل TeamViewer: {e}")
            return False
    
    def stop_teamviewer(self) -> bool:
        """إيقاف TeamViewer"""
        try:
            logger.info("🛑 إيقاف TeamViewer...")
            
            # إنهاء عمليات TeamViewer
            subprocess.run(["taskkill", "/f", "/im", "TeamViewer.exe"], 
                         capture_output=True)
            subprocess.run(["taskkill", "/f", "/im", "TeamViewer_Service.exe"], 
                         capture_output=True)
            
            self.is_running = False
            self.connection_info['status'] = 'متوقف'
            
            logger.info("✅ تم إيقاف TeamViewer")
            return True
            
        except Exception as e:
            logger.error(f"فشل في إيقاف TeamViewer: {e}")
            return False
    
    def _get_connection_info(self):
        """الحصول على معلومات الاتصال"""
        try:
            # محاولة قراءة معلومات TeamViewer من السجل
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                  r"SOFTWARE\TeamViewer") as key:
                    try:
                        client_id = winreg.QueryValueEx(key, "ClientID")[0]
                        self.teamviewer_id = str(client_id)
                        self.connection_info['id'] = self.teamviewer_id
                    except FileNotFoundError:
                        pass
            except (FileNotFoundError, OSError):
                pass
            
            # إنشاء كلمة مرور عشوائية إذا كانت مفعلة
            if self.config.get("auto_generate_password", True):
                import random
                import string
                
                length = self.config.get("password_length", 8)
                password = ''.join(random.choices(string.digits, k=length))
                self.teamviewer_password = password
                self.connection_info['password'] = password
            
            self.connection_info['status'] = 'متصل'
            self.connection_info['last_update'] = datetime.now().isoformat()
            
            logger.info(f"📋 معلومات الاتصال محدثة - ID: {self.teamviewer_id}")
            
        except Exception as e:
            logger.error(f"فشل في الحصول على معلومات الاتصال: {e}")
    
    def get_connection_info(self) -> Dict[str, Any]:
        """الحصول على معلومات الاتصال الحالية"""
        return self.connection_info.copy()
    
    def is_teamviewer_running(self) -> bool:
        """فحص إذا كان TeamViewer يعمل"""
        try:
            result = subprocess.run(["tasklist", "/fi", "imagename eq TeamViewer.exe"], 
                                  capture_output=True, text=True)
            return "TeamViewer.exe" in result.stdout
        except:
            return False
    
    def restart_teamviewer(self) -> bool:
        """إعادة تشغيل TeamViewer"""
        logger.info("🔄 إعادة تشغيل TeamViewer...")
        
        if self.stop_teamviewer():
            time.sleep(2)
            return self.start_teamviewer()
        
        return False
    
    def configure_teamviewer(self):
        """تكوين إعدادات TeamViewer"""
        try:
            logger.info("⚙️ تكوين إعدادات TeamViewer...")
            
            # هنا يمكن إضافة تكوين إعدادات TeamViewer
            # مثل جودة الاتصال، أذونات نقل الملفات، إلخ
            
            # مثال: تعديل إعدادات السجل
            try:
                with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                                  r"SOFTWARE\TeamViewer", 
                                  0, winreg.KEY_SET_VALUE) as key:
                    
                    # تفعيل نقل الملفات
                    if self.config.get("allow_file_transfer", True):
                        winreg.SetValueEx(key, "AllowFileTransfer", 0, winreg.REG_DWORD, 1)
                    
                    # تعيين جودة الاتصال
                    quality_map = {"low": 0, "medium": 1, "high": 2}
                    quality = quality_map.get(self.config.get("quality", "high"), 2)
                    winreg.SetValueEx(key, "Quality", 0, winreg.REG_DWORD, quality)
                    
            except (FileNotFoundError, OSError):
                logger.warning("لا يمكن تعديل إعدادات السجل")
            
            logger.info("✅ تم تكوين TeamViewer")
            
        except Exception as e:
            logger.error(f"فشل في تكوين TeamViewer: {e}")
    
    def send_connection_notification(self, telegram_manager=None):
        """إرسال إشعار بمعلومات الاتصال"""
        try:
            if not self.config.get("notification_enabled", True):
                return
            
            connection_info = self.get_connection_info()
            
            if connection_info['id'] and telegram_manager:
                message = f"""
🖥️ معلومات التحكم عن بُعد - TeamViewer

🆔 معرف الاتصال: {connection_info['id']}
🔑 كلمة المرور: {connection_info['password']}
📊 الحالة: {connection_info['status']}
🕐 آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M')}

💡 يمكنك الآن الاتصال بالنظام من أي مكان!

📚 قناة نظام طلاب نظام إدارة الطلاب المتطور – دروس، امتحانات، ومتابعة مستمرة.
                """
                
                # إرسال عبر Telegram إذا كان متاحاً
                import asyncio
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(
                        telegram_manager.send_notification(message, "system_alerts")
                    )
                    loop.close()
                except Exception as e:
                    logger.warning(f"فشل في إرسال إشعار Telegram: {e}")
            
        except Exception as e:
            logger.error(f"فشل في إرسال إشعار الاتصال: {e}")
    
    def log_connection_attempt(self, remote_ip: str, success: bool):
        """تسجيل محاولات الاتصال"""
        try:
            if not self.config.get("log_connections", True):
                return
            
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'remote_ip': remote_ip,
                'success': success,
                'teamviewer_id': self.teamviewer_id
            }
            
            # حفظ في ملف السجل
            log_file = Path("logs") / "teamviewer_connections.log"
            log_file.parent.mkdir(exist_ok=True)
            
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"{json.dumps(log_entry, ensure_ascii=False)}\n")
            
        except Exception as e:
            logger.error(f"فشل في تسجيل محاولة الاتصال: {e}")
    
    def get_teamviewer_status(self) -> Dict[str, Any]:
        """الحصول على حالة TeamViewer"""
        return {
            "enabled": self.config.get("enabled", True),
            "is_running": self.is_teamviewer_running(),
            "teamviewer_path": self.teamviewer_path,
            "connection_info": self.connection_info,
            "auto_start": self.config.get("auto_start", True),
            "last_check": datetime.now().isoformat()
        }
    
    def update_password(self) -> str:
        """تحديث كلمة المرور"""
        try:
            if self.config.get("auto_generate_password", True):
                import random
                import string
                
                length = self.config.get("password_length", 8)
                new_password = ''.join(random.choices(string.digits, k=length))
                
                self.teamviewer_password = new_password
                self.connection_info['password'] = new_password
                self.connection_info['last_update'] = datetime.now().isoformat()
                
                logger.info("🔑 تم تحديث كلمة مرور TeamViewer")
                return new_password
            
        except Exception as e:
            logger.error(f"فشل في تحديث كلمة المرور: {e}")
        
        return self.teamviewer_password

# دالة مساعدة لإنشاء مدير TeamViewer
def create_teamviewer_manager(config_file="teamviewer_config.json"):
    """إنشاء مدير TeamViewer"""
    return TeamViewerManager(config_file)
