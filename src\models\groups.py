# -*- coding: utf-8 -*-
"""
نموذج المجموعات
Groups Model

يحتوي على جميع العمليات المتعلقة بإدارة المجموعات
"""

from typing import List, Dict, Any, Optional
from ..database.database_manager import DatabaseManager

class Groups:
    """فئة إدارة المجموعات"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self._ensure_groups_table()
    
    def _ensure_groups_table(self):
        """التأكد من وجود جدول المجموعات"""
        self.db.execute_query("""
            CREATE TABLE IF NOT EXISTS groups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                group_name TEXT NOT NULL UNIQUE,
                description TEXT,
                max_students INTEGER DEFAULT 30,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        """)
        
        # إضافة مجموعات افتراضية إذا لم تكن موجودة
        default_groups = [
            ('مجموعة أ', 'المجموعة الأولى'),
            ('مجموعة ب', 'المجموعة الثانية'),
            ('مجموعة ج', 'المجموعة الثالثة'),
            ('مجموعة د', 'المجموعة الرابعة')
        ]
        
        for group_name, description in default_groups:
            existing = self.db.fetch_one(
                "SELECT id FROM groups WHERE group_name = ?", 
                (group_name,)
            )
            if not existing:
                self.add_group(group_name, description)
    
    def add_group(self, group_name: str, description: str = "", max_students: int = 30) -> Optional[int]:
        """إضافة مجموعة جديدة"""
        try:
            # التحقق من عدم تكرار اسم المجموعة
            existing = self.db.fetch_one(
                "SELECT id FROM groups WHERE group_name = ?", 
                (group_name,)
            )
            
            if existing:
                raise ValueError(f"المجموعة '{group_name}' موجودة مسبقاً")
            
            # إدراج المجموعة الجديدة
            cursor = self.db.execute_query("""
                INSERT INTO groups (group_name, description, max_students)
                VALUES (?, ?, ?)
            """, (group_name, description, max_students))
            
            return cursor.lastrowid
            
        except Exception as e:
            print(f"خطأ في إضافة المجموعة: {e}")
            return None
    
    def get_all_groups(self) -> List[Dict[str, Any]]:
        """جلب جميع المجموعات النشطة"""
        return self.db.fetch_all("""
            SELECT g.*, 
                   COUNT(s.id) as student_count
            FROM groups g
            LEFT JOIN students s ON g.group_name = s.group_name
            WHERE g.is_active = 1
            GROUP BY g.id, g.group_name
            ORDER BY g.group_name
        """)
    
    def get_group_by_id(self, group_id: int) -> Optional[Dict[str, Any]]:
        """جلب مجموعة بالمعرف"""
        return self.db.fetch_one("""
            SELECT g.*, 
                   COUNT(s.id) as student_count
            FROM groups g
            LEFT JOIN students s ON g.group_name = s.group_name
            WHERE g.id = ?
            GROUP BY g.id, g.group_name
        """, (group_id,))
    
    def get_group_by_name(self, group_name: str) -> Optional[Dict[str, Any]]:
        """جلب مجموعة بالاسم"""
        return self.db.fetch_one("""
            SELECT g.*, 
                   COUNT(s.id) as student_count
            FROM groups g
            LEFT JOIN students s ON g.group_name = s.group_name
            WHERE g.group_name = ?
            GROUP BY g.id, g.group_name
        """, (group_name,))
    
    def get_students_in_group(self, group_name: str) -> List[Dict[str, Any]]:
        """جلب جميع الطلاب في مجموعة معينة"""
        return self.db.fetch_all("""
            SELECT * FROM students 
            WHERE group_name = ?
            ORDER BY CAST(student_code AS INTEGER)
        """, (group_name,))
    
    def update_group(self, group_id: int, group_data: Dict[str, Any]) -> bool:
        """تحديث بيانات مجموعة"""
        try:
            # التحقق من عدم تكرار اسم المجموعة (إذا تم تغييره)
            if 'group_name' in group_data:
                existing = self.db.fetch_one("""
                    SELECT id FROM groups 
                    WHERE group_name = ? AND id != ?
                """, (group_data['group_name'], group_id))
                
                if existing:
                    raise ValueError(f"المجموعة '{group_data['group_name']}' موجودة مسبقاً")
            
            # بناء استعلام التحديث
            update_fields = []
            params = []
            
            for field in ['group_name', 'description', 'max_students']:
                if field in group_data:
                    update_fields.append(f"{field} = ?")
                    params.append(group_data[field])
            
            if not update_fields:
                return False
            
            params.append(group_id)
            
            self.db.execute_query(
                f"UPDATE groups SET {', '.join(update_fields)} WHERE id = ?",
                tuple(params)
            )
            
            return True
            
        except Exception as e:
            print(f"خطأ في تحديث المجموعة: {e}")
            return False
    
    def delete_group(self, group_id: int) -> bool:
        """حذف مجموعة (إلغاء تفعيل)"""
        try:
            # التحقق من وجود طلاب في المجموعة
            group = self.get_group_by_id(group_id)
            if not group:
                return False
            
            if group['student_count'] > 0:
                raise ValueError("لا يمكن حذف مجموعة تحتوي على طلاب")
            
            # إلغاء تفعيل المجموعة بدلاً من حذفها
            self.db.execute_query(
                "UPDATE groups SET is_active = 0 WHERE id = ?",
                (group_id,)
            )
            
            return True
            
        except Exception as e:
            print(f"خطأ في حذف المجموعة: {e}")
            return False
    
    def move_student_to_group(self, student_id: int, new_group_name: str) -> bool:
        """نقل طالب إلى مجموعة جديدة"""
        try:
            # التحقق من وجود المجموعة الجديدة
            new_group = self.get_group_by_name(new_group_name)
            if not new_group:
                raise ValueError(f"المجموعة '{new_group_name}' غير موجودة")
            
            # التحقق من عدد الطلاب في المجموعة الجديدة
            if new_group['student_count'] >= new_group['max_students']:
                raise ValueError(f"المجموعة '{new_group_name}' ممتلئة")
            
            # تحديث مجموعة الطالب
            self.db.execute_query(
                "UPDATE students SET group_name = ? WHERE id = ?",
                (new_group_name, student_id)
            )
            
            return True
            
        except Exception as e:
            print(f"خطأ في نقل الطالب: {e}")
            return False
    
    def get_group_statistics(self) -> Dict[str, Any]:
        """إحصائيات المجموعات"""
        stats = {}
        
        # إجمالي المجموعات
        total_groups = self.db.fetch_one(
            "SELECT COUNT(*) as count FROM groups WHERE is_active = 1"
        )
        stats['total_groups'] = total_groups['count'] if total_groups else 0
        
        # إحصائيات كل مجموعة
        group_stats = self.db.fetch_all("""
            SELECT g.group_name, 
                   COUNT(s.id) as student_count,
                   g.max_students,
                   ROUND((COUNT(s.id) * 100.0 / g.max_students), 2) as fill_percentage
            FROM groups g
            LEFT JOIN students s ON g.group_name = s.group_name
            WHERE g.is_active = 1
            GROUP BY g.id, g.group_name, g.max_students
            ORDER BY g.group_name
        """)
        
        stats['by_group'] = group_stats
        
        return stats
    
    def get_group_names(self) -> List[str]:
        """جلب أسماء المجموعات فقط"""
        groups = self.db.fetch_all(
            "SELECT group_name FROM groups WHERE is_active = 1 ORDER BY group_name"
        )
        return [group['group_name'] for group in groups]
