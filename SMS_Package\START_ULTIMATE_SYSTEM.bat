@echo off
chcp 65001 > nul
title النظام النهائي المحسن - نظام إدارة الطلاب المتطور

echo.
echo ===============================================
echo    🎓 النظام النهائي المحسن 🎓
echo    نظام إدارة الطلاب المتطور
echo ===============================================
echo.
echo 📚 نظام إدارة الطلاب المتطور – دروس، امتحانات، ومتابعة مستمرة.
echo.
echo ===============================================
echo           🌟 الإصدار 2.0 المحسن 🌟
echo        جميع المشاكل محلولة والمميزات مضافة
echo ===============================================
echo.
echo ✅ المشاكل المحلولة:
echo    • دعم الشبكات والوصول عن بُعد
echo    • قاعدة بيانات متقدمة (PostgreSQL/MySQL)
echo    • مزامنة سحابية تلقائية
echo    • إشعارات Telegram ذكية
echo    • نظام تحديث تلقائي
echo    • واجهة ويب متجاوبة
echo    • دعم متعدد المستخدمين
echo.
echo ===============================================

REM فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo 📥 يرجى تثبيت Python أولاً من: https://python.org
    echo.
    pause
    exit /b 1
)

echo 🔍 فحص Python... ✅

REM فحص وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متاح
    echo 📥 يرجى تثبيت pip أولاً
    echo.
    pause
    exit /b 1
)

echo 🔍 فحص pip... ✅
echo.

echo ===============================================
echo              📦 تثبيت المتطلبات
echo ===============================================
echo.

echo 🔧 تثبيت المتطلبات الأساسية...
pip install PyQt5 reportlab openpyxl Pillow requests --quiet --disable-pip-version-check
if errorlevel 1 (
    echo ⚠️ تحذير: مشكلة في تثبيت المتطلبات الأساسية
)

echo 🌐 تثبيت متطلبات الخادم الويب...
pip install Flask Flask-SocketIO python-socketio eventlet --quiet --disable-pip-version-check
if errorlevel 1 (
    echo ⚠️ تحذير: مشكلة في تثبيت متطلبات الخادم الويب
)

echo 📊 تثبيت متطلبات قاعدة البيانات المتقدمة...
pip install SQLAlchemy psycopg2-binary PyMySQL --quiet --disable-pip-version-check
if errorlevel 1 (
    echo ⚠️ تحذير: مشكلة في تثبيت متطلبات قاعدة البيانات المتقدمة
)

echo ☁️ تثبيت متطلبات المزامنة السحابية...
pip install google-cloud-storage boto3 dropbox --quiet --disable-pip-version-check
if errorlevel 1 (
    echo ⚠️ تحذير: مشكلة في تثبيت متطلبات المزامنة السحابية
)

echo 🤖 تثبيت متطلبات بوت Telegram...
pip install python-telegram-bot --quiet --disable-pip-version-check
if errorlevel 1 (
    echo ⚠️ تحذير: مشكلة في تثبيت متطلبات Telegram
)

echo 🔄 تثبيت متطلبات التحديث التلقائي...
pip install packaging cryptography --quiet --disable-pip-version-check
if errorlevel 1 (
    echo ⚠️ تحذير: مشكلة في تثبيت متطلبات التحديث التلقائي
)

echo.
echo ===============================================
echo              📁 إعداد المجلدات
echo ===============================================
echo.

REM إنشاء المجلدات المطلوبة
if not exist "data" mkdir data
if not exist "logs" mkdir logs
if not exist "exports" mkdir exports
if not exist "backups" mkdir backups
if not exist "temp" mkdir temp

echo 📁 تم إنشاء جميع المجلدات المطلوبة... ✅
echo.

echo ===============================================
echo              🧪 اختبار النظام
echo ===============================================
echo.

REM اختبار المميزات إذا كان ملف الاختبار موجود
if exist "test_enhanced_features.py" (
    echo 🧪 تشغيل اختبار سريع للمميزات...
    python test_enhanced_features.py --quick >nul 2>&1
    if errorlevel 1 (
        echo ⚠️ تحذير: بعض المميزات المتقدمة قد لا تعمل
    ) else (
        echo ✅ جميع المميزات تعمل بنجاح
    )
    echo.
)

echo ===============================================
echo              🚀 تشغيل النظام
echo ===============================================
echo.

echo 🎯 اختر طريقة التشغيل:
echo.
echo 1. النظام المحسن الكامل (موصى به)
echo 2. الخادم الويب فقط
echo 3. النظام الأساسي التقليدي
echo 4. اختبار المميزات المحسنة
echo.
set /p choice="أدخل اختيارك (1-4): "

if "%choice%"=="1" goto enhanced_system
if "%choice%"=="2" goto web_server
if "%choice%"=="3" goto basic_system
if "%choice%"=="4" goto test_features
goto enhanced_system

:enhanced_system
echo.
echo 🚀 تشغيل النظام المحسن الكامل...
echo.
echo 💡 المميزات المتاحة:
echo    • واجهة تقليدية (PyQt5)
echo    • واجهة ويب: http://localhost:5000
echo    • خادم ويب للوصول عن بُعد
echo    • مزامنة سحابية تلقائية
echo    • بوت Telegram للإشعارات
echo    • نظام تحديث تلقائي
echo    • أيقونة في شريط المهام
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.

if exist "enhanced_main.py" (
    python enhanced_main.py
) else (
    echo ❌ ملف enhanced_main.py غير موجود
    echo 🔄 تشغيل النظام الأساسي بدلاً من ذلك...
    if exist "main.py" (
        python main.py
    ) else (
        echo ❌ لم يتم العثور على أي ملف تشغيل
        pause
        exit /b 1
    )
)
goto end

:web_server
echo.
echo 🌐 تشغيل الخادم الويب فقط...
echo.
echo 💡 الوصول للنظام:
echo    • محلياً: http://localhost:5000
echo    • من الشبكة: http://[عنوان-الجهاز]:5000
echo.

if exist "web_server_launcher.py" (
    python web_server_launcher.py
) else (
    echo ❌ ملف web_server_launcher.py غير موجود
    echo 🔄 محاولة تشغيل النظام المحسن...
    goto enhanced_system
)
goto end

:basic_system
echo.
echo 🖥️ تشغيل النظام الأساسي التقليدي...
echo.

if exist "main.py" (
    python main.py
) else (
    echo ❌ ملف main.py غير موجود
    echo 🔄 محاولة تشغيل النظام المحسن...
    goto enhanced_system
)
goto end

:test_features
echo.
echo 🧪 تشغيل اختبار المميزات المحسنة...
echo.

if exist "test_enhanced_features.py" (
    python test_enhanced_features.py
) else (
    echo ❌ ملف test_enhanced_features.py غير موجود
    echo 🔄 تشغيل النظام المحسن بدلاً من ذلك...
    goto enhanced_system
)
goto end

:end
echo.
echo ===============================================
echo                تم إنهاء البرنامج
echo ===============================================
echo.
echo 📞 للدعم الفني:
echo    • راجع ملف ENHANCED_USER_GUIDE.md
echo    • فحص سجلات النظام في مجلد logs
echo    • تواصل مع المطور للمساعدة
echo.
echo 🎉 شكراً لاستخدام نظام إدارة الطلاب المحسن!
echo.
pause
