#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تشغيل التطبيق الرئيسية
Main Application Launcher
"""

import sys
import os
import ctypes
import subprocess
from datetime import datetime, timedelta

# إخفاء نافذة الأوامر في نظام ويندوز
if os.name == 'nt':
    kernel32 = ctypes.WinDLL('kernel32')
    user32 = ctypes.WinDLL('user32')
    hwnd = kernel32.GetConsoleWindow()
    if hwnd:
        user32.ShowWindow(hwnd, 0)  # 0 يخفي النافذة، 1 يظهرها

def check_license():
    """التحقق من صلاحية الترخيص"""
    try:
        license_file = os.path.join(os.path.dirname(__file__), 'license.lic')
        
        # إذا كان ملف الترخيص غير موجود
        if not os.path.exists(license_file):
            return show_license_window()
            
        # قراءة تاريخ الانتهاء من ملف الترخيص
        with open(license_file, 'r') as f:
            expiry_date_str = f.read().strip()
            
        expiry_date = datetime.strptime(expiry_date_str, '%Y-%m-%d')
        
        # التحقق من صلاحية الترخيص
        if datetime.now() > expiry_date:
            return show_license_window(expired=True)
            
        return True
        
    except Exception as e:
        # في حالة حدوث أي خطأ، نعرض نافذة الترخيص
        return show_license_window()

def show_license_window(expired=False):
    """عرض نافذة إدخال مفتاح الترخيص"""
    from PyQt5.QtWidgets import (QApplication, QDialog, QVBoxLayout, QLabel, 
                               QLineEdit, QPushButton, QMessageBox)
    from PyQt5.QtCore import Qt
    
    app = QApplication(sys.argv)
    
    class LicenseDialog(QDialog):
        def __init__(self, expired=False):
            super().__init__()
            self.expired = expired
            self.init_ui()
            
        def init_ui(self):
            self.setWindowTitle('تفعيل الترخيص')
            self.setFixedSize(400, 250)
            self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)
            
            layout = QVBoxLayout()
            
            # رسالة الترخيص
            if self.expired:
                message = "انتهت صلاحية الترخيص. يرجى تجديد الترخيص."
            else:
                message = "مرحباً! يرجى إدخال مفتاح الترخيص لتفعيل التطبيق."
                
            label = QLabel(message)
            label.setWordWrap(True)
            layout.addWidget(label)
            
            # حقل إدخال مفتاح الترخيص
            self.key_input = QLineEdit()
            self.key_input.setPlaceholderText('أدخل مفتاح الترخيص هنا...')
            layout.addWidget(self.key_input)
            
            # زر التفعيل
            activate_btn = QPushButton('تفعيل الترخيص')
            activate_btn.clicked.connect(self.activate_license)
            layout.addWidget(activate_btn)
            
            # زر الخروج
            exit_btn = QPushButton('خروج')
            exit_btn.clicked.connect(self.reject)
            layout.addWidget(exit_btn)
            
            self.setLayout(layout)
            
        def activate_license(self):
            """تفعيل الترخيص"""
            key = self.key_input.text().strip()
            
            # هنا يمكنك إضافة التحقق من صحة المفتاح
            # هذا مثال بسيط للتوضيح فقط
            if self.validate_key(key):
                # حفظ الترخيص لمدة شهر
                expiry_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
                license_file = os.path.join(os.path.dirname(__file__), 'license.lic')
                
                with open(license_file, 'w') as f:
                    f.write(expiry_date)
                    
                QMessageBox.information(self, 'تم', 'تم تفعيل الترخيص بنجاح حتى ' + expiry_date)
                self.accept()
                return True
            else:
                QMessageBox.warning(self, 'خطأ', 'مفتاح الترخيص غير صالح')
                return False
                
        def validate_key(self, key):
            """التحقق من صحة مفتاح الترخيص"""
            # هذه دالة بسيطة للتحقق من صحة المفتاح
            # يمكن استبدالها بخوارزمية أكثر تعقيداً
            return len(key) >= 10 and any(c.isdigit() for c in key) and any(c.isalpha() for c in key)
    
    dialog = LicenseDialog(expired=expired)
    if dialog.exec_() == QDialog.Accepted:
        return True
    return False

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    # التحقق من الترخيص أولاً
    if not check_license():
        return
    
    # إضافة مسار المجلدات للاستيراد
    sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        from src.ui.login_window import LoginWindow
        from src.database.database_manager import DatabaseManager
        
        app = QApplication(sys.argv)

        # إعداد خصائص التطبيق
        app.setApplicationName("نظام إدارة الطلاب")
        app.setApplicationVersion("1.0")
        app.setOrganizationName("نظام إدارة الطلاب المتطور")

        # إعداد الخط العربي
        font = QFont("Segoe UI", 10)
        app.setFont(font)

        # إعداد اتجاه النص للعربية
        app.setLayoutDirection(Qt.RightToLeft)

        # تهيئة قاعدة البيانات
        db_manager = DatabaseManager()
        db_manager.initialize_database()

        # إنشاء وعرض نافذة تسجيل الدخول
        login_window = LoginWindow()
        login_window.show()

        sys.exit(app.exec_())
        
    except Exception as e:
        import traceback
        error_msg = f"حدث خطأ أثناء تشغيل التطبيق:\n{str(e)}\n\n{traceback.format_exc()}"
        
        # عرض رسالة الخطأ في نافذة رسالة
        try:
            from PyQt5.QtWidgets import QApplication, QMessageBox
            app = QApplication(sys.argv)
            QMessageBox.critical(None, "خطأ", error_msg)
            sys.exit(1)
        except:
            # في حالة فشل إنشاء نافذة الرسالة، نطبع الخطأ في ملف السجل
            with open('error.log', 'w', encoding='utf-8') as f:
                f.write(error_msg)
            sys.exit(1)

if __name__ == "__main__":
    main()
