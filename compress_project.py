#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ضغط المشروع
Compress Project

هذا الملف يقوم بضغط جميع ملفات المشروع في ملف ZIP
"""

import os
import zipfile
import shutil
from pathlib import Path
from datetime import datetime

def create_project_zip():
    """إنشاء ملف ZIP للمشروع"""
    
    # اسم الملف المضغوط
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    zip_filename = f"StudentManagementSystem_{timestamp}.zip"
    
    # مسار المشروع
    project_root = Path(__file__).parent
    
    print("🗜️ بدء ضغط المشروع...")
    print(f"📁 مسار المشروع: {project_root}")
    print(f"📦 اسم الملف المضغوط: {zip_filename}")
    print("=" * 50)
    
    # الملفات والمجلدات المطلوب ضغطها
    files_to_include = [
        # الملفات الرئيسية
        'main.py',
        'run.py', 
        'launch.py',
        'config.py',
        'requirements.txt',
        
        # ملفات التشغيل
        'run_app.bat',
        'start.bat',
        'build.bat',
        
        # ملفات الأدوات
        'fix_imports.py',
        'test_app.py',
        'build_exe.py',
        'compress_project.py',
        
        # ملفات التوثيق
        'README.md',
        'README_FINAL.md',
        'USER_GUIDE.md',
        'INSTALLATION.md',
        'PROJECT_SUMMARY.md'
    ]
    
    # المجلدات المطلوب ضغطها
    dirs_to_include = [
        'src',
        'data',
        'assets'
    ]
    
    # المجلدات التي سيتم إنشاؤها فارغة
    empty_dirs_to_create = [
        'logs',
        'backups', 
        'exports',
        'temp'
    ]
    
    try:
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            
            # إضافة الملفات الرئيسية
            print("📄 إضافة الملفات الرئيسية...")
            for file_name in files_to_include:
                file_path = project_root / file_name
                if file_path.exists():
                    zipf.write(file_path, file_name)
                    print(f"  ✅ {file_name}")
                else:
                    print(f"  ⚠️ {file_name} (غير موجود)")
            
            # إضافة المجلدات
            print("\n📁 إضافة المجلدات...")
            for dir_name in dirs_to_include:
                dir_path = project_root / dir_name
                if dir_path.exists():
                    for root, dirs, files in os.walk(dir_path):
                        for file in files:
                            file_path = Path(root) / file
                            arc_path = file_path.relative_to(project_root)
                            zipf.write(file_path, arc_path)
                    print(f"  ✅ {dir_name}/")
                else:
                    print(f"  ⚠️ {dir_name}/ (غير موجود)")
            
            # إنشاء المجلدات الفارغة
            print("\n📂 إنشاء المجلدات الفارغة...")
            for dir_name in empty_dirs_to_create:
                # إنشاء ملف فارغ في المجلد لضمان إنشاء المجلد في ZIP
                zipf.writestr(f"{dir_name}/.gitkeep", "")
                print(f"  ✅ {dir_name}/")
            
            # إضافة ملف معلومات المشروع
            project_info = f"""# معلومات المشروع

## نظام إدارة الطلاب لنظام إدارة الطلاب المتطور
**تاريخ الضغط:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**الإصدار:** 1.0.0

## طريقة التشغيل:
1. استخرج الملفات من ZIP
2. انقر مرتين على run_app.bat
3. أو شغل: python main.py

## بيانات تسجيل الدخول:
- اسم المستخدم: admin
- كلمة المرور: admin123

## الملفات المهمة:
- run_app.bat: ملف التشغيل الرئيسي
- USER_GUIDE.md: دليل المستخدم
- INSTALLATION.md: دليل التثبيت

## المطور:
مساعد الذكي - تم التطوير خصيصاً لنظام إدارة الطلاب المتطور
"""
            zipf.writestr("PROJECT_INFO.txt", project_info)
            print("  ✅ PROJECT_INFO.txt")
        
        # معلومات الملف المضغوط
        zip_size = os.path.getsize(zip_filename)
        zip_size_mb = zip_size / (1024 * 1024)
        
        print("\n" + "=" * 50)
        print("🎉 تم ضغط المشروع بنجاح!")
        print(f"📦 اسم الملف: {zip_filename}")
        print(f"📏 حجم الملف: {zip_size_mb:.2f} ميجابايت")
        print(f"📍 المسار: {project_root / zip_filename}")
        
        return zip_filename
        
    except Exception as e:
        print(f"❌ خطأ في ضغط المشروع: {e}")
        return None

def create_readme_for_zip():
    """إنشاء ملف README خاص بالملف المضغوط"""
    readme_content = """# نظام إدارة الطلاب - نظام إدارة الطلاب المتطور

## 🚀 التشغيل السريع

### الطريقة الأولى (الأسهل):
1. انقر مرتين على `run_app.bat`
2. اختر الخيار المناسب
3. استخدم admin/admin123 لتسجيل الدخول

### الطريقة الثانية:
1. افتح سطر الأوامر في مجلد المشروع
2. شغل: `python main.py`

## 📋 المتطلبات
- Windows 10+
- Python 3.6+ (سيتم تثبيته تلقائياً إذا لم يكن موجوداً)

## 📖 الأدلة
- `USER_GUIDE.md` - دليل المستخدم الكامل
- `INSTALLATION.md` - دليل التثبيت المفصل
- `PROJECT_SUMMARY.md` - ملخص المشروع

## 🔧 في حالة وجود مشاكل
1. شغل `run_app.bat` واختر الخيار 4
2. أو شغل `python fix_imports.py`

## 📞 الدعم
راجع الأدلة المرفقة أو تواصل مع المطور

---
**تم التطوير خصيصاً لنظام إدارة الطلاب المتطور**
"""
    
    with open('README_ZIP.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ تم إنشاء README_ZIP.md")

def main():
    """الدالة الرئيسية"""
    print("📦 ضغط نظام إدارة الطلاب")
    print("=" * 50)
    
    # إنشاء README للملف المضغوط
    create_readme_for_zip()
    
    # ضغط المشروع
    zip_file = create_project_zip()
    
    if zip_file:
        print("\n💡 تعليمات:")
        print("1. احفظ الملف المضغوط في مكان آمن")
        print("2. يمكنك نسخه على أي جهاز Windows")
        print("3. استخرج الملفات وشغل run_app.bat")
        print("\n🎓 نظام إدارة الطلاب جاهز للاستخدام!")
        
        # فتح مجلد الملف
        try:
            import subprocess
            subprocess.run(['explorer', '/select,', zip_file], check=True)
        except:
            pass
            
    else:
        print("❌ فشل في ضغط المشروع")

if __name__ == "__main__":
    main()
