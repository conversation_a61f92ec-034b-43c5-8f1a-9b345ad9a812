#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الطلاب المحسن
Enhanced Student Management System

الإصدار المحسن مع جميع المميزات الجديدة:
- دعم الشبكات والوصول عن بُعد
- قاعدة بيانات متقدمة (PostgreSQL/MySQL)
- مزامنة سحابية تلقائية
- بوت Telegram للإشعارات
- نظام تحديث تلقائي
- واجهة محسنة

المطور: مساعد الذكي
التاريخ: 2025
الإصدار: 2.0.0
"""

import sys
import os
import logging
import threading
import time
import asyncio
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QSplashScreen, QMessageBox, QSystemTrayIcon, QMenu, QAction
from PyQt5.QtCore import Qt, QT<PERSON><PERSON>, pyqtSignal, QObject
from PyQt5.QtGui import QFont, QPixmap, QIcon

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# الاستيرادات الأساسية
from src.ui.login_window import LoginWindow
from src.database.database_manager import DatabaseManager

# الاستيرادات المحسنة
try:
    from src.database.advanced_database_manager import create_advanced_database_manager
    ADVANCED_DB_AVAILABLE = True
except ImportError:
    ADVANCED_DB_AVAILABLE = False
    logging.warning("قاعدة البيانات المتقدمة غير متاحة")

try:
    from src.web_server.web_app import create_web_app
    WEB_SERVER_AVAILABLE = True
except ImportError:
    WEB_SERVER_AVAILABLE = False
    logging.warning("الخادم الويب غير متاح")

try:
    from src.cloud_sync.cloud_manager import create_cloud_sync_manager
    CLOUD_SYNC_AVAILABLE = True
except ImportError:
    CLOUD_SYNC_AVAILABLE = False
    logging.warning("المزامنة السحابية غير متاحة")

try:
    from src.telegram_bot.telegram_manager import create_telegram_manager
    TELEGRAM_BOT_AVAILABLE = True
except ImportError:
    TELEGRAM_BOT_AVAILABLE = False
    logging.warning("بوت Telegram غير متاح")

try:
    from src.updater.update_manager import create_update_manager
    AUTO_UPDATE_AVAILABLE = True
except ImportError:
    AUTO_UPDATE_AVAILABLE = False
    logging.warning("نظام التحديث التلقائي غير متاح")

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/enhanced_app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedSystemManager(QObject):
    """مدير النظام المحسن"""
    
    # إشارات للتحديثات
    status_updated = pyqtSignal(str)
    notification_received = pyqtSignal(str, str)  # title, message
    
    def __init__(self):
        super().__init__()
        
        # المدراء الأساسيون
        self.db_manager = None
        self.advanced_db_manager = None
        self.web_app = None
        self.cloud_sync_manager = None
        self.telegram_manager = None
        self.update_manager = None
        
        # حالة النظام
        self.system_status = {
            'database': False,
            'web_server': False,
            'cloud_sync': False,
            'telegram_bot': False,
            'auto_update': False
        }
        
        # خيوط التشغيل
        self.web_server_thread = None
        
        # تهيئة النظام
        self.initialize_system()
    
    def initialize_system(self):
        """تهيئة جميع مكونات النظام"""
        logger.info("🚀 بدء تهيئة النظام المحسن...")
        
        # تهيئة قاعدة البيانات
        self.initialize_database()
        
        # تهيئة الخادم الويب
        if WEB_SERVER_AVAILABLE:
            self.initialize_web_server()
        
        # تهيئة المزامنة السحابية
        if CLOUD_SYNC_AVAILABLE:
            self.initialize_cloud_sync()
        
        # تهيئة بوت Telegram
        if TELEGRAM_BOT_AVAILABLE:
            self.initialize_telegram_bot()
        
        # تهيئة نظام التحديث
        if AUTO_UPDATE_AVAILABLE:
            self.initialize_auto_update()
        
        logger.info("✅ تم تهيئة النظام المحسن بنجاح")
        self.status_updated.emit("تم تهيئة النظام بنجاح")
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            logger.info("📊 تهيئة قاعدة البيانات...")
            
            # تهيئة قاعدة البيانات الأساسية
            self.db_manager = DatabaseManager()
            self.db_manager.initialize_database()
            
            # تهيئة قاعدة البيانات المتقدمة إذا كانت متاحة
            if ADVANCED_DB_AVAILABLE:
                try:
                    self.advanced_db_manager = create_advanced_database_manager()
                    if self.advanced_db_manager.test_connection():
                        self.advanced_db_manager.create_tables()
                        logger.info("✅ تم تهيئة قاعدة البيانات المتقدمة")
                    else:
                        logger.warning("⚠️ فشل اختبار قاعدة البيانات المتقدمة، استخدام SQLite")
                        self.advanced_db_manager = None
                except Exception as e:
                    logger.error(f"❌ خطأ في قاعدة البيانات المتقدمة: {e}")
                    self.advanced_db_manager = None
            
            self.system_status['database'] = True
            logger.info("✅ تم تهيئة قاعدة البيانات بنجاح")
            
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة قاعدة البيانات: {e}")
            self.system_status['database'] = False
    
    def initialize_web_server(self):
        """تهيئة الخادم الويب"""
        try:
            logger.info("🌐 تهيئة الخادم الويب...")
            
            # استخدام قاعدة البيانات المتقدمة إذا كانت متاحة
            db_to_use = self.advanced_db_manager if self.advanced_db_manager else self.db_manager
            
            self.web_app = create_web_app(db_to_use, port=5000)
            
            # تشغيل الخادم في خيط منفصل
            self.web_server_thread = self.web_app.run_in_thread(debug=False, host='0.0.0.0')
            
            self.system_status['web_server'] = True
            logger.info("✅ تم تشغيل الخادم الويب على المنفذ 5000")
            
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة الخادم الويب: {e}")
            self.system_status['web_server'] = False
    
    def initialize_cloud_sync(self):
        """تهيئة المزامنة السحابية"""
        try:
            logger.info("☁️ تهيئة المزامنة السحابية...")
            
            self.cloud_sync_manager = create_cloud_sync_manager()
            
            # اختبار الاتصالات السحابية
            connections = self.cloud_sync_manager.test_all_connections()
            if any(connections.values()):
                self.cloud_sync_manager.start_auto_sync()
                self.system_status['cloud_sync'] = True
                logger.info("✅ تم تفعيل المزامنة السحابية")
            else:
                logger.warning("⚠️ لا توجد اتصالات سحابية متاحة")
                self.system_status['cloud_sync'] = False
            
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة المزامنة السحابية: {e}")
            self.system_status['cloud_sync'] = False
    
    def initialize_telegram_bot(self):
        """تهيئة بوت Telegram"""
        try:
            logger.info("🤖 تهيئة بوت Telegram...")
            
            self.telegram_manager = create_telegram_manager()
            
            if self.telegram_manager.start_bot():
                self.system_status['telegram_bot'] = True
                logger.info("✅ تم تشغيل بوت Telegram")
                
                # إرسال إشعار بدء التشغيل (سيتم إرساله عند تشغيل البوت)
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(
                        self.telegram_manager.send_notification(
                            "تم تشغيل نظام إدارة الطلاب المحسن بنجاح! 🎉",
                            "system_alerts"
                        )
                    )
                    loop.close()
                except Exception as e:
                    logger.warning(f"فشل في إرسال إشعار Telegram: {e}")
            else:
                logger.warning("⚠️ فشل في تشغيل بوت Telegram")
                self.system_status['telegram_bot'] = False
            
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة بوت Telegram: {e}")
            self.system_status['telegram_bot'] = False
    
    def initialize_auto_update(self):
        """تهيئة نظام التحديث التلقائي"""
        try:
            logger.info("🔄 تهيئة نظام التحديث التلقائي...")
            
            self.update_manager = create_update_manager()
            self.update_manager.start_auto_check()
            
            self.system_status['auto_update'] = True
            logger.info("✅ تم تفعيل نظام التحديث التلقائي")
            
        except Exception as e:
            logger.error(f"❌ فشل في تهيئة نظام التحديث: {e}")
            self.system_status['auto_update'] = False
    
    def get_system_status(self):
        """الحصول على حالة النظام"""
        return self.system_status.copy()
    
    def shutdown_system(self):
        """إغلاق جميع مكونات النظام"""
        logger.info("🛑 إغلاق النظام...")
        
        # إيقاف المزامنة السحابية
        if self.cloud_sync_manager:
            self.cloud_sync_manager.stop_auto_sync()
        
        # إيقاف بوت Telegram
        if self.telegram_manager:
            self.telegram_manager.stop_bot()
        
        # إيقاف نظام التحديث
        if self.update_manager:
            self.update_manager.stop_auto_check()
            self.update_manager.cleanup()
        
        # إغلاق قواعد البيانات
        if self.advanced_db_manager:
            self.advanced_db_manager.close()
        
        logger.info("✅ تم إغلاق النظام بنجاح")

class EnhancedSplashScreen(QSplashScreen):
    """شاشة البداية المحسنة"""
    
    def __init__(self):
        # إنشاء صورة شاشة البداية
        pixmap = QPixmap(600, 400)
        pixmap.fill(Qt.white)
        
        super().__init__(pixmap)
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.SplashScreen)
        
        # إعداد النص
        self.setStyleSheet("""
            QSplashScreen {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 #3498db, stop:1 #2980b9);
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
        """)
    
    def show_message(self, message):
        """عرض رسالة على شاشة البداية"""
        self.showMessage(
            f"\n\n🎓 نظام إدارة الطلاب المحسن\n"
            f"نظام إدارة الطلاب المتطور\n\n"
            f"قناة نظام طلاب نظام إدارة الطلاب المتطور – دروس، امتحانات، ومتابعة مستمرة.\n\n"
            f"📊 {message}\n\n"
            f"الإصدار 2.0.0 - 2025",
            Qt.AlignCenter,
            Qt.white
        )
        QApplication.processEvents()

class SystemTrayManager:
    """مدير شريط النظام"""
    
    def __init__(self, system_manager):
        self.system_manager = system_manager
        self.tray_icon = None
        
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.setup_tray_icon()
    
    def setup_tray_icon(self):
        """إعداد أيقونة شريط النظام"""
        self.tray_icon = QSystemTrayIcon()
        
        # إعداد الأيقونة
        icon = QIcon("assets/icon.png") if os.path.exists("assets/icon.png") else \
               QApplication.style().standardIcon(QApplication.style().SP_ComputerIcon)
        self.tray_icon.setIcon(icon)
        
        # إعداد القائمة
        menu = QMenu()
        
        # معلومات النظام
        status_action = QAction("نظام إدارة الطلاب المحسن")
        status_action.setEnabled(False)
        menu.addAction(status_action)
        menu.addSeparator()
        
        # فتح الواجهة الويب
        web_action = QAction("🌐 فتح الواجهة الويب")
        web_action.triggered.connect(self.open_web_interface)
        menu.addAction(web_action)
        
        # حالة النظام
        system_status_action = QAction("📊 حالة النظام")
        system_status_action.triggered.connect(self.show_system_status)
        menu.addAction(system_status_action)
        
        menu.addSeparator()
        
        # خروج
        exit_action = QAction("❌ خروج")
        exit_action.triggered.connect(QApplication.quit)
        menu.addAction(exit_action)
        
        self.tray_icon.setContextMenu(menu)
        self.tray_icon.show()
        
        # رسالة ترحيب
        self.tray_icon.showMessage(
            "نظام إدارة الطلاب المحسن",
            "تم تشغيل النظام بنجاح!\nجميع المميزات الجديدة متاحة الآن.",
            QSystemTrayIcon.Information,
            5000
        )
    
    def open_web_interface(self):
        """فتح الواجهة الويب"""
        import webbrowser
        webbrowser.open("http://localhost:5000")
    
    def show_system_status(self):
        """عرض حالة النظام"""
        status = self.system_manager.get_system_status()
        
        status_text = "حالة مكونات النظام:\n\n"
        for component, is_active in status.items():
            icon = "✅" if is_active else "❌"
            status_text += f"{icon} {component}\n"
        
        QMessageBox.information(None, "حالة النظام", status_text)

def main():
    """الدالة الرئيسية لتشغيل التطبيق المحسن"""
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد خصائص التطبيق
        app.setApplicationName("نظام إدارة الطلاب المحسن")
        app.setApplicationVersion("2.0.0")
        app.setOrganizationName("نظام إدارة الطلاب المتطور")
        
        # إعداد الخط العربي
        font = QFont("Segoe UI", 12)
        app.setFont(font)
        
        # إعداد اتجاه النص للعربية
        app.setLayoutDirection(Qt.RightToLeft)
        
        # عرض شاشة البداية
        splash = EnhancedSplashScreen()
        splash.show()
        
        # تهيئة مدير النظام
        splash.show_message("تهيئة مدير النظام...")
        system_manager = EnhancedSystemManager()
        
        # إعداد شريط النظام
        tray_manager = SystemTrayManager(system_manager)
        
        # تهيئة قاعدة البيانات
        splash.show_message("تهيئة قاعدة البيانات...")
        time.sleep(1)
        
        # تهيئة الخادم الويب
        if WEB_SERVER_AVAILABLE:
            splash.show_message("تشغيل الخادم الويب...")
            time.sleep(1)
        
        # تهيئة المزامنة السحابية
        if CLOUD_SYNC_AVAILABLE:
            splash.show_message("تفعيل المزامنة السحابية...")
            time.sleep(1)
        
        # تهيئة بوت Telegram
        if TELEGRAM_BOT_AVAILABLE:
            splash.show_message("تشغيل بوت Telegram...")
            time.sleep(1)
        
        # تهيئة نظام التحديث
        if AUTO_UPDATE_AVAILABLE:
            splash.show_message("تفعيل نظام التحديث التلقائي...")
            time.sleep(1)
        
        # إنشاء وعرض نافذة تسجيل الدخول
        splash.show_message("فتح نافذة تسجيل الدخول...")
        time.sleep(1)
        
        # استخدام قاعدة البيانات المناسبة
        db_to_use = system_manager.advanced_db_manager if system_manager.advanced_db_manager else system_manager.db_manager
        
        login_window = LoginWindow(db_to_use)
        
        # إخفاء شاشة البداية وعرض نافذة تسجيل الدخول
        splash.finish(login_window)
        login_window.show()
        
        # معالجة إغلاق التطبيق
        def cleanup():
            system_manager.shutdown_system()
        
        app.aboutToQuit.connect(cleanup)
        
        # تشغيل التطبيق
        return app.exec_()
        
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل التطبيق: {e}")
        QMessageBox.critical(None, "خطأ", f"فشل في تشغيل التطبيق:\n{str(e)}")
        return 1

if __name__ == "__main__":
    # إنشاء مجلد السجلات
    os.makedirs("logs", exist_ok=True)
    
    # تشغيل التطبيق
    sys.exit(main())
