import os
import sys
from pathlib import Path

# Application info
APP_NAME = "نظام إدارة المدارس"
APP_VERSION = "1.0.0"
ORGANIZATION_NAME = "نظام إدارة المدارس المتطور"

# Paths
if getattr(sys, 'frozen', False):
    # Running in a bundle (PyInstaller)
    BASE_DIR = Path(sys._MEIPASS)
else:
    # Running in development
    BASE_DIR = Path(__file__).parent.parent

# Data directories
DATA_DIR = BASE_DIR / 'data'
LOG_DIR = BASE_DIR / 'logs'
LICENSE_DIR = BASE_DIR / 'licenses'

# Create necessary directories
for directory in [DATA_DIR, LOG_DIR, LICENSE_DIR]:
    directory.mkdir(parents=True, exist_ok=True)

# Database
DATABASE_PATH = str(DATA_DIR / 'school_management.db')
DATABASE_URL = f'sqlite:///{DATABASE_PATH}'

# Logging
LOG_FILE = str(LOG_DIR / 'app.log')
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# UI Settings
DEFAULT_FONT_FAMILY = 'Segoe UI'
DEFAULT_FONT_SIZE = 10

# License settings
LICENSE_FILE = str(LICENSE_DIR / 'license.lic')
LICENSE_PUBLIC_KEY = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA... (your public key here)
-----END PUBLIC KEY-----"""

# Demo mode settings
DEMO_DAYS = 14
DEMO_MAX_STUDENTS = 50
DEMO_MAX_USERS = 1

# Grace period (in days)
GRACE_PERIOD_DAYS = 7

# Application icon
if getattr(sys, 'frozen', False):
    APP_ICON = str(BASE_DIR / 'resources' / 'app_icon.ico')
else:
    APP_ICON = str(BASE_DIR / 'src' / 'resources' / 'app_icon.ico')

# TeamViewer settings
TEAMVIEWER_PARTNER_ID = "123456789"

# Styles
STYLES_DIR = str(BASE_DIR / 'src' / 'ui' / 'styles')
DARK_STYLESHEET = str(Path(STYLES_DIR) / 'dark.qss')
LIGHT_STYLESHEET = str(Path(STYLES_DIR) / 'light.qss')

# Application settings
DEFAULT_LANGUAGE = 'ar'
SUPPORTED_LANGUAGES = ['ar', 'en']

# Feature flags
FEATURE_DEMO_MODE = True
FEATURE_ONLINE_ACTIVATION = True
FEATURE_OFFLINE_ACTIVATION = True
