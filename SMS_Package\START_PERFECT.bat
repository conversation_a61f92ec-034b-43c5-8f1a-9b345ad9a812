@echo off
chcp 65001 >nul 2>&1

REM تشغيل مباشر وسريع
cd /d "%~dp0"

REM محاولة التشغيل بأسرع طريقة
pythonw main.py >nul 2>&1 && goto success
py main.py >nul 2>&1 && goto success
python main.py >nul 2>&1 && goto success

REM إذا فشل التشغيل، عرض معلومات مفيدة
cls
title نظام إدارة الطلاب - خطأ في التشغيل
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة الطلاب المتطور                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo ❌ فشل في تشغيل البرنامج
echo.
echo 💡 الحلول السريعة:

echo    1. تثبيت Python من Microsoft Store
echo    2. تثبيت Python من python.org
echo    3. إعادة تشغيل الكمبيوتر
echo    4. تشغيل كمدير (Run as Administrator)
echo.
echo 🌐 فتح صفحة تحميل Python...
start https://python.org/downloads
echo.
goto end

:success
echo تم تشغيل البرنامج بنجاح!
timeout /t 1 >nul
exit

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      معلومات مهمة                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo ⚠️  ملاحظات مهمة:
echo    • يجب إدخال البيانات الصحيحة لتسجيل الدخول
echo    • لا يمكن الدخول بدون كتابة البيانات
echo    • رسائل الخطأ ستظهر بوضوح
echo.
echo 📚 المميزات المتاحة:
echo    ✅ إدارة الطلاب (تم إصلاح الأخطاء)
echo    ✅ تسجيل الحضور السريع
echo    ✅ إدارة درجات الجغرافيا والتاريخ
echo    ✅ تقارير احترافية PDF/Excel
echo    ✅ نظام نسخ احتياطي
echo    ✅ واجهة عربية محسنة
echo.
echo 🎓 تم التطوير والتحسين خصيصاً لنظام إدارة الطلاب المتطور
echo    نظام إدارة الطلاب المتطور
echo.
echo 📞 للدعم الفني: راجع ملف USER_GUIDE.md
echo.
pause
