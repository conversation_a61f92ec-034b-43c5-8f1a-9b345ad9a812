@echo off
title النظام المثالي لإدارة الطلاب - نظام إدارة الطلاب المتطور

echo.
echo ===============================================
echo    النظام المثالي لإدارة الطلاب
echo    Ultimate Student Management System
echo ===============================================
echo.
echo 🎯 الإصدار: 3.0.0 (مثالي)
echo 👨‍🏫 المعلم: نظام إدارة الطلاب المتطور
echo 📚 المواد: الجغرافيا والتاريخ
echo.
echo ===============================================
echo              🔧 فحص النظام
echo ===============================================
echo.

REM فحص Python
echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo.
    echo 📥 يرجى تثبيت Python من: https://python.org
    echo ✅ تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)
echo ✅ Python مثبت بنجاح

REM فحص المكتبات الأساسية
echo 🔍 فحص المكتبات الأساسية...
python -c "import PyQt5" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ PyQt5 غير مثبت، جاري التثبيت...
    pip install PyQt5 --quiet --disable-pip-version-check
)

python -c "import psutil" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ psutil غير مثبت، جاري التثبيت...
    pip install psutil --quiet --disable-pip-version-check
)

echo ✅ جميع المكتبات الأساسية متاحة

echo.
echo ===============================================
echo              📁 إنشاء المجلدات
echo ===============================================
echo.

REM إنشاء المجلدات المطلوبة
if not exist "data" mkdir data
if not exist "logs" mkdir logs
if not exist "exports" mkdir exports
if not exist "backups" mkdir backups
if not exist "temp" mkdir temp
if not exist "config" mkdir config

echo ✅ تم إنشاء جميع المجلدات المطلوبة

echo.
echo ===============================================
echo              🚀 تشغيل النظام المثالي
echo ===============================================
echo.

echo 💡 المميزات الجديدة:
echo    • أداء محسن مع استهلاك ذاكرة منخفض
echo    • يعمل بدون إنترنت
echo    • تشخيص تلقائي للمشاكل
echo    • توافق كامل مع جميع الأنظمة
echo    • أمان متقدم
echo    • صيانة تلقائية
echo    • تحكم عن بُعد متقدم
echo.

echo 🌐 معلومات الاتصال:
echo    • الواجهة المحلية: نافذة التطبيق
echo    • التحكم عن بُعد: http://localhost:8080
echo    • من الشبكة: http://[عنوان-الجهاز]:8080
echo.

echo 🔐 بيانات تسجيل الدخول:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.

REM تشغيل النظام المثالي
if exist "ultimate_system.py" (
    echo 🚀 تشغيل النظام المثالي...
    python ultimate_system.py
) else (
    echo ❌ ملف ultimate_system.py غير موجود
    echo 🔄 محاولة تشغيل النظام المحسن...
    
    if exist "enhanced_main.py" (
        python enhanced_main.py
    ) else (
        echo ❌ لم يتم العثور على أي ملف تشغيل
        echo 📞 يرجى التواصل مع الدعم الفني
        pause
        exit /b 1
    )
)

echo.
echo ===============================================
echo                تم إنهاء البرنامج
echo ===============================================
echo.
echo 📞 للدعم الفني:
echo    • راجع ملف USER_GUIDE.md
echo    • فحص سجلات النظام في مجلد logs
echo    • تواصل مع المطور للمساعدة
echo.
echo 🎉 شكراً لاستخدام النظام المثالي!
pause 