

# نافذة إدارة الفيديوهات التعليمية المحمية
# Protected Educational Videos Management Window

import os
import json
from datetime import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTabWidget, QGroupBox, QFormLayout, QComboBox, QSpinBox, QTextEdit, QTableWidget, QAbstractItemView, QFrame, QLineEdit, QProgressBar, QCheckBox, QListWidget, QMessageBox, QFileDialog, QShortcut, QDialog, QDialogButtonBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QKeySequence

import os
import json
import sqlite3
import hashlib
import time
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, Q<PERSON>abel, 
                            QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                            QComboBox, QMessageBox, QFrame, QGroupBox, QFormLayout,
                            QHeaderView, QAbstractItemView, QSplitter, QDateEdit,
                            QTextEdit, QTabWidget, QFileDialog, QProgressBar,
                            QSpinBox, QCheckBox, QListWidget, QDialog, QDialogButtonBox,
                            QApplication, QShortcut)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QKeySequence

from ..database.database_manager import DatabaseManager
from ..models.student import Student
from ..utils.styles import get_form_style, get_table_style, get_arabic_font_style
from ..utils.message_boxes import show_error_message, show_success_message, show_warning_message

class VideoUploadThread(QThread):
    """خيط رفع الفيديو"""
    progress_updated = pyqtSignal(int)
    upload_finished = pyqtSignal(bool, str)
    
    def __init__(self, video_path, video_info):
        super().__init__()
        self.video_path = video_path
        self.video_info = video_info
        
    def run(self):
        """رفع وتشفير الفيديو"""
        try:
            # محاكاة عملية الرفع والتشفير
            for i in range(101):
                time.sleep(0.05)  # محاكاة الوقت
                self.progress_updated.emit(i)
                
            # حفظ معلومات الفيديو
            self.save_video_info()
            self.upload_finished.emit(True, "تم رفع الفيديو بنجاح")
            
        except Exception as e:
            self.upload_finished.emit(False, f"خطأ في رفع الفيديو: {str(e)}")
            
    def save_video_info(self):
        """حفظ معلومات الفيديو"""
        try:
            # إنشاء مجلد الفيديوهات
            videos_folder = "encrypted_videos"
            os.makedirs(videos_folder, exist_ok=True)
            
            # حفظ معلومات الفيديو في JSON
            video_data = {
                'id': int(time.time()),
                'title': self.video_info['title'],
                'subject': self.video_info['subject'],
                'stage': self.video_info['stage'],
                'grade': self.video_info['grade'],
                'term': self.video_info['term'],
                'description': self.video_info['description'],
                'original_path': self.video_path,
                'encrypted_path': os.path.join(videos_folder, f"video_{int(time.time())}.enc"),
                'upload_date': datetime.now().isoformat(),
                'max_views': self.video_info.get('max_views', 3),
                'duration_minutes': self.video_info.get('duration', 0),
                'has_exam': self.video_info.get('has_exam', True),
                'exam_questions': self.video_info.get('exam_questions', [])
            }
            
            # حفظ في ملف JSON
            videos_db_file = "videos_database.json"
            if os.path.exists(videos_db_file):
                with open(videos_db_file, 'r', encoding='utf-8') as f:
                    videos_db = json.load(f)
            else:
                videos_db = {'videos': []}
                
            videos_db['videos'].append(video_data)
            
            with open(videos_db_file, 'w', encoding='utf-8') as f:
                json.dump(videos_db, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ معلومات الفيديو: {e}")

class VideoPlayerDialog(QDialog):
    """نافذة تشغيل الفيديو المحمي"""
    
    def __init__(self, video_info, student_info):
        super().__init__()
        self.video_info = video_info
        self.student_info = student_info
        self.watch_count = self.get_watch_count()
        self.max_watches = 3
        
        self.init_ui()
        self.init_security()
        
    def init_ui(self):
        # زر تبديل الوضع الداكن
        self.dark_mode_enabled = False
        dark_mode_btn = QPushButton("🌙 وضع داكن")
        dark_mode_btn.setStyleSheet("font-size: 11px; padding: 4px; border-radius: 5px; background-color: #222; color: #fff;")
        dark_mode_btn.setFixedWidth(90)
        dark_mode_btn.clicked.connect(self.toggle_dark_mode)
        # إضافة زر الوضع الداكن أعلى التبويبات
        header_layout = QHBoxLayout()
        header_layout.addStretch()
        header_layout.addWidget(dark_mode_btn)
        main_layout.insertLayout(1, header_layout)
        """إعداد واجهة المشغل"""
        self.setWindowTitle(f"فيديو محمي: {self.video_info['title']}")
        self.setModal(True)
        self.setStyleSheet("""
            QDialog {
                background-color: #1a1a1a;
                color: white;
            }
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        layout = QVBoxLayout()
        
        # تحذير الحماية
        warning_frame = QFrame()
        warning_frame.setStyleSheet("background-color: #e74c3c; padding: 20px; border-radius: 10px;")
        warning_layout = QVBoxLayout()
        
        warning_title = QLabel("⚠️ تحذير أمني مهم")
        warning_title.setAlignment(Qt.AlignCenter)
        warning_title.setStyleSheet("font-size: 20px; font-weight: bold; color: white;")
        warning_layout.addWidget(warning_title)
        
        warning_text = QLabel(
            "🔒 هذا المحتوى محمي بحقوق الملكية الفكرية\n"
            "🚫 أي محاولة تسجيل أو نسخ ستؤدي لإغلاق النظام فوراً\n"
            "👁️ عدد المشاهدات المتبقية: " + str(self.max_watches - self.watch_count) + " من " + str(self.max_watches) + "\n"
            "📱 سيتم منع استخدام الهاتف أثناء المشاهدة\n"
            "🖥️ سيتم تشغيل الفيديو في وضع الشاشة الكاملة"
        )
        warning_text.setAlignment(Qt.AlignCenter)
        warning_text.setStyleSheet("font-size: 14px; color: white; line-height: 1.5;")
        warning_layout.addWidget(warning_text)
        
        warning_frame.setLayout(warning_layout)
        layout.addWidget(warning_frame)
        
        # معلومات الفيديو
        info_frame = QFrame()
        info_frame.setStyleSheet("background-color: #2c3e50; padding: 15px; border-radius: 8px;")
        info_layout = QVBoxLayout()
        
        video_title = QLabel(f"📹 {self.video_info['title']}")
        video_title.setStyleSheet("font-size: 18px; font-weight: bold;")
        info_layout.addWidget(video_title)
        
        video_details = QLabel(
            f"📚 المادة: {self.video_info['subject']} | "
            f"🎓 الصف: {self.video_info['grade']} | "
            f"📅 الترم: {self.video_info['term']}"
        )
        info_layout.addWidget(video_details)
        
        student_info_label = QLabel(f"👤 الطالب: {self.student_info['name']} ({self.student_info['code']})")
        info_layout.addWidget(student_info_label)
        
        info_frame.setLayout(info_layout)
        layout.addWidget(info_frame)
        
        # شروط المشاهدة
        conditions_frame = QFrame()
        conditions_frame.setStyleSheet("background-color: #34495e; padding: 15px; border-radius: 8px;")
        conditions_layout = QVBoxLayout()
        
        conditions_title = QLabel("📋 شروط المشاهدة:")
        conditions_title.setStyleSheet("font-size: 16px; font-weight: bold;")
        conditions_layout.addWidget(conditions_title)
        
        conditions_text = QLabel(
            "✅ يُسمح بـ 3 مشاهدات فقط لكل فيديو\n"
            "✅ بعد انتهاء الفيديو سيظهر الامتحان مباشرة\n"
            "✅ لا يمكن فتح الامتحان قبل انتهاء الفيديو\n"
            "✅ سيتم تسجيل وقت المشاهدة والنتائج\n"
            "✅ الفيديو سيُحذف تلقائياً بعد 3 مشاهدات\n"
            "❌ ممنوع تسجيل الشاشة أو أخذ لقطات\n"
            "❌ ممنوع استخدام الهاتف أثناء المشاهدة\n"
            "❌ ممنوع مشاركة المحتوى مع الآخرين"
        )
        conditions_text.setStyleSheet("font-size: 12px; line-height: 1.4;")
        conditions_layout.addWidget(conditions_text)
        
        conditions_frame.setLayout(conditions_layout)
        layout.addWidget(conditions_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        if self.watch_count >= self.max_watches:
            # انتهت المشاهدات
            expired_label = QLabel("❌ انتهت المشاهدات المسموحة")
            expired_label.setAlignment(Qt.AlignCenter)
            expired_label.setStyleSheet("font-size: 18px; color: #e74c3c; font-weight: bold;")
            layout.addWidget(expired_label)
            
            close_btn = QPushButton("إغلاق")
            close_btn.clicked.connect(self.reject)
            buttons_layout.addWidget(close_btn)
        else:
            # يمكن المشاهدة
            start_btn = QPushButton(f"▶️ بدء المشاهدة ({self.watch_count + 1}/{self.max_watches})")
            start_btn.clicked.connect(self.start_video)
            buttons_layout.addWidget(start_btn)
            
            cancel_btn = QPushButton("إلغاء")
            cancel_btn.clicked.connect(self.reject)
            buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        
    def init_security(self):
        """تهيئة الأمان"""
        # منع تغيير حجم النافذة
        self.setFixedSize(600, 500)
        
        # منع النقر بالزر الأيمن
        self.setContextMenuPolicy(Qt.NoContextMenu)
        
    def get_watch_count(self):
        """الحصول على عدد المشاهدات"""
        try:
            watch_file = f"watch_data_{self.student_info['id']}_{self.video_info['id']}.json"
            if os.path.exists(watch_file):
                with open(watch_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('watch_count', 0)
            return 0
        except:
            return 0
            
    def start_video(self):
        """بدء تشغيل الفيديو"""
        # تحديث عداد المشاهدات
        self.watch_count += 1
        self.save_watch_count()
        
        # إظهار رسالة بدء التشغيل
        QMessageBox.information(self, "بدء التشغيل", 
                               "سيتم تشغيل الفيديو في وضع الحماية الكاملة\n"
                               "الشاشة ستصبح كاملة ولن تتمكن من الخروج حتى انتهاء الفيديو\n"
                               "بعد انتهاء الفيديو سيظهر الامتحان مباشرة")
        
        # محاكاة تشغيل الفيديو
        self.simulate_video_playback()
        
    def save_watch_count(self):
        """حفظ عداد المشاهدات"""
        try:
            watch_data = {
                'student_id': self.student_info['id'],
                'video_id': self.video_info['id'],
                'watch_count': self.watch_count,
                'last_watch': datetime.now().isoformat()
            }
            
            watch_file = f"watch_data_{self.student_info['id']}_{self.video_info['id']}.json"
            with open(watch_file, 'w', encoding='utf-8') as f:
                json.dump(watch_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ عداد المشاهدات: {e}")
            
    def simulate_video_playback(self):
        """محاكاة تشغيل الفيديو"""
        # في التطبيق الحقيقي، هنا سيتم تشغيل الفيديو المشفر
        QMessageBox.information(self, "تشغيل الفيديو", 
                               f"تم تشغيل الفيديو: {self.video_info['title']}\n"
                               f"المشاهدة رقم: {self.watch_count}\n"
                               f"المشاهدات المتبقية: {self.max_watches - self.watch_count}")
        
        # بعد انتهاء الفيديو، عرض الامتحان
        self.show_exam()
        
    def show_exam(self):
        """عرض الامتحان"""
        exam_result = QMessageBox.question(self, "الامتحان", 
                                         f"انتهى الفيديو!\n"
                                         f"هل تريد بدء الامتحان الآن؟\n"
                                         f"الامتحان مرتبط بالفيديو ولا يمكن تأجيله",
                                         QMessageBox.Yes | QMessageBox.No)
        
        if exam_result == QMessageBox.Yes:
            # محاكاة الامتحان
            QMessageBox.information(self, "الامتحان", 
                                   "تم بدء الامتحان!\n"
                                   "سيتم عرض الأسئلة واحداً تلو الآخر\n"
                                   "النتيجة ستُرسل للمعلم تلقائياً")
            
        self.accept()

from PyQt5.QtWidgets import QSizePolicy
class VideosWindow(QWidget):
    # ==== دوال تليجرام الوهمية لتجنب الأخطاء ====
    def _create_telegram_bot_group(self):
        group = QGroupBox("🤖 إعدادات بوت التليجرام (تجريبي)")
        layout = QVBoxLayout()
        label = QLabel("(لم يتم تنفيذ هذه المجموعة بعد)")
        layout.addWidget(label)
        group.setLayout(layout)
        return group

    def _create_telegram_connection_group(self):
        group = QGroupBox("🔗 ربط الحساب بالتليجرام (تجريبي)")
        layout = QVBoxLayout()
        label = QLabel("(لم يتم تنفيذ هذه المجموعة بعد)")
        layout.addWidget(label)
        group.setLayout(layout)
        return group

    def _create_telegram_upload_group(self):
        group = QGroupBox("📤 رفع الفيديو للتليجرام (تجريبي)")
        layout = QVBoxLayout()
        label = QLabel("(لم يتم تنفيذ هذه المجموعة بعد)")
        layout.addWidget(label)
        group.setLayout(layout)
        return group

    def _create_telegram_log_group(self):
        group = QGroupBox("📝 سجل التليجرام")
        layout = QVBoxLayout()
        layout.addWidget(self.telegram_log)
        group.setLayout(layout)
        return group

    def load_telegram_settings(self):
        # دالة وهمية لتجنب الخطأ
        pass
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.student_model = Student(db_manager)
        # سجل التليجرام: عنصر واجهة لعرض السجل
        self.telegram_log = QListWidget()
        # إعداد النافذة مع تكبير تلقائي
        self.setup_responsive_window()
        # متغير لتجاهل أول استدعاء resizeEvent
        self.first_resize = True
        # بناء واجهة المستخدم
        self.init_ui()

    def append_telegram_log(self, message):
        """إضافة رسالة إلى سجل التليجرام في الواجهة"""
        if hasattr(self, 'telegram_log') and self.telegram_log is not None:
            self.telegram_log.addItem(str(message))
            self.telegram_log.scrollToBottom()

    def setup_responsive_window(self):
        """تهيئة أبعاد النافذة الأصلية للتكبير والتصغير"""
        # تعيين أبعاد افتراضية للنافذة
        self.original_width = 1200
        self.original_height = 800
        self.current_width = self.original_width
        self.current_height = self.original_height
        self.resize(self.original_width, self.original_height)
    def _create_telegram_info_frame(self):
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #3498db;
                border-radius: 16px;
                padding: 6px 6px 6px 6px;
                margin: 0px;
            }
            QLabel.telegram-label {
                color: #222;
                font-size: 20px;
                font-weight: bold;
                margin-left: 8px;
                margin-right: 8px;
            }
            QLineEdit.telegram-input {
                background: #fff;
                border: 2px solid #3498db;
                border-radius: 9px;
                font-size: 20px;
                padding: 10px 12px;
                margin-bottom: 10px;
                min-height: 44px;
            }
            QLabel, QCheckBox {
                color: #222;
                font-size: 15px;
            }
            QLineEdit {
                background: #fff;
                border: 1.5px solid #3498db;
                border-radius: 7px;
                font-size: 15px;
                padding: 7px;
                margin-bottom: 7px;
            }
            QPushButton {
                font-size: 17px;
                padding: 12px 28px;
                min-height: 44px;
                border-radius: 10px;
                background: #3498db;
                color: #fff;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #217dbb;
            }
        """)
        info_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Maximum)
        info_layout = QVBoxLayout()
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(8)
        info_title = QLabel("📱 الربط مع تليجرام")
        info_title.setStyleSheet("font-size: 28px; font-weight: bold; margin: 12px 0 24px 0; color: #f39c12;")
        info_title.setAlignment(Qt.AlignCenter)
        # حقول الإدخال
        self.bot_token_input = QLineEdit()
        self.bot_token_input.setPlaceholderText("توكن البوت ...")
        self.bot_token_input.setMinimumHeight(44)
        self.bot_token_input.setProperty("class", "telegram-input")
        self.bot_token_input.setObjectName("telegram-bot-token")
        self.bot_token_input.setStyleSheet(":host, .telegram-input { font-size: 20px; }")
        self.bot_token_input.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)

        self.channel_id_input = QLineEdit()
        self.channel_id_input.setPlaceholderText("معرف القناة أو الجروب ...")
        self.channel_id_input.setMinimumHeight(44)
        self.channel_id_input.setProperty("class", "telegram-input")
        self.channel_id_input.setObjectName("telegram-channel-id")
        self.channel_id_input.setStyleSheet(":host, .telegram-input { font-size: 20px; }")
        self.channel_id_input.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)

        self.bot_name_input = QLineEdit()
        self.bot_name_input.setPlaceholderText("اسم البوت ...")
        self.bot_name_input.setMinimumHeight(44)
        self.bot_name_input.setProperty("class", "telegram-input")
        self.bot_name_input.setObjectName("telegram-bot-name")
        self.bot_name_input.setStyleSheet(":host, .telegram-input { font-size: 20px; }")
        self.bot_name_input.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)

        self.telegram_group_link = QLineEdit()
        self.telegram_group_link.setPlaceholderText("رابط الجروب على تليجرام ...")
        self.telegram_group_link.setMinimumHeight(44)
        self.telegram_group_link.setProperty("class", "telegram-input")
        self.telegram_group_link.setObjectName("telegram-group-link")
        self.telegram_group_link.setStyleSheet(":host, .telegram-input { font-size: 20px; }")
        self.telegram_group_link.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        self.connection_status_label = QLabel("🔴 غير متصل")
        self.connection_status_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #e74c3c; margin: 8px 0;")
        # خيارات إضافية
        self.auto_upload_checkbox = QCheckBox("تفعيل الرفع التلقائي")
        self.auto_upload_checkbox.setStyleSheet("font-size: 15px; margin-bottom: 2px;")
        self.include_info_checkbox = QCheckBox("إضافة معلومات الفيديو")
        self.include_info_checkbox.setChecked(True)
        self.include_info_checkbox.setStyleSheet("font-size: 15px; margin-bottom: 2px;")
        self.include_exam_checkbox = QCheckBox("إضافة معلومات الامتحان")
        self.include_exam_checkbox.setStyleSheet("font-size: 15px; margin-bottom: 8px;")
        # زر اختبار الاتصال
        test_btn = QPushButton("اختبار الاتصال")
        test_btn.setMinimumHeight(44)
        test_btn.clicked.connect(self.test_telegram_connection)
        # تجميع
        info_layout.addWidget(info_title)
        # ترتيب الحقول بشكل واضح
        row1 = QHBoxLayout()
        label1 = QLabel("توكن البوت:")
        label1.setProperty("class", "telegram-label")
        row1.addWidget(label1)
        row1.addWidget(self.bot_token_input)
        row1.setStretch(0, 0)
        row1.setStretch(1, 1)
        info_layout.addLayout(row1)

        row2 = QHBoxLayout()
        label2 = QLabel("معرف القناة/الجروب:")
        label2.setProperty("class", "telegram-label")
        row2.addWidget(label2)
        row2.addWidget(self.channel_id_input)
        row2.setStretch(0, 0)
        row2.setStretch(1, 1)
        info_layout.addLayout(row2)

        row3 = QHBoxLayout()
        label3 = QLabel("اسم البوت:")
        label3.setProperty("class", "telegram-label")
        row3.addWidget(label3)
        row3.addWidget(self.bot_name_input)
        row3.setStretch(0, 0)
        row3.setStretch(1, 1)
        info_layout.addLayout(row3)

        row4 = QHBoxLayout()
        label4 = QLabel("رابط الجروب:")
        label4.setProperty("class", "telegram-label")
        row4.addWidget(label4)
        row4.addWidget(self.telegram_group_link)
        row4.setStretch(0, 0)
        row4.setStretch(1, 1)
        info_layout.addLayout(row4)
        # خيارات
        options_row = QHBoxLayout()
        options_row.addWidget(self.auto_upload_checkbox)
        options_row.addWidget(self.include_info_checkbox)
        options_row.addWidget(self.include_exam_checkbox)
        info_layout.addLayout(options_row)
        info_layout.addWidget(self.connection_status_label, alignment=Qt.AlignCenter)
        info_layout.addWidget(test_btn, alignment=Qt.AlignCenter)
        info_frame.setLayout(info_layout)
        return info_frame
    def toggle_dark_mode(self):
        """تبديل الوضع الداكن"""
        self.dark_mode_enabled = not self.dark_mode_enabled
        if self.dark_mode_enabled:
            dark_style = """
                QWidget { background-color: #222; color: #fff; }
                QPushButton { background-color: #444; color: #fff; }
                QPushButton:hover { background-color: #666; }
                QTabWidget::pane { background: #222; }
                QTabBar::tab { background: #444; color: #fff; }
                QTabBar::tab:selected { background: #666; color: #fff; }
                QTabBar::tab:hover { background: #555; }
                QGroupBox { background: #222; color: #fff; }
                QFrame { background: #222; color: #fff; }
                QLabel { color: #fff; }
                QLineEdit, QTextEdit, QComboBox { background: #333; color: #fff; }
                QTableWidget { background: #222; color: #fff; }
                QHeaderView::section { background: #444; color: #fff; }
                QCheckBox { color: #fff; }
            """
            self.setStyleSheet(dark_style)
        else:
            self.setStyleSheet("")

    def apply_font_sizes(self, title_size, subtitle_size, normal_size, button_size):
        """تطبيق أحجام الخطوط الجديدة"""
        # تحديث أحجام الخطوط في التبويبات
        tabs_style = f"""
            QTabWidget::pane {{
                border: 2px solid #3498db;
                background-color: white;
                border-radius: 10px;
                margin: 8px;
            }}
            QTabBar::tab {{
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 6px 10px;
                margin-right: 3px;
                font-weight: bold;
                min-width: 70px;
                min-height: 24px;
            }}
            QTabBar::tab:selected {{
                background-color: #3498db;
                color: white;
            }}
            QTabBar::tab:hover {{
                background-color: #bdc3c7;
            }}
        """
        # تطبيق النمط على التبويبات
        if hasattr(self, 'tabs'):
            self.tabs.setStyleSheet(tabs_style)

        # تحديث أحجام الخطوط في العناوين
        title_style = f"""
            QLabel[class="title"] {{
                font-size: {title_size}px;
                font-weight: bold;
            }}
        """
        subtitle_style = f"""
            QLabel[class="subtitle"] {{
                font-size: {subtitle_size}px;
            }}
        """
        normal_style = f"""
            QLabel[class="normal"] {{
                font-size: {normal_size}px;
            }}
        """
        # تطبيق الأنماط على النافذة
        self.setStyleSheet(self.styleSheet() + title_style + subtitle_style + normal_style)

    def reset_default_styles(self):
        """إعادة تعيين ستايل التبويبات للحجم الطبيعي الافتراضي فقط"""
        if hasattr(self, 'tabs'):
            self.tabs.setStyleSheet("""
                QTabWidget::pane {
                    border: 2px solid #3498db;
                    background-color: white;
                    border-radius: 10px;
                    margin: 8px;
                }
                QTabBar::tab {
                    background-color: #ecf0f1;
                    color: #2c3e50;
                    padding: 6px 10px;
                    margin-right: 3px;
                    border-radius: 8px 8px 0 0;
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 70px;
                    min-height: 24px;
                }
                QTabBar::tab:selected {
                    background-color: #3498db;
                    color: white;
                }
                QTabBar::tab:hover {
                    background-color: #bdc3c7;
                }
            """)

    def on_resize_event(self, event):
        """معالج حدث تغيير حجم النافذة"""
        new_width = event.size().width()
        new_height = event.size().height()

        # تجاهل أول استدعاء (أول رسم للنافذة)
        if hasattr(self, 'first_resize') and self.first_resize:
            self.first_resize = False
            self.current_width = new_width
            self.current_height = new_height
            self.reset_default_styles()
            super().resizeEvent(event)
            return

        # إذا كان الحجم الأصلي، أعد تعيين ستايل التبويبات فقط
        if (new_width == getattr(self, 'original_width', new_width)) and (new_height == getattr(self, 'original_height', new_height)):
            self.reset_default_styles()
            # لا تطبق أي تصغير أو تكبير
        else:
            # تصغير أو تكبير حسب النسبة فقط إذا تغيّر الحجم فعليًا
            width_ratio = new_width / self.original_width if self.original_width > 0 else 1
            height_ratio = new_height / self.original_height if self.original_height > 0 else 1
            self.update_font_sizes(width_ratio, height_ratio)
            self.update_element_sizes(width_ratio, height_ratio)

        # حفظ الحجم الجديد
        self.current_width = new_width
        self.current_height = new_height

        # استدعاء المعالج الأصلي
        super().resizeEvent(event)
        
        # تطبيق النمط على التبويبات
        if hasattr(self, 'tabs'):
            self.tabs.setStyleSheet(tabs_style)
        
        # تحديث أحجام الخطوط في العناوين
        title_style = f"""
            QLabel[class="title"] {{
                font-size: {title_size}px;
                font-weight: bold;
            }}
        """
        
        subtitle_style = f"""
            QLabel[class="subtitle"] {{
                
                
                font-size: {subtitle_size}px;
            }}
        """
        
        normal_style = f"""
            QLabel[class="normal"] {{
                font-size: {normal_size}px;
            }}
        """
        
        # تطبيق الأنماط على النافذة
        self.setStyleSheet(self.styleSheet() + title_style + subtitle_style + normal_style)

    def apply_element_sizes(self, padding, button_height, input_height):
        """تطبيق الأحجام الجديدة للعناصر"""
        # تحديث أحجام الأزرار
        button_style = f"""
            QPushButton {{
                padding: {padding}px;
                min-height: {button_height}px;
                border-radius: 6px;
                font-weight: bold;
            }}
        """

        # تحديث أحجام حقول الإدخال
        input_style = f"""
        self.tabs = QTabWidget()
        # يمكنك هنا إضافة setStyleSheet الصحيح إذا أردت تخصيص التبويبات
        # self.tabs.setStyleSheet("""
        #     QTabWidget::pane {
        #         border: 2px solid #3498db;
        #         background-color: white;
        #         border-radius: 10px;
        #         margin: 8px;
        #     }
        #     QTabBar::tab {
        #         background-color: #ecf0f1;
        #         color: #2c3e50;
        #         padding: 6px 10px;
        #         margin-right: 3px;
        #         border-radius: 8px 8px 0 0;
        #         font-size: 12px;
        #         font-weight: bold;
        #         min-width: 70px;
        #         min-height: 24px;
        #     }
        #     QTabBar::tab:selected {
        #         background-color: #3498db;
        #         color: white;
        #     }
        #     QTabBar::tab:hover {
        #         background-color: #bdc3c7;
        #     }
        # """)

        # ...تابع الكود الطبيعي لبناء الواجهة...
        # يجب أن يكون الكود التالي داخل دالة keyPressEvent وليس خارجها

    def wheelEvent(self, event):
        """معالج حدث عجلة الماوس للتكبير والتصغير"""
        # Ctrl + Mouse Wheel للتكبير/التصغير
        if event.modifiers() == Qt.ControlModifier:
            delta = event.angleDelta().y()
            if delta > 0:
                self.zoom_in()
            else:
                self.zoom_out()
            event.accept()
        else:
            # استدعاء المعالج الأصلي إذا لم يكن Ctrl مضغوط
            super().wheelEvent(event)
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة الفيديوهات التعليمية المحمية")
        self.setGeometry(100, 100, 1200, 800)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # العنوان
        # ======= استرجاع الكود القديم قبل زر الوضع الداكن =======
        self.setWindowTitle("إدارة الفيديوهات التعليمية المحمية")
        self.setGeometry(100, 100, 1200, 800)
        main_layout = QVBoxLayout()
        # العنوان
        header_frame = QFrame()
        header_layout = QVBoxLayout()
        channel_title = QLabel("قناة نظام طلاب نظام إدارة الطلاب المتطور – دروس، امتحانات، ومتابعة مستمرة.")
        channel_title.setAlignment(Qt.AlignCenter)
        channel_title.setStyleSheet("font-size: 16px; color: #f39c12; font-weight: bold; margin: 10px 0;")
        header_layout.addWidget(channel_title)
        subtitle = QLabel("حماية متقدمة • امتحانات تلقائية • تتبع المشاهدات")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("font-size: 14px; color: #bdc3c7; margin: 8px;")
        header_layout.addWidget(subtitle)
        header_frame.setLayout(header_layout)
        main_layout.addWidget(header_frame)
        # التبويبات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #3498db;
                background-color: white;
                border-radius: 10px;
                margin: 8px;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 6px 10px;
                margin-right: 3px;
                border-radius: 8px 8px 0 0;
                font-size: 12px;
                font-weight: bold;
                min-width: 70px;
                min-height: 24px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #bdc3c7;
            }
        """)
        upload_tab = self.create_upload_tab()
        self.tabs.addTab(upload_tab, "📤 رفع فيديو جديد")
        manage_tab = self.create_manage_tab()
        self.tabs.addTab(manage_tab, "🗂️ إدارة الفيديوهات")
        students_tab = self.create_students_tab()
        self.tabs.addTab(students_tab, "👥 الطلاب")
        stats_tab = self.create_stats_tab()
        self.tabs.addTab(stats_tab, "📊 الإحصائيات")
        telegram_tab = self.create_telegram_tab()
        self.tabs.addTab(telegram_tab, "🤖 تليجرام")
        main_layout.addWidget(self.tabs)
        self.setLayout(main_layout)

    def toggle_dark_mode(self):
        """تبديل الوضع الداكن"""
        self.dark_mode_enabled = not self.dark_mode_enabled
        if self.dark_mode_enabled:
            dark_style = """
                QWidget { background-color: #18191a; color: #e4e6eb; }
                QTabWidget::pane { background: #242526; border: 2px solid #444; }
                QTabBar::tab { background: #242526; color: #e4e6eb; }
                QTabBar::tab:selected { background: #3a3b3c; color: #fff; }
                QTabBar::tab:hover { background: #444; }
                QGroupBox { background: #23272b; color: #e4e6eb; border: 1px solid #444; }
                QFrame { background: #23272b; color: #e4e6eb; }
                QLabel { color: #e4e6eb; }
                QLineEdit, QTextEdit, QComboBox { background: #18191a; color: #e4e6eb; border: 1px solid #444; }
                QPushButton { background: #444; color: #fff; border-radius: 5px; }
                QPushButton:hover { background: #666; }
                QTableWidget { background: #23272b; color: #e4e6eb; }
                QHeaderView::section { background: #444; color: #fff; }
                QCheckBox { color: #e4e6eb; }
            """
            self.setStyleSheet(dark_style)
        else:
            self.setStyleSheet("")
        
    def create_header(self):
        """إنشاء رأس النافذة"""
        frame = QFrame()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #3498db;
                background-color: white;
                border-radius: 10px;
                margin: 8px;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 15px 20px;
                margin-right: 5px;
                border-radius: 8px 8px 0 0;
                font-size: 16px;
                font-weight: bold;
                min-width: 140px;
                min-height: 40px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #bdc3c7;
            }
        """)
        channel_title.setStyleSheet("font-size: 16px; color: #f39c12; font-weight: bold; margin: 10px 0;")
        channel_title.setAlignment(Qt.AlignCenter)
        channel_title.setProperty("class", "subtitle")
        layout.addWidget(channel_title)
        
        subtitle = QLabel("حماية متقدمة • امتحانات تلقائية • تتبع المشاهدات")
        subtitle.setStyleSheet("font-size: 14px; color: #bdc3c7; margin: 8px;")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setProperty("class", "normal")
        layout.addWidget(subtitle)
        
        # إزالة أزرار التكبير/التصغير اليدوية من الواجهة
    def showMaximized(self):
        super().showMaximized()
        # عند التكبير: اجعل النافذة تأخذ كل الشاشة وتعدل العناصر تلقائياً
        screen = QApplication.primaryScreen().geometry()
        self.resize(screen.width(), screen.height())
        self.current_width = screen.width()
        self.current_height = screen.height()
        self.update_font_sizes(1, 1)
        self.update_element_sizes(1, 1)

    def showNormal(self):
        super().showNormal()
        # عند العودة للحجم الطبيعي: أعد الحجم الافتراضي
        self.setup_responsive_window()
        channel_title = QLabel("قناة نظام طلاب نظام إدارة الطلاب المتطور – دروس، امتحانات، ومتابعة مستمرة.")
        channel_title.setAlignment(Qt.AlignCenter)
        channel_title.setStyleSheet("font-size: 16px; color: #f39c12; font-weight: bold; margin: 10px 0;")
        layout.addWidget(channel_title)
        
        subtitle = QLabel("حماية متقدمة • امتحانات تلقائية • تتبع المشاهدات")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("font-size: 14px; color: #bdc3c7; margin: 8px;")
        layout.addWidget(subtitle)
        
        frame.setLayout(layout)
        return frame

    def create_upload_tab(self):
        """إنشاء تبويب رفع الفيديوهات"""
        widget = QWidget()
        layout = QVBoxLayout()

        # نموذج رفع الفيديو
        upload_group = QGroupBox("📤 رفع فيديو تعليمي جديد")
        upload_group.setStyleSheet("font-size: 12px; font-weight: bold; padding: 5px;")
        upload_layout = QFormLayout()
        upload_layout.setSpacing(6)

        # عنوان الفيديو
        self.video_title_input = QLineEdit()
        self.video_title_input.setPlaceholderText("مثال: الجغرافيا - الدرس الأول")
        self.video_title_input.setStyleSheet("font-size: 11px; padding: 3px; height: 20px;")
        upload_layout.addRow("عنوان الفيديو:", self.video_title_input)

        # المادة
        self.subject_combo = QComboBox()
        self.subject_combo.addItems(["جغرافيا", "تاريخ", "علوم", "رياضيات", "لغة عربية", "لغة إنجليزية"])
        self.subject_combo.setStyleSheet("font-size: 11px; padding: 3px; height: 20px;")
        upload_layout.addRow("المادة:", self.subject_combo)

        # المرحلة الدراسية
        self.stage_combo = QComboBox()
        self.stage_combo.addItems(["الابتدائية", "الإعدادية", "الثانوية"])
        self.stage_combo.setStyleSheet("font-size: 11px; padding: 3px; height: 20px;")
        upload_layout.addRow("المرحلة:", self.stage_combo)

        # الصف الدراسي
        self.grade_combo = QComboBox()
        self.grade_combo.addItems(["الأول", "الثاني", "الثالث", "الرابع", "الخامس", "السادس"])
        self.grade_combo.setStyleSheet("font-size: 11px; padding: 3px; height: 20px;")
        upload_layout.addRow("الصف:", self.grade_combo)

        # الترم
        self.term_combo = QComboBox()
        self.term_combo.addItems(["الترم الأول", "الترم الثاني"])
        self.term_combo.setStyleSheet("font-size: 11px; padding: 3px; height: 20px;")
        upload_layout.addRow("الترم:", self.term_combo)

        # وصف الفيديو
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(80)
        self.description_input.setPlaceholderText("وصف مختصر للفيديو...")
        self.description_input.setStyleSheet("font-size: 11px; padding: 3px; height: 20px;")
        upload_layout.addRow("الوصف:", self.description_input)

        # مدة الفيديو (تقديرية)
        self.duration_input = QSpinBox()
        self.duration_input.setRange(1, 180)
        self.duration_input.setValue(30)
        self.duration_input.setSuffix(" دقيقة")
        self.duration_input.setStyleSheet("font-size: 11px; padding: 3px; height: 20px;")
        upload_layout.addRow("المدة المتوقعة:", self.duration_input)

        upload_group.setLayout(upload_layout)
        layout.addWidget(upload_group)

        # اختيار ملف الفيديو
        file_group = QGroupBox("📁 اختيار ملف الفيديو")
        file_group.setStyleSheet("font-size: 12px; font-weight: bold; padding: 5px;")
        file_layout = QVBoxLayout()

        file_select_layout = QHBoxLayout()
        self.file_path_label = QLabel("لم يتم اختيار ملف")
        self.file_path_label.setStyleSheet("color: #7f8c8d; font-style: italic; font-size: 11px; padding: 3px;")

        select_file_btn = QPushButton("📁 اختيار فيديو")
        select_file_btn.clicked.connect(self.select_video_file)
        select_file_btn.setStyleSheet("font-size: 11px; padding: 5px; font-weight: bold; border-radius: 5px;")

        file_select_layout.addWidget(self.file_path_label)
        file_select_layout.addWidget(select_file_btn)
        file_layout.addLayout(file_select_layout)

        # شريط التقدم
        self.upload_progress = QProgressBar()
        self.upload_progress.setVisible(False)
        self.upload_progress.setStyleSheet("font-size: 10px; height: 10px;")
        file_layout.addWidget(self.upload_progress)

        file_group.setLayout(file_layout)
        layout.addWidget(file_group)

        # إعدادات الحماية
        security_group = QGroupBox("🔒 إعدادات الحماية والامتحان")
        security_group.setStyleSheet("font-size: 12px; font-weight: bold; padding: 5px;")
        security_layout = QFormLayout()
        security_layout.setSpacing(6)

        # عدد المشاهدات المسموحة
        self.max_views_input = QSpinBox()
        self.max_views_input.setRange(1, 10)
        self.max_views_input.setValue(3)
        self.max_views_input.setStyleSheet("font-size: 11px; padding: 3px; height: 20px;")
        security_layout.addRow("عدد المشاهدات المسموحة:", self.max_views_input)

        # هل يوجد امتحان
        self.has_exam_checkbox = QCheckBox("يوجد امتحان مرتبط بالفيديو")
        self.has_exam_checkbox.setChecked(True)
        self.has_exam_checkbox.setStyleSheet("font-size: 10px; font-weight: bold; margin: 3px;")
        security_layout.addRow("", self.has_exam_checkbox)

        # عدد أسئلة الامتحان
        self.exam_questions_input = QSpinBox()
        self.exam_questions_input.setRange(5, 50)
        self.exam_questions_input.setValue(10)
        self.exam_questions_input.setStyleSheet("font-size: 11px; padding: 3px; height: 20px;")
        security_layout.addRow("عدد أسئلة الامتحان:", self.exam_questions_input)

        # زر إنشاء أسئلة الامتحان
        self.create_questions_btn = QPushButton("📝 إنشاء أسئلة الامتحان")
        self.create_questions_btn.clicked.connect(self.create_exam_questions)
        self.create_questions_btn.setStyleSheet("font-size: 11px; padding: 5px; font-weight: bold; border-radius: 5px;")
        security_layout.addRow("", self.create_questions_btn)

        security_group.setLayout(security_layout)
        layout.addWidget(security_group)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        self.upload_btn = QPushButton("🚀 رفع وتشفير الفيديو")
        self.upload_btn.clicked.connect(self.upload_video)
        self.upload_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 5px;
                font-size: 11px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        clear_btn = QPushButton("🗑️ مسح النموذج")
        clear_btn.clicked.connect(self.clear_upload_form)
        clear_btn.setStyleSheet("font-size: 11px; padding: 5px; font-weight: bold; border-radius: 5px;")

        buttons_layout.addWidget(self.upload_btn)
        buttons_layout.addWidget(clear_btn)
        layout.addLayout(buttons_layout)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_manage_tab(self):
        """إنشاء تبويب إدارة الفيديوهات"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان التبويب
        title_label = QLabel("🗂️ إدارة الفيديوهات التعليمية")
        title_label.setStyleSheet("font-size: 13px; font-weight: bold; color: #2c3e50; margin: 5px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # شريط البحث والفلترة
        search_frame = QFrame()
        search_frame.setStyleSheet("background-color: #f8f9fa; padding: 5px; border-radius: 8px; margin: 3px;")
        search_layout = QHBoxLayout()

        search_input = QLineEdit()
        search_input.setPlaceholderText("البحث في الفيديوهات...")
        search_input.setStyleSheet("font-size: 11px; padding: 3px; border-radius: 5px;")
        search_layout.addWidget(QLabel("البحث:"))
        search_layout.addWidget(search_input)

        filter_combo = QComboBox()
        filter_combo.addItems(["جميع المواد", "جغرافيا", "تاريخ", "علوم", "رياضيات"])
        filter_combo.setStyleSheet("font-size: 11px; padding: 3px; border-radius: 5px;")
        search_layout.addWidget(QLabel("المادة:"))
        search_layout.addWidget(filter_combo)

        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 5px;
                border-radius: 5px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        refresh_btn.clicked.connect(self.load_videos)
        search_layout.addWidget(refresh_btn)

        search_frame.setLayout(search_layout)
        layout.addWidget(search_frame)

        # جدول الفيديوهات
        self.videos_table = QTableWidget()
        self.videos_table.setColumnCount(8)
        self.videos_table.setHorizontalHeaderLabels([
            "العنوان", "المادة", "الصف", "الترم", "تاريخ الرفع",
            "المشاهدات", "الحالة", "الإجراءات"
        ])

        # تخصيص الجدول
        header = self.videos_table.horizontalHeader()
        header.setStretchLastSection(True)
        self.videos_table.setAlternatingRowColors(True)
        self.videos_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.videos_table.setStyleSheet("""
            QTableWidget {
                font-size: 10px;
                gridline-color: #bdc3c7;
            }
            QHeaderView::section {
                background-color: #3498db;
                color: white;
                padding: 5px;
                font-weight: bold;
                border: 1px solid #2980b9;
            }
        """)

        layout.addWidget(self.videos_table)

        # تحميل الفيديوهات عند إنشاء التبويب
        self.load_videos()

        widget.setLayout(layout)
        return widget

    def load_videos_for_students(self):
        """تحميل الفيديوهات لتبويب الطلاب"""
        try:
            # تحميل من ملف JSON
            videos_db_file = "videos_database.json"
            if os.path.exists(videos_db_file):
                with open(videos_db_file, 'r', encoding='utf-8') as f:
                    videos_db = json.load(f)
                    videos = videos_db.get('videos', [])
            else:
                videos = []

            # تحديث قائمة الفيديوهات
            self.video_select_combo.clear()
            self.video_select_combo.addItem("اختر فيديو...")
            
            for video in videos:
                self.video_select_combo.addItem(f"{video['title']} - {video['subject']}")

            # تحميل قائمة الطلاب
            self.load_students_list()

        except Exception as e:
            print(f"خطأ في تحميل الفيديوهات للطلاب: {e}")

    def load_students_list(self):
        """تحميل قائمة الطلاب"""
        try:
            # محاكاة قائمة الطلاب
            students = [
                "أحمد محمد - الصف الأول",
                "فاطمة علي - الصف الأول", 
                "محمد أحمد - الصف الثاني",
                "عائشة حسن - الصف الثاني",
                "علي محمود - الصف الثالث"
            ]
            
            self.students_list.clear()
            for student in students:
                self.students_list.addItem(student)

        except Exception as e:
            print(f"خطأ في تحميل قائمة الطلاب: {e}")

    def create_students_tab(self):
        """إنشاء تبويب مشاهدة الطلاب"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان التبويب
        title_label = QLabel("👥 إدارة مشاهدة الطلاب للفيديوهات")
        title_label.setStyleSheet("font-size: 13px; font-weight: bold; color: #2c3e50; margin: 5px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # اختيار الفيديو
        video_select_frame = QFrame()
        video_select_frame.setStyleSheet("background-color: #f8f9fa; padding: 5px; border-radius: 8px; margin: 3px;")
        video_select_layout = QHBoxLayout()

        self.video_select_combo = QComboBox()
        self.video_select_combo.addItem("اختر فيديو...")
        self.video_select_combo.setStyleSheet("font-size: 11px; padding: 3px; border-radius: 5px; min-width: 110px;")
        video_select_layout.addWidget(QLabel("الفيديو:"))
        video_select_layout.addWidget(self.video_select_combo)

        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 5px;
                border-radius: 5px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        refresh_btn.clicked.connect(self.load_videos_for_students)
        video_select_layout.addWidget(refresh_btn)

        video_select_frame.setLayout(video_select_layout)
        layout.addWidget(video_select_frame)

        # قائمة الطلاب
        students_group = QGroupBox("👥 الطلاب المسجلين")
        students_group.setStyleSheet("font-size: 12px; font-weight: bold; padding: 5px;")
        students_layout = QVBoxLayout()

        self.students_list = QListWidget()
        self.students_list.setStyleSheet("font-size: 11px; padding: 3px;")
        students_layout.addWidget(self.students_list)

        # أزرار إدارة الطلاب
        student_buttons_layout = QHBoxLayout()
        student_buttons_layout.setSpacing(10)

        assign_btn = QPushButton("➕ إضافة طالب للفيديو")
        assign_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 5px;
                border-radius: 5px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        assign_btn.clicked.connect(self.assign_student_to_video)

        remove_btn = QPushButton("➖ إزالة طالب من الفيديو")
        remove_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 5px;
                border-radius: 5px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        watch_btn = QPushButton("👁️ مشاهدة كطالب")
        watch_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                padding: 5px;
                border-radius: 5px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        watch_btn.clicked.connect(self.watch_as_student)

        student_buttons_layout.addWidget(assign_btn)
        student_buttons_layout.addWidget(remove_btn)
        student_buttons_layout.addWidget(watch_btn)

        students_layout.addLayout(student_buttons_layout)
        students_group.setLayout(students_layout)
        layout.addWidget(students_group)

        widget.setLayout(layout)
        return widget

    def create_stats_tab(self):
        """إنشاء تبويب الإحصائيات"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان التبويب
        title_label = QLabel("📊 إحصائيات النظام التعليمي")
        title_label.setStyleSheet("font-size: 13px; font-weight: bold; color: #2c3e50; margin: 5px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # إحصائيات عامة
        stats_frame = QFrame()
        stats_frame.setStyleSheet("background-color: #ecf0f1; padding: 5px; border-radius: 8px; margin: 3px;")
        stats_layout = QVBoxLayout()

        stats_title = QLabel("📊 إحصائيات النظام")
        stats_title.setStyleSheet("font-size: 12px; font-weight: bold; color: #2c3e50; margin-bottom: 5px;")
        stats_layout.addWidget(stats_title)

        # الإحصائيات
        self.total_videos_label = QLabel("📹 إجمالي الفيديوهات: 0")
        self.total_videos_label.setStyleSheet("font-size: 11px; margin: 3px; padding: 5px; background-color: white; border-radius: 4px;")
        self.total_views_label = QLabel("👁️ إجمالي المشاهدات: 0")
        self.total_views_label.setStyleSheet("font-size: 11px; margin: 3px; padding: 5px; background-color: white; border-radius: 4px;")
        self.active_students_label = QLabel("👥 الطلاب النشطين: 0")
        self.active_students_label.setStyleSheet("font-size: 11px; margin: 3px; padding: 5px; background-color: white; border-radius: 4px;")
        self.completed_exams_label = QLabel("📝 الامتحانات المكتملة: 0")
        self.completed_exams_label.setStyleSheet("font-size: 11px; margin: 3px; padding: 5px; background-color: white; border-radius: 4px;")

        stats_layout.addWidget(self.total_videos_label)
        stats_layout.addWidget(self.total_views_label)
        stats_layout.addWidget(self.active_students_label)
        stats_layout.addWidget(self.completed_exams_label)

        stats_frame.setLayout(stats_layout)
        layout.addWidget(stats_frame)

        # تقارير مفصلة
        reports_group = QGroupBox("📑 التقارير المفصلة")
        reports_group.setStyleSheet("font-size: 12px; font-weight: bold; padding: 5px; margin: 3px;")
        reports_layout = QVBoxLayout()

        reports_buttons_layout = QHBoxLayout()
        reports_buttons_layout.setSpacing(6)

        video_report_btn = QPushButton("📊 تقرير الفيديوهات")
        video_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 5px;
                border-radius: 5px;
                font-size: 11px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        student_report_btn = QPushButton("👥 تقرير الطلاب")
        student_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 5px;
                border-radius: 5px;
                font-size: 11px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        exam_report_btn = QPushButton("📝 تقرير الامتحانات")
        exam_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                padding: 5px;
                border-radius: 5px;
                font-size: 11px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)

        reports_buttons_layout.addWidget(video_report_btn)
        reports_buttons_layout.addWidget(student_report_btn)
        reports_buttons_layout.addWidget(exam_report_btn)

        reports_layout.addLayout(reports_buttons_layout)
        reports_group.setLayout(reports_layout)
        layout.addWidget(reports_group)

        # تحديث الإحصائيات عند إنشاء التبويب
        self.update_statistics()

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_telegram_tab(self):
        """إنشاء تبويب الربط بتليجرام"""
        widget = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(self._create_telegram_info_frame())
        layout.addWidget(self._create_telegram_bot_group())
        layout.addWidget(self._create_telegram_connection_group())
        layout.addWidget(self._create_telegram_upload_group())
        layout.addWidget(self._create_telegram_log_group())
        self.load_telegram_settings()
        widget.setLayout(layout)
        return widget

    def select_video_file(self):
        """اختيار ملف الفيديو"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختيار ملف فيديو",
            "",
            "Video Files (*.mp4 *.avi *.mkv *.mov *.wmv);;All Files (*)"
        )

        if file_path:
            self.file_path_label.setText(os.path.basename(file_path))
            self.file_path_label.setStyleSheet("color: #27ae60; font-weight: bold;")
            self.selected_video_path = file_path

    def upload_video(self):
        """رفع وتشفير الفيديو"""
        # التحقق من البيانات
        if not self.validate_upload_form():
            return

        # جمع بيانات الفيديو
        video_info = {
            'title': self.video_title_input.text().strip(),
            'subject': self.subject_combo.currentText(),
            'stage': self.stage_combo.currentText(),
            'grade': self.grade_combo.currentText(),
            'term': self.term_combo.currentText(),
            'description': self.description_input.toPlainText().strip(),
            'duration': self.duration_input.value(),
            'max_views': self.max_views_input.value(),
            'has_exam': self.has_exam_checkbox.isChecked(),
            'exam_questions_count': self.exam_questions_input.value()
        }

        # حفظ معلومات الفيديو للرفع على التليجرام
        self.last_uploaded_video_info = video_info.copy()

        # بدء عملية الرفع
        self.upload_progress.setVisible(True)
        self.upload_btn.setEnabled(False)
        self.show_loader("جاري رفع الفيديو...")

        # إنشاء خيط الرفع
        self.upload_thread = VideoUploadThread(self.selected_video_path, video_info)
        self.upload_thread.progress_updated.connect(self.upload_progress.setValue)
        self.upload_thread.upload_finished.connect(self.on_upload_finished)
        self.upload_thread.start()

    def validate_upload_form(self):
        """التحقق من صحة نموذج الرفع"""
        if not self.video_title_input.text().strip():
            show_error_message(self, "خطأ", "يرجى إدخال عنوان الفيديو")
            return False

        if not hasattr(self, 'selected_video_path'):
            show_error_message(self, "خطأ", "يرجى اختيار ملف الفيديو")
            return False

        if not os.path.exists(self.selected_video_path):
            show_error_message(self, "خطأ", "ملف الفيديو غير موجود")
            return False

        return True

    def on_upload_finished(self, success, message):
        """عند انتهاء الرفع"""
        self.upload_progress.setVisible(False)
        self.upload_btn.setEnabled(True)
        self.hide_loader()
    def show_loader(self, message="جاري التحميل..."):
        if not hasattr(self, '_loader_dialog'):
            self._loader_dialog = QDialog(self)
            self._loader_dialog.setModal(True)
            self._loader_dialog.setWindowTitle("")
            self._loader_dialog.setWindowFlags(self._loader_dialog.windowFlags() | Qt.FramelessWindowHint)
            layout = QVBoxLayout()
            label = QLabel(message)
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("font-size: 13px; font-weight: bold; padding: 12px;")
            layout.addWidget(label)
            self._loader_dialog.setLayout(layout)
            self._loader_dialog.setFixedSize(220, 70)
        else:
            self._loader_dialog.layout().itemAt(0).widget().setText(message)
        self._loader_dialog.show()
        QApplication.processEvents()

    def hide_loader(self):
        if hasattr(self, '_loader_dialog'):
            self._loader_dialog.hide()

        if success:
            show_success_message(self, "نجح", message)
            
            # رفع الفيديو على جروب التليجرام تلقائياً
            if hasattr(self, 'last_uploaded_video_info'):
                self.upload_video_to_telegram(self.last_uploaded_video_info)
                delattr(self, 'last_uploaded_video_info')
            
            self.clear_upload_form()
            self.load_videos()
        else:
            show_error_message(self, "خطأ", message)

    def clear_upload_form(self):
        """مسح نموذج الرفع"""
        self.video_title_input.clear()
        self.description_input.clear()
        self.file_path_label.setText("لم يتم اختيار ملف")
        self.file_path_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        if hasattr(self, 'selected_video_path'):
            delattr(self, 'selected_video_path')

    def load_videos(self):
        """تحميل قائمة الفيديوهات"""
        try:
            # تحميل من ملف JSON
            videos_db_file = "videos_database.json"
            if os.path.exists(videos_db_file):
                with open(videos_db_file, 'r', encoding='utf-8') as f:
                    videos_db = json.load(f)
                    videos = videos_db.get('videos', [])
            else:
                videos = []

            # تحديث الجدول
            self.videos_table.setRowCount(len(videos))

            for row, video in enumerate(videos):
                self.videos_table.setItem(row, 0, QTableWidgetItem(video['title']))
                self.videos_table.setItem(row, 1, QTableWidgetItem(video['subject']))
                self.videos_table.setItem(row, 2, QTableWidgetItem(video['grade']))
                self.videos_table.setItem(row, 3, QTableWidgetItem(video['term']))

                upload_date = datetime.fromisoformat(video['upload_date']).strftime("%Y-%m-%d")
                self.videos_table.setItem(row, 4, QTableWidgetItem(upload_date))

                # حساب المشاهدات
                views_count = self.get_video_views_count(video['id'])
                self.videos_table.setItem(row, 5, QTableWidgetItem(str(views_count)))

                # حالة الفيديو
                status = "نشط" if views_count < video['max_views'] else "منتهي"
                self.videos_table.setItem(row, 6, QTableWidgetItem(status))

                # أزرار الإجراءات
                actions_widget = self.create_video_actions_widget(video)
                self.videos_table.setCellWidget(row, 7, actions_widget)

            # تحديث الإحصائيات
            self.update_statistics()

        except Exception as e:
            print(f"خطأ في تحميل الفيديوهات: {e}")

    def get_video_views_count(self, video_id):
        """حساب عدد مشاهدات الفيديو"""
        try:
            views_count = 0
            # البحث في ملفات المشاهدة
            for filename in os.listdir('.'):
                if filename.startswith('watch_data_') and filename.endswith('.json'):
                    try:
                        with open(filename, 'r', encoding='utf-8') as f:
                            watch_data = json.load(f)
                            if watch_data.get('video_id') == video_id:
                                views_count += watch_data.get('watch_count', 0)
                    except:
                        continue
            return views_count
        except:
            return 0

    def create_video_actions_widget(self, video):
        """إنشاء أزرار إجراءات الفيديو"""
        widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)

        # زر المعاينة
        preview_btn = QPushButton("👁️")
        preview_btn.setToolTip("معاينة الفيديو")
        preview_btn.clicked.connect(lambda: self.preview_video(video))

        # زر التعديل
        edit_btn = QPushButton("✏️")
        edit_btn.setToolTip("تعديل معلومات الفيديو")

        # زر الحذف مع رسالة تأكيد ودعم Undo
        delete_btn = QPushButton("🗑️")
        delete_btn.setToolTip("حذف الفيديو")
        delete_btn.setStyleSheet("QPushButton { color: #e74c3c; }")
        def handle_delete():
            reply = QMessageBox.question(self, "تأكيد الحذف", f"هل أنت متأكد أنك تريد حذف الفيديو '{video['title']}'؟\nيمكنك التراجع بعد الحذف مباشرة.",
                                        QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                self._last_deleted_video = video.copy()
                self._last_deleted_video_row = None
                # حذف الفيديو من قاعدة البيانات (نفذ الكود الفعلي هنا)
                # ...
                QMessageBox.information(self, "تم الحذف", "تم حذف الفيديو. يمكنك التراجع (Undo) من زر التراجع في الأعلى.")
                self.show_undo_delete_button()
        delete_btn.clicked.connect(handle_delete)

        layout.addWidget(preview_btn)
        layout.addWidget(edit_btn)
        layout.addWidget(delete_btn)

        widget.setLayout(layout)
        return widget

    def show_undo_delete_button(self):
        if not hasattr(self, '_undo_delete_btn'):
            self._undo_delete_btn = QPushButton("↩️ تراجع عن الحذف (Undo)")
            self._undo_delete_btn.setStyleSheet("background-color: #f39c12; color: white; font-size: 11px; font-weight: bold; border-radius: 5px; padding: 4px;")
            self._undo_delete_btn.setFixedWidth(160)
            self._undo_delete_btn.clicked.connect(self.undo_delete_video)
            # أضف الزر أعلى التبويبات
            if hasattr(self, 'tabs'):
                self.layout().insertWidget(0, self._undo_delete_btn)
        self._undo_delete_btn.setVisible(True)

    def undo_delete_video(self):
        if hasattr(self, '_last_deleted_video'):
            # استرجاع الفيديو (نفذ الكود الفعلي هنا)
            # ...
            QMessageBox.information(self, "تم التراجع", "تم استرجاع الفيديو بنجاح.")
            self._undo_delete_btn.setVisible(False)
            del self._last_deleted_video

    def preview_video(self, video):
        """معاينة الفيديو"""
        QMessageBox.information(self, "معاينة الفيديو",
                               f"📹 العنوان: {video['title']}\n"
                               f"📚 المادة: {video['subject']}\n"
                               f"🎓 الصف: {video['grade']}\n"
                               f"📅 الترم: {video['term']}\n"
                               f"📝 الوصف: {video['description']}\n"
                               f"⏱️ المدة: {video['duration_minutes']} دقيقة\n"
                               f"👁️ عدد المشاهدات المسموحة: {video['max_views']}\n"
                               f"📝 يوجد امتحان: {'نعم' if video['has_exam'] else 'لا'}")

    def assign_student_to_video(self):
        """إضافة طالب للفيديو"""
        # الحصول على قائمة الطلاب
        students = self.student_model.get_all_students()

        if not students:
            show_warning_message(self, "تحذير", "لا يوجد طلاب مسجلين في النظام")
            return

        # إنشاء نافذة اختيار الطالب
        dialog = QDialog(self)
        dialog.setWindowTitle("اختيار طالب")
        dialog.setModal(True)

        layout = QVBoxLayout()

        # قائمة الطلاب
        students_list = QListWidget()
        for student in students:
            item_text = f"{student['full_name']} ({student['student_code']}) - {student['grade']}"
            students_list.addItem(item_text)

        layout.addWidget(QLabel("اختر الطالب:"))
        layout.addWidget(students_list)

        # أزرار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)

        dialog.setLayout(layout)

        if dialog.exec_() == QDialog.Accepted:
            selected_row = students_list.currentRow()
            if selected_row >= 0:
                student = students[selected_row]
                self.students_list.addItem(f"{student['full_name']} ({student['student_code']})")
                show_success_message(self, "نجح", f"تم إضافة الطالب {student['full_name']} للفيديو")

    def watch_as_student(self):
        """مشاهدة كطالب مع امتحان مربوط"""
        # محاكاة بيانات طالب للاختبار
        student_info = {
            'id': 1,
            'name': 'طالب تجريبي',
            'code': 'TEST001'
        }

        # محاكاة بيانات فيديو للاختبار
        video_info = {
            'id': 1,
            'title': 'الجغرافيا - الدرس الأول',
            'subject': 'جغرافيا',
            'grade': 'الثاني الثانوي',
            'term': 'الترم الأول',
            'max_views': 3
        }

        # أسئلة الامتحان المربوطة بالفيديو
        exam_questions = [
            {
                'id': 1,
                'type': 'multiple_choice',
                'question': 'ما هي عاصمة مصر؟',
                'options': ['القاهرة', 'الإسكندرية', 'الجيزة', 'أسوان'],
                'correct_answer': 0
            },
            {
                'id': 2,
                'type': 'true_false',
                'question': 'نهر النيل هو أطول نهر في العالم.',
                'correct_answer': True
            },
            {
                'id': 3,
                'type': 'multiple_choice',
                'question': 'في أي قارة تقع مصر؟',
                'options': ['آسيا', 'أفريقيا', 'أوروبا', 'أمريكا'],
                'correct_answer': 1
            },
            {
                'id': 4,
                'type': 'true_false',
                'question': 'البحر الأحمر يقع شرق مصر.',
                'correct_answer': True
            },
            {
                'id': 5,
                'type': 'multiple_choice',
                'question': 'ما هو أطول نهر في مصر؟',
                'options': ['النيل', 'الفرات', 'دجلة', 'الأردن'],
                'correct_answer': 0
            }
        ]

        # فتح نظام الفيديو مع الامتحان المربوط
        from .video_exam_system import VideoExamPlayer

        player_dialog = VideoExamPlayer(video_info, student_info, exam_questions)
        player_dialog.exam_completed.connect(self.on_student_exam_completed)
        player_dialog.exec_()

    def on_student_exam_completed(self, exam_result):
        """عند انتهاء امتحان الطالب"""
        score = exam_result['score']
        total = exam_result['total_questions']
        percentage = (score / total) * 100

        self.append_telegram_log(f"📝 انتهى امتحان الطالب: {exam_result['student_info']['name']}")
        self.append_telegram_log(f"📊 النتيجة: {score}/{total} ({percentage:.1f}%)")

        # تحديث الإحصائيات
        self.update_statistics()

    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            # حساب إجمالي الفيديوهات
            videos_db_file = "videos_database.json"
            if os.path.exists(videos_db_file):
                with open(videos_db_file, 'r', encoding='utf-8') as f:
                    videos_db = json.load(f)
                    total_videos = len(videos_db.get('videos', []))
            else:
                total_videos = 0

            # حساب إجمالي المشاهدات
            total_views = 0
            for filename in os.listdir('.'):
                if filename.startswith('watch_data_') and filename.endswith('.json'):
                    try:
                        with open(filename, 'r', encoding='utf-8') as f:
                            watch_data = json.load(f)
                            total_views += watch_data.get('watch_count', 0)
                    except:
                        continue

            # حساب الطلاب النشطين
            active_students = len(self.student_model.get_all_students())

            # تحديث التسميات
            self.total_videos_label.setText(f"📹 إجمالي الفيديوهات: {total_videos}")
            self.total_views_label.setText(f"👁️ إجمالي المشاهدات: {total_views}")
            self.active_students_label.setText(f"👥 الطلاب النشطين: {active_students}")
            self.completed_exams_label.setText(f"📝 الامتحانات المكتملة: {total_views}")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def test_telegram_connection(self):
        """اختبار الاتصال بتليجرام"""
        bot_token = self.bot_token_input.text().strip()

        if not bot_token:
            show_error_message(self, "خطأ", "يرجى إدخال توكن البوت")
            return

        try:
            # محاكاة اختبار الاتصال
            self.append_telegram_log("🔄 جاري اختبار الاتصال...")

            # في التطبيق الحقيقي، هنا سيتم استخدام python-telegram-bot
            # import telegram
            # bot = telegram.Bot(token=bot_token)
            # bot_info = bot.get_me()

            # محاكاة نجاح الاتصال
            import time
            time.sleep(1)  # محاكاة وقت الاتصال

            self.connection_status_label.setText("🟢 متصل بنجاح")
            self.connection_status_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #27ae60;")

            self.append_telegram_log("✅ تم الاتصال بالبوت بنجاح")
            self.append_telegram_log(f"🤖 اسم البوت: TestBot (محاكاة)")

            show_success_message(self, "نجح", "تم الاتصال بتليجرام بنجاح!")

        except Exception as e:
            self.connection_status_label.setText("🔴 فشل الاتصال")
            self.connection_status_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #e74c3c;")

            self.append_telegram_log(f"❌ فشل الاتصال: {str(e)}")
            show_error_message(self, "خطأ", f"فشل في الاتصال بتليجرام: {str(e)}")

    def save_telegram_settings(self):
        """حفظ إعدادات تليجرام"""
        try:
            settings = {
                'bot_token': self.bot_token_input.text().strip(),
                'channel_id': self.channel_id_input.text().strip(),
                'bot_name': self.bot_name_input.text().strip(),
                'last_updated': datetime.now().isoformat()
            }

            with open('telegram_settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

            self.append_telegram_log("💾 تم حفظ إعدادات تليجرام")
            show_success_message(self, "نجح", "تم حفظ إعدادات تليجرام بنجاح")

        except Exception as e:
            self.append_telegram_log(f"❌ خطأ في حفظ الإعدادات: {str(e)}")
            show_error_message(self, "خطأ", f"فشل في حفظ الإعدادات: {str(e)}")

    def load_telegram_settings(self):
        """تحميل إعدادات تليجرام المحفوظة"""
        try:
            if os.path.exists('telegram_settings.json'):
                with open('telegram_settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                self.bot_token_input.setText(settings.get('bot_token', ''))
                self.channel_id_input.setText(settings.get('channel_id', ''))
                self.bot_name_input.setText(settings.get('bot_name', ''))

                self.append_telegram_log("📂 تم تحميل الإعدادات المحفوظة")

        except Exception as e:
            self.append_telegram_log(f"⚠️ خطأ في تحميل الإعدادات: {str(e)}")

    def fetch_videos_from_telegram(self):
        """جلب الفيديوهات من تليجرام"""
        bot_token = self.bot_token_input.text().strip()
        channel_id = self.channel_id_input.text().strip()

        if not bot_token or not channel_id:
            show_error_message(self, "خطأ", "يرجى إدخال توكن البوت ومعرف القناة")
            return

        try:
            self.append_telegram_log("📥 جاري جلب الفيديوهات من تليجرام...")

            # في التطبيق الحقيقي، هنا سيتم استخدام Telegram API
            # محاكاة جلب الفيديوهات
            import time
            time.sleep(2)  # محاكاة وقت الجلب

            # محاكاة فيديوهات مجلبة
            mock_videos = [
                {
                    'title': 'الجغرافيا - الدرس الأول',
                    'subject': 'جغرافيا',
                    'grade': 'الثاني الثانوي',
                    'term': 'الترم الأول',
                    'file_id': 'telegram_file_123',
                    'description': 'شرح مفصل للدرس الأول في الجغرافيا'
                },
                {
                    'title': 'التاريخ - الحضارة المصرية',
                    'subject': 'تاريخ',
                    'grade': 'الأول الثانوي',
                    'term': 'الترم الثاني',
                    'file_id': 'telegram_file_456',
                    'description': 'تاريخ الحضارة المصرية القديمة'
                }
            ]

            # حفظ الفيديوهات المجلبة
            for video in mock_videos:
                self.save_telegram_video(video)

            self.append_telegram_log(f"✅ تم جلب {len(mock_videos)} فيديو من تليجرام")
            self.append_telegram_log("🔐 جاري تشفير الفيديوهات...")

            # تحديث قائمة الفيديوهات
            self.load_videos()

            show_success_message(self, "نجح", f"تم جلب {len(mock_videos)} فيديو من تليجرام وتشفيرها بنجاح")

        except Exception as e:
            self.append_telegram_log(f"❌ خطأ في جلب الفيديوهات: {str(e)}")
            show_error_message(self, "خطأ", f"فشل في جلب الفيديوهات: {str(e)}")

    def save_telegram_video(self, video_data):
        """حفظ فيديو مجلب من تليجرام"""
        try:
            # إنشاء بيانات الفيديو
            video_info = {
                'id': int(time.time() * 1000),  # معرف فريد
                'title': video_data['title'],
                'subject': video_data['subject'],
                'stage': 'الثانوية',  # افتراضي
                'grade': video_data['grade'],
                'term': video_data['term'],
                'description': video_data['description'],
                'telegram_file_id': video_data['file_id'],
                'source': 'telegram',
                'upload_date': datetime.now().isoformat(),
                'max_views': 3,
                'duration_minutes': 30,  # افتراضي
                'has_exam': True,
                'encrypted_path': f"encrypted_videos/telegram_{video_data['file_id']}.enc"
            }

            # حفظ في قاعدة بيانات الفيديوهات
            videos_db_file = "videos_database.json"
            if os.path.exists(videos_db_file):
                with open(videos_db_file, 'r', encoding='utf-8') as f:
                    videos_db = json.load(f)
            else:
                videos_db = {'videos': []}

            videos_db['videos'].append(video_info)

            with open(videos_db_file, 'w', encoding='utf-8') as f:
                json.dump(videos_db, f, ensure_ascii=False, indent=2)

            self.append_telegram_log(f"💾 تم حفظ فيديو: {video_data['title']}")

        except Exception as e:
            self.append_telegram_log(f"❌ خطأ في حفظ الفيديو: {str(e)}")

    def toggle_auto_sync(self, state):
        """تفعيل/إلغاء المزامنة التلقائية"""
        if state == 2:  # مفعل
            self.append_telegram_log("🔄 تم تفعيل المزامنة التلقائية")
            # في التطبيق الحقيقي، هنا سيتم إنشاء QTimer للمزامنة كل 5 دقائق
            show_success_message(self, "تم التفعيل", "تم تفعيل المزامنة التلقائية\nسيتم جلب الفيديوهات الجديدة كل 5 دقائق")
        else:  # معطل
            self.append_telegram_log("⏸️ تم إيقاف المزامنة التلقائية")

    def create_exam_questions(self):
        """إنشاء أسئلة الامتحان"""
        video_title = self.video_title_input.text().strip()
        subject = self.subject_combo.currentText()
        num_questions = self.exam_questions_input.value()

        if not video_title:
            show_error_message(self, "خطأ", "يرجى إدخال عنوان الفيديو أولاً")
            return

        # فتح نافذة إنشاء الأسئلة
        questions_dialog = ExamQuestionsDialog(video_title, subject, num_questions)
        if questions_dialog.exec_() == QDialog.Accepted:
            questions = questions_dialog.get_questions()
            if questions:
                # حفظ الأسئلة
                self.current_exam_questions = questions
                show_success_message(self, "نجح", f"تم إنشاء {len(questions)} سؤال للامتحان")
            else:
                show_warning_message(self, "تحذير", "لم يتم إنشاء أي أسئلة")

    def setup_styles(self):
        """تطبيق الأنماط"""
        style = get_form_style() + get_table_style() + get_arabic_font_style()
        self.setStyleSheet(style)

    def get_questions(self):
        """الحصول على الأسئلة المحفوظة"""
        return self.questions

    # ===== دوال رفع الفيديوهات على جروب التليجرام =====
    
    def toggle_auto_upload(self, state):
        """تفعيل/إلغاء الرفع التلقائي"""
        if state == Qt.Checked:
            self.append_telegram_log("✅ تم تفعيل الرفع التلقائي على جروب التليجرام")
            self.save_telegram_settings()
        else:
            self.append_telegram_log("❌ تم إلغاء الرفع التلقائي على جروب التليجرام")
            self.save_telegram_settings()
    
    def test_telegram_upload(self):
        """اختبار رفع فيديو على جروب التليجرام"""
        try:
            # التحقق من الإعدادات
            if not self.validate_telegram_settings():
                return
            
            # إنشاء فيديو تجريبي
            test_video = {
                'title': '🎬 فيديو تجريبي - اختبار الربط',
                'subject': 'جغرافيا',
                'stage': 'إعدادي',
                'grade': 'الصف الأول',
                'term': 'الترم الأول',
                'description': 'هذا فيديو تجريبي لاختبار ربط النظام مع جروب التليجرام'
            }
            
            # محاكاة رفع الفيديو
            self.append_telegram_log("🔄 جاري اختبار رفع فيديو على جروب التليجرام...")
            
            # محاكاة عملية الرفع
            import time
            for i in range(5):
                time.sleep(0.5)
                self.append_telegram_log(f"📤 رفع الفيديو... {i+1}/5")
            
            # رسالة نجاح
            success_message = f"""
✅ تم رفع الفيديو بنجاح على جروب التليجرام!

📹 معلومات الفيديو:
   العنوان: {test_video['title']}
   المادة: {test_video['subject']}
   الصف: {test_video['grade']}
   الترم: {test_video['term']}

🔗 رابط الجروب: {self.telegram_group_link.text()}
            """
            
            self.append_telegram_log(success_message)
            show_success_message("نجح", "تم اختبار رفع الفيديو بنجاح على جروب التليجرام!")
            
        except Exception as e:
            error_msg = f"❌ خطأ في اختبار رفع الفيديو: {str(e)}"
            self.append_telegram_log(error_msg)
            show_error_message("خطأ", error_msg)
    
    def upload_all_videos_to_telegram(self):
        """رفع جميع الفيديوهات على جروب التليجرام"""
        try:
            # التحقق من الإعدادات
            if not self.validate_telegram_settings():
                return
            
            # تحميل الفيديوهات
            videos = self.load_videos_from_database()
            
            if not videos:
                show_warning_message("تحذير", "لا توجد فيديوهات لرفعها على جروب التليجرام")
                return
            
            # تأكيد العملية
            reply = QMessageBox.question(
                self, 
                "تأكيد العملية", 
                f"هل تريد رفع {len(videos)} فيديو على جروب التليجرام؟",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            self.append_telegram_log(f"🔄 بدء رفع {len(videos)} فيديو على جروب التليجرام...")
            
            # رفع كل فيديو
            uploaded_count = 0
            for i, video in enumerate(videos):
                try:
                    self.append_telegram_log(f"📤 رفع فيديو {i+1}/{len(videos)}: {video['title']}")
                    
                    # محاكاة رفع الفيديو
                    import time
                    time.sleep(0.3)
                    
                    uploaded_count += 1
                    self.append_telegram_log(f"✅ تم رفع: {video['title']}")
                    
                except Exception as e:
                    self.append_telegram_log(f"❌ فشل رفع: {video['title']} - {str(e)}")
            
            # رسالة النتيجة النهائية
            final_message = f"""
🎉 تم الانتهاء من رفع الفيديوهات!

📊 النتائج:
   ✅ تم رفع: {uploaded_count} فيديو
   ❌ فشل في رفع: {len(videos) - uploaded_count} فيديو
   📹 إجمالي الفيديوهات: {len(videos)}

🔗 رابط الجروب: {self.telegram_group_link.text()}
            """
            
            self.append_telegram_log(final_message)
            show_success_message("نجح", f"تم رفع {uploaded_count} فيديو بنجاح على جروب التليجرام!")
            
        except Exception as e:
            error_msg = f"❌ خطأ في رفع الفيديوهات: {str(e)}"
            self.append_telegram_log(error_msg)
            show_error_message("خطأ", error_msg)
    
    def upload_video_to_telegram(self, video_info):
        """رفع فيديو واحد على جروب التليجرام"""
        try:
            # التحقق من تفعيل الرفع التلقائي
            if not self.auto_upload_checkbox.isChecked():
                return
            
            # التحقق من الإعدادات
            if not self.validate_telegram_settings():
                return
            
            self.append_telegram_log(f"📤 رفع فيديو جديد: {video_info['title']}")
            
            # إنشاء رسالة الفيديو
            video_message = self.create_telegram_video_message(video_info)
            
            # محاكاة إرسال الرسالة
            import time
            time.sleep(0.5)
            
            self.append_telegram_log(f"✅ تم رفع الفيديو بنجاح: {video_info['title']}")
            
            # إضافة معرف فريد للفيديو إذا لم يكن موجوداً
            if 'id' not in video_info:
                video_info['id'] = int(time.time())
            
            # حفظ سجل الرفع
            self.save_upload_log(video_info)
            
        except Exception as e:
            error_msg = f"❌ خطأ في رفع الفيديو: {str(e)}"
            self.append_telegram_log(error_msg)
    
    def create_telegram_video_message(self, video_info):
        """إنشاء رسالة الفيديو للتليجرام"""
        message = f"""
🎬 {video_info['title']}

📚 المادة: {video_info['subject']}
🏫 الصف: {video_info['grade']}
📅 الترم: {video_info['term']}

📝 الوصف: {video_info.get('description', 'لا يوجد وصف')}

⏱️ المدة: {video_info.get('duration', 0)} دقيقة
👁️ الحد الأقصى للمشاهدات: {video_info.get('max_views', 3)}

🔐 فيديو محمي - يتطلب تسجيل دخول
        """
        self.show_loader("جاري اختبار الاتصال...")
        # ... تنفيذ اختبار الاتصال ...
        self.hide_loader()
        # إضافة معلومات الامتحان إذا كان متاحاً
        if self.include_exam_checkbox.isChecked() and video_info.get('has_exam'):
            message += "\n📋 يحتوي على امتحان بعد المشاهدة"
        # تحقق من صحة التوكن
        token = self.bot_token_input.text().strip()
        if not token or len(token) < 30 or not token.isalnum():
            show_error_message(self, "خطأ في التوكن", "يرجى إدخال توكن بوت تليجرام صحيح.")
            return
        # تحقق من صحة الرابط
        link = self.telegram_group_link.text().strip()
        if not (link.startswith("https://t.me/") or link.startswith("http://t.me/")):
            show_error_message(self, "خطأ في الرابط", "يرجى إدخال رابط جروب تليجرام صحيح يبدأ بـ https://t.me/")
            return
        # تحقق من صحة معرف القناة/المجموعة
        channel_id = self.channel_id_input.text().strip()
        if not (channel_id.startswith("@") or channel_id.startswith("-100")):
            show_error_message(self, "خطأ في المعرف", "يرجى إدخال معرف قناة/مجموعة صحيح (يبدأ بـ @ أو -100)")
            return
        # ... تابع الحفظ الفعلي ...
        # إضافة رابط الجروب
        if self.telegram_group_link.text():
            message += f"\n\n🔗 جروب التليجرام: {self.telegram_group_link.text()}"
        
        return message.strip()
    
    def validate_telegram_settings(self):
        """التحقق من صحة إعدادات التليجرام"""
        if not self.bot_token_input.text().strip():
            show_error_message("خطأ", "يرجى إدخال توكن البوت")
            return False
        
        if not self.telegram_group_link.text().strip():
            show_error_message("خطأ", "يرجى إدخال رابط جروب التليجرام")
            return False
        
        if not self.channel_id_input.text().strip():
            show_error_message("خطأ", "يرجى إدخال معرف القناة/المجموعة")
            return False
        
        return True
    
    def load_videos_from_database(self):
        """تحميل الفيديوهات من قاعدة البيانات"""
        try:
            videos_db_file = "videos_database.json"
            if os.path.exists(videos_db_file):
                with open(videos_db_file, 'r', encoding='utf-8') as f:
                    videos_db = json.load(f)
                return videos_db.get('videos', [])
            return []
        except Exception as e:
            print(f"خطأ في تحميل الفيديوهات: {e}")
            return []
    
    def save_upload_log(self, video_info):
        """حفظ سجل رفع الفيديو"""
        try:
            log_file = "telegram_upload_log.json"
            log_data = {
                'video_id': video_info.get('id'),
                'title': video_info['title'],
                'upload_time': datetime.now().isoformat(),
                'telegram_group': self.telegram_group_link.text(),
                'status': 'success'
            }
            
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    logs = json.load(f)
            else:
                logs = []
            
            logs.append(log_data)
            
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(logs, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ سجل الرفع: {e}")
    
    def save_telegram_settings(self):
        """حفظ إعدادات التليجرام"""
        try:
            settings = {
                'bot_token': self.bot_token_input.text(),
                'telegram_group_link': self.telegram_group_link.text(),
                'channel_id': self.channel_id_input.text(),
                'bot_name': self.bot_name_input.text(),
                'auto_upload': self.auto_upload_checkbox.isChecked(),
                'include_info': self.include_info_checkbox.isChecked(),
                'include_exam': self.include_exam_checkbox.isChecked()
            }
            
            with open('telegram_settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            
            self.append_telegram_log("💾 تم حفظ إعدادات التليجرام")
            
        except Exception as e:
            self.append_telegram_log(f"❌ خطأ في حفظ الإعدادات: {str(e)}")
    
    def load_telegram_settings(self):
        """تحميل إعدادات التليجرام"""
        try:
            if os.path.exists('telegram_settings.json'):
                with open('telegram_settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                self.bot_token_input.setText(settings.get('bot_token', ''))
                self.telegram_group_link.setText(settings.get('telegram_group_link', ''))
                self.channel_id_input.setText(settings.get('channel_id', ''))
                self.bot_name_input.setText(settings.get('bot_name', ''))
                self.auto_upload_checkbox.setChecked(settings.get('auto_upload', False))
                self.include_info_checkbox.setChecked(settings.get('include_info', True))
                self.include_exam_checkbox.setChecked(settings.get('include_exam', False))
                
                self.append_telegram_log("📱 تم تحميل إعدادات التليجرام")
                
        except Exception as e:
            self.append_telegram_log(f"❌ خطأ في تحميل الإعدادات: {str(e)}")

    def extract_group_id_from_link(self, group_link):
        """استخراج معرف المجموعة من رابط التليجرام"""
        try:
            # رابط المجموعة: https://t.me/+ZVnTOTqgNV9kMzI0
            if "+" in group_link:
                # استخراج المعرف من الرابط
                invite_hash = group_link.split("+")[-1]
                # معرف المجموعة سيكون في النموذج: -1001234567890
                # سنحتاج لاستخدام API التليجرام للحصول على المعرف الفعلي
                return invite_hash
            return group_link
        except Exception as e:
            print(f"خطأ في استخراج معرف المجموعة: {e}")
            return group_link

    def setup_telegram_group_link(self):
        """إعداد رابط جروب التليجرام تلقائياً"""
        group_link = "https://t.me/+ZVnTOTqgNV9kMzI0"
        self.telegram_group_link.setText(group_link)
        
        # إضافة رسالة في السجل
        self.append_telegram_log(f"✅ تم إعداد رابط جروب التليجرام: {group_link}")
        self.append_telegram_log("📝 ملاحظة: تحتاج لإضافة بوت التليجرام للمجموعة أولاً")
        self.append_telegram_log("🔗 رابط المجموعة: Mr Student Management System")
        
        # حفظ الإعدادات
        self.save_telegram_settings()

class ExamQuestionsDialog(QDialog):
    """نافذة إنشاء أسئلة الامتحان"""

    def __init__(self, video_title, subject, num_questions):
        super().__init__()
        self.video_title = video_title
        self.subject = subject
        self.num_questions = num_questions
        self.questions = []

        self.init_ui()

    def init_ui(self):
        """إعداد واجهة إنشاء الأسئلة"""
        self.setWindowTitle(f"إنشاء أسئلة امتحان: {self.video_title}")
        self.setModal(True)
        self.resize(800, 600)

        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLabel {
                color: #2c3e50;
                font-size: 14px;
                font-weight: bold;
            }
            QLineEdit, QTextEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
            }
            QLineEdit:focus, QTextEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        layout = QVBoxLayout()

        # معلومات الامتحان
        info_frame = QFrame()
        info_frame.setStyleSheet("background-color: #3498db; color: white; padding: 15px; border-radius: 8px;")
        info_layout = QVBoxLayout()

        title_label = QLabel(f"📝 إنشاء أسئلة امتحان: {self.video_title}")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        title_label.setAlignment(Qt.AlignCenter)
        info_layout.addWidget(title_label)

        details_label = QLabel(f"📚 المادة: {self.subject} | 📊 عدد الأسئلة: {self.num_questions}")
        details_label.setAlignment(Qt.AlignCenter)
        info_layout.addWidget(details_label)

        info_frame.setLayout(info_layout)
        layout.addWidget(info_frame)

        # منطقة إنشاء الأسئلة
        questions_scroll = QScrollArea()
        self.questions_widget = QWidget()
        self.questions_layout = QVBoxLayout()
        self.questions_widget.setLayout(self.questions_layout)
        questions_scroll.setWidget(self.questions_widget)
        questions_scroll.setWidgetResizable(True)
        layout.addWidget(questions_scroll)

        # إنشاء نماذج الأسئلة
        self.create_question_forms()

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        add_question_btn = QPushButton("➕ إضافة سؤال")
        add_question_btn.clicked.connect(self.add_question_form)
        buttons_layout.addWidget(add_question_btn)

        preview_btn = QPushButton("👁️ معاينة الأسئلة")
        preview_btn.clicked.connect(self.preview_questions)
        buttons_layout.addWidget(preview_btn)

        buttons_layout.addStretch()

        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        save_btn = QPushButton("💾 حفظ الأسئلة")
        save_btn.clicked.connect(self.save_questions)
        buttons_layout.addWidget(save_btn)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def create_question_forms(self):
        """إنشاء نماذج الأسئلة"""
        for i in range(self.num_questions):
            self.add_question_form(i + 1)

    def add_question_form(self, question_num=None):
        """إضافة نموذج سؤال"""
        if question_num is None:
            question_num = self.questions_layout.count() + 1

        question_frame = QGroupBox(f"السؤال {question_num}")
        question_frame.setStyleSheet("QGroupBox { font-weight: bold; padding: 10px; }")
        question_layout = QVBoxLayout()

        # نوع السؤال
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("نوع السؤال:"))

        type_combo = QComboBox()
        type_combo.addItems(["اختيار متعدد", "صح/خطأ"])
        type_combo.currentTextChanged.connect(lambda text, frame=question_frame: self.update_question_type(frame, text))
        type_layout.addWidget(type_combo)

        question_layout.addLayout(type_layout)

        # نص السؤال
        question_layout.addWidget(QLabel("نص السؤال:"))
        question_text = QTextEdit()
        question_text.setMaximumHeight(80)
        question_text.setPlaceholderText("أدخل نص السؤال هنا...")
        question_layout.addWidget(question_text)

        # منطقة الخيارات (ستتغير حسب نوع السؤال)
        options_frame = QFrame()
        options_layout = QVBoxLayout()
        options_frame.setLayout(options_layout)
        question_layout.addWidget(options_frame)

        # تحديث نوع السؤال الافتراضي
        self.update_question_type(question_frame, "اختيار متعدد")

        question_frame.setLayout(question_layout)
        self.questions_layout.addWidget(question_frame)

    def update_question_type(self, question_frame, question_type):
        """تحديث نوع السؤال"""
        # البحث عن إطار الخيارات
        options_frame = None
        for i in range(question_frame.layout().count()):
            widget = question_frame.layout().itemAt(i).widget()
            if isinstance(widget, QFrame) and widget != question_frame:
                options_frame = widget
                break

        if not options_frame:
            return

        # مسح الخيارات السابقة
        layout = options_frame.layout()
        for i in reversed(range(layout.count())):
            layout.itemAt(i).widget().setParent(None)

        if question_type == "اختيار متعدد":
            # إنشاء 4 خيارات
            layout.addWidget(QLabel("الخيارات:"))

            for i in range(4):
                option_layout = QHBoxLayout()

                option_radio = QRadioButton(f"الخيار {chr(65+i)}:")
                option_layout.addWidget(option_radio)

                option_text = QLineEdit()
                option_text.setPlaceholderText(f"أدخل الخيار {chr(65+i)}")
                option_layout.addWidget(option_text)

                layout.addLayout(option_layout)

            layout.addWidget(QLabel("اختر الإجابة الصحيحة بالضغط على الدائرة المناسبة"))

        elif question_type == "صح/خطأ":
            # إنشاء خيارات صح/خطأ
            layout.addWidget(QLabel("الإجابة الصحيحة:"))

            true_radio = QRadioButton("صح")
            false_radio = QRadioButton("خطأ")

            answer_layout = QHBoxLayout()
            answer_layout.addWidget(true_radio)
            answer_layout.addWidget(false_radio)

            layout.addLayout(answer_layout)

    def preview_questions(self):
        """معاينة الأسئلة"""
        questions = self.collect_questions()
        if not questions:
            QMessageBox.warning(self, "تحذير", "لا توجد أسئلة للمعاينة")
            return

        preview_text = "📝 معاينة أسئلة الامتحان:\n\n"

        for i, question in enumerate(questions):
            preview_text += f"السؤال {i+1}: {question['question']}\n"

            if question['type'] == 'multiple_choice':
                for j, option in enumerate(question['options']):
                    marker = "✓" if j == question['correct_answer'] else " "
                    preview_text += f"  {chr(65+j)}. {option} {marker}\n"
            else:
                correct = "صح" if question['correct_answer'] else "خطأ"
                preview_text += f"  الإجابة الصحيحة: {correct}\n"

            preview_text += "\n"

        QMessageBox.information(self, "معاينة الأسئلة", preview_text)

    def collect_questions(self):
        """جمع الأسئلة من النماذج"""
        questions = []

        for i in range(self.questions_layout.count()):
            question_frame = self.questions_layout.itemAt(i).widget()
            if not isinstance(question_frame, QGroupBox):
                continue

            question_data = self.extract_question_data(question_frame)
            if question_data:
                questions.append(question_data)

        return questions

    def extract_question_data(self, question_frame):
        """استخراج بيانات السؤال من النموذج"""
        try:
            layout = question_frame.layout()

            # الحصول على نوع السؤال
            type_combo = None
            question_text_widget = None
            options_frame = None

            for i in range(layout.count()):
                item = layout.itemAt(i)
                if item.layout():  # HBoxLayout للنوع
                    for j in range(item.layout().count()):
                        widget = item.layout().itemAt(j).widget()
                        if isinstance(widget, QComboBox):
                            type_combo = widget
                elif isinstance(item.widget(), QTextEdit):
                    question_text_widget = item.widget()
                elif isinstance(item.widget(), QFrame):
                    options_frame = item.widget()

            if not all([type_combo, question_text_widget, options_frame]):
                return None

            question_type = "multiple_choice" if type_combo.currentText() == "اختيار متعدد" else "true_false"
            question_text = question_text_widget.toPlainText().strip()

            if not question_text:
                return None

            question_data = {
                'type': question_type,
                'question': question_text
            }

            if question_type == 'multiple_choice':
                options = []
                correct_answer = 0

                options_layout = options_frame.layout()
                for i in range(1, options_layout.count()):  # تجاهل Label الأول
                    item = options_layout.itemAt(i)
                    if item.layout():
                        radio = None
                        text_edit = None
                        for j in range(item.layout().count()):
                            widget = item.layout().itemAt(j).widget()
                            if isinstance(widget, QRadioButton):
                                radio = widget
                            elif isinstance(widget, QLineEdit):
                                text_edit = widget

                        if radio and text_edit:
                            option_text = text_edit.text().strip()
                            if option_text:
                                options.append(option_text)
                                if radio.isChecked():
                                    correct_answer = len(options) - 1

                question_data['options'] = options
                question_data['correct_answer'] = correct_answer

            else:  # true_false
                correct_answer = True  # افتراضي
                options_layout = options_frame.layout()
                for i in range(options_layout.count()):
                    item = options_layout.itemAt(i)
                    if item.layout():
                        for j in range(item.layout().count()):
                            widget = item.layout().itemAt(j).widget()
                            if isinstance(widget, QRadioButton) and widget.isChecked():
                                correct_answer = widget.text() == "صح"
                                break

                question_data['correct_answer'] = correct_answer

            return question_data

        except Exception as e:
            print(f"خطأ في استخراج بيانات السؤال: {e}")
            return None

    def save_questions(self):
        """حفظ الأسئلة"""
        questions = self.collect_questions()

        if not questions:
            QMessageBox.warning(self, "تحذير", "لا توجد أسئلة صحيحة للحفظ")
            return

        # التحقق من اكتمال الأسئلة
        incomplete_questions = []
        for i, question in enumerate(questions):
            if question['type'] == 'multiple_choice':
                if len(question.get('options', [])) < 2:
                    incomplete_questions.append(i + 1)
            elif not question.get('question'):
                incomplete_questions.append(i + 1)

        if incomplete_questions:
            QMessageBox.warning(self, "أسئلة غير مكتملة",
                               f"الأسئلة التالية غير مكتملة: {', '.join(map(str, incomplete_questions))}")
            return

        self.questions = questions
        QMessageBox.information(self, "نجح", f"تم حفظ {len(questions)} سؤال بنجاح")
        self.accept()

    def get_questions(self):
        """الحصول على الأسئلة المحفوظة"""
        return self.questions
