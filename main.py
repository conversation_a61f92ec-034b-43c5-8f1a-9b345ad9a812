#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة المدارس - School Management System

هو نظام متكامل لإدارة المدارس يشمل إدارة الطلاب والمعلمين والصفوف 
والحضور والغياب والدرجات والامتحانات والمدفوعات.
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon

# Add src directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.ui.main_window import MainWindow
from src.database.database_manager import DatabaseManager
from src.utils.auth import AuthManager
from src.ui.login_dialog import LoginDialog

def main():
    """الدالة الرئيسية لتشغيل التطبيق."""
    try:
        # Create application instance
        app = QApplication(sys.argv)
        
        # Set application properties
        app.setApplicationName("نظام إدارة المدارس")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("نظام إدارة المدارس المتطور")
        
        # Set Arabic font and RTL layout
        font = QFont("Arial", 10)
        app.setFont(font)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # Initialize database
        print("📊 جاري تهيئة قاعدة البيانات...")
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # Initialize authentication manager - FIXED: Pass the db_manager instance directly
        auth_manager = AuthManager(db_manager)
        
        # Show login dialog
        login_dialog = LoginDialog(auth_manager)
        if login_dialog.exec_() == LoginDialog.Accepted:
            # Create and show main window if login successful
            print("🚀 جاري تحميل النافذة الرئيسية...")
            main_window = MainWindow(auth_manager)
            main_window.show()
            print("✅ تم تشغيل التطبيق بنجاح!")
            sys.exit(app.exec_())
        else:
            print("❌ تم إلغاء تسجيل الدخول")
            return 0
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        QMessageBox.critical(
            None,
            "خطأ",
            f"حدث خطأ أثناء تشغيل التطبيق:\n{str(e)}\n\nالرجاء مراجعة ملف السجل لمزيد من التفاصيل."
        )
        return 1

if __name__ == "__main__":
    main()
