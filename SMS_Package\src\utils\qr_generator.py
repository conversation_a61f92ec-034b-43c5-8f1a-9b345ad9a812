# -*- coding: utf-8 -*-
"""
مولد QR Code المشفر للطلاب
QR Code Generator with Encryption
"""

import qrcode
import json
import hashlib
import hmac
import base64
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PB<PERSON>DF2HMAC
import os
from PIL import Image, ImageDraw, ImageFont
import io

class QRCodeGenerator:
    """مولد QR Code مشفر للطلاب"""
    
    def __init__(self, secret_key=None):
        """تهيئة المولد مع مفتاح التشفير"""
        if secret_key is None:
            secret_key = "SMS_QR_SECRET_KEY_2024"
        
        self.secret_key = secret_key.encode()
        self.cipher_suite = self._generate_cipher()
        
    def _generate_cipher(self):
        """إنشاء مفتاح التشفير"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'sms_salt_2024',
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.secret_key))
        return Fernet(key)
    
    def _generate_signature(self, data):
        """إنشاء توقيع رقمي للبيانات"""
        return hmac.new(
            self.secret_key,
            data.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _verify_signature(self, data, signature):
        """التحقق من صحة التوقيع الرقمي"""
        expected_signature = self._generate_signature(data)
        return hmac.compare_digest(expected_signature, signature)
    
    def generate_student_qr(self, student_data, validity_hours=24):
        """
        إنشاء QR Code مشفر للطالب
        
        Args:
            student_data: بيانات الطالب (dict)
            validity_hours: مدة صلاحية QR Code بالساعات
        
        Returns:
            tuple: (qr_image, qr_data_string)
        """
        try:
            # إنشاء البيانات المشفرة
            current_time = datetime.now()
            expiry_time = current_time + timedelta(hours=validity_hours)
            
            qr_payload = {
                'student_id': student_data['id'],
                'student_code': student_data['student_code'],
                'student_name': student_data['full_name'],
                'generated_at': current_time.isoformat(),
                'expires_at': expiry_time.isoformat(),
                'version': '1.0'
            }
            
            # تحويل البيانات إلى JSON
            json_data = json.dumps(qr_payload, ensure_ascii=False)
            
            # إنشاء التوقيع الرقمي
            signature = self._generate_signature(json_data)
            
            # إضافة التوقيع للبيانات
            signed_data = {
                'data': qr_payload,
                'signature': signature
            }
            
            # تشفير البيانات
            encrypted_data = self.cipher_suite.encrypt(
                json.dumps(signed_data, ensure_ascii=False).encode('utf-8')
            )
            
            # تحويل إلى base64 للـ QR Code
            qr_string = base64.urlsafe_b64encode(encrypted_data).decode('utf-8')
            
            # إنشاء QR Code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_M,
                box_size=10,
                border=4,
            )
            qr.add_data(qr_string)
            qr.make(fit=True)
            
            # إنشاء صورة QR Code
            qr_image = qr.make_image(fill_color="black", back_color="white")
            
            # إضافة معلومات الطالب للصورة
            enhanced_image = self._add_student_info_to_qr(qr_image, student_data, expiry_time)
            
            return enhanced_image, qr_string
            
        except Exception as e:
            print(f"خطأ في إنشاء QR Code: {e}")
            return None, None
    
    def _add_student_info_to_qr(self, qr_image, student_data, expiry_time):
        """إضافة معلومات الطالب لصورة QR Code"""
        try:
            # تحويل QR Code إلى RGB
            qr_image = qr_image.convert('RGB')
            
            # إنشاء صورة جديدة أكبر لتشمل النص
            width, height = qr_image.size
            new_height = height + 120
            new_image = Image.new('RGB', (width, new_height), 'white')
            
            # لصق QR Code في الأعلى
            new_image.paste(qr_image, (0, 0))
            
            # إضافة النص
            draw = ImageDraw.Draw(new_image)
            
            try:
                # محاولة استخدام خط عربي
                font_large = ImageFont.truetype("arial.ttf", 16)
                font_small = ImageFont.truetype("arial.ttf", 12)
            except:
                # استخدام الخط الافتراضي
                font_large = ImageFont.load_default()
                font_small = ImageFont.load_default()
            
            # معلومات الطالب
            student_name = student_data['full_name']
            student_code = student_data['student_code']
            expiry_str = expiry_time.strftime("%Y-%m-%d %H:%M")
            
            # رسم النصوص
            y_offset = height + 10
            
            # اسم الطالب
            draw.text((10, y_offset), f"الطالب: {student_name}", 
                     fill='black', font=font_large)
            y_offset += 25
            
            # كود الطالب
            draw.text((10, y_offset), f"الكود: {student_code}", 
                     fill='black', font=font_small)
            y_offset += 20
            
            # تاريخ الانتهاء
            draw.text((10, y_offset), f"صالح حتى: {expiry_str}", 
                     fill='red', font=font_small)
            y_offset += 20
            
            # تحذير
            draw.text((10, y_offset), "⚠️ لا تشارك هذا الرمز مع أحد", 
                     fill='red', font=font_small)
            
            return new_image
            
        except Exception as e:
            print(f"خطأ في إضافة معلومات الطالب: {e}")
            return qr_image
    
    def verify_qr_code(self, qr_string):
        """
        التحقق من صحة QR Code وفك تشفيره
        
        Args:
            qr_string: نص QR Code المشفر
        
        Returns:
            dict: بيانات الطالب إذا كان صحيحاً، None إذا كان خاطئاً
        """
        try:
            # فك تشفير base64
            encrypted_data = base64.urlsafe_b64decode(qr_string.encode('utf-8'))
            
            # فك التشفير
            decrypted_data = self.cipher_suite.decrypt(encrypted_data)
            signed_data = json.loads(decrypted_data.decode('utf-8'))
            
            # استخراج البيانات والتوقيع
            data = signed_data['data']
            signature = signed_data['signature']
            
            # التحقق من التوقيع
            json_data = json.dumps(data, ensure_ascii=False)
            if not self._verify_signature(json_data, signature):
                return {'valid': False, 'error': 'توقيع غير صحيح'}
            
            # التحقق من انتهاء الصلاحية
            expiry_time = datetime.fromisoformat(data['expires_at'])
            if datetime.now() > expiry_time:
                return {'valid': False, 'error': 'انتهت صلاحية الرمز'}
            
            # إرجاع البيانات الصحيحة
            return {
                'valid': True,
                'student_id': data['student_id'],
                'student_code': data['student_code'],
                'student_name': data['student_name'],
                'generated_at': data['generated_at'],
                'expires_at': data['expires_at']
            }
            
        except Exception as e:
            return {'valid': False, 'error': f'خطأ في فك التشفير: {str(e)}'}
    
    def save_qr_image(self, qr_image, file_path):
        """حفظ صورة QR Code"""
        try:
            qr_image.save(file_path, 'PNG')
            return True
        except Exception as e:
            print(f"خطأ في حفظ QR Code: {e}")
            return False
    
    def generate_batch_qr_codes(self, students_list, output_dir="qr_codes"):
        """إنشاء QR Codes لمجموعة من الطلاب"""
        try:
            # إنشاء مجلد الحفظ
            os.makedirs(output_dir, exist_ok=True)
            
            generated_codes = []
            
            for student in students_list:
                # إنشاء QR Code
                qr_image, qr_string = self.generate_student_qr(student)
                
                if qr_image and qr_string:
                    # حفظ الصورة
                    filename = f"qr_{student['student_code']}.png"
                    file_path = os.path.join(output_dir, filename)
                    
                    if self.save_qr_image(qr_image, file_path):
                        generated_codes.append({
                            'student_id': student['id'],
                            'student_code': student['student_code'],
                            'student_name': student['full_name'],
                            'qr_string': qr_string,
                            'file_path': file_path
                        })
            
            return generated_codes
            
        except Exception as e:
            print(f"خطأ في إنشاء QR Codes متعددة: {e}")
            return []

# مثال للاستخدام
if __name__ == "__main__":
    # إنشاء مولد QR Code
    qr_gen = QRCodeGenerator()
    
    # بيانات طالب تجريبية
    student_data = {
        'id': 1,
        'student_code': 'ST001',
        'full_name': 'أحمد محمد علي'
    }
    
    # إنشاء QR Code
    qr_image, qr_string = qr_gen.generate_student_qr(student_data)
    
    if qr_image:
        # حفظ الصورة
        qr_gen.save_qr_image(qr_image, "test_qr.png")
        print("تم إنشاء QR Code بنجاح")
        
        # اختبار التحقق
        verification_result = qr_gen.verify_qr_code(qr_string)
        print(f"نتيجة التحقق: {verification_result}")
    else:
        print("فشل في إنشاء QR Code")
