@echo off
title Student Management System - Complete Setup and Launch

cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Student Management System                     ║
echo ║                Complete Setup and Launch                     ║
echo ║                    Advanced Student Management System                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo Choose your preferred method:
echo.
echo [1] Quick Launch (if everything is ready)
echo [2] Complete Installation (download and install everything)
echo [3] PowerShell Installation (advanced)
echo [4] Manual Setup Guide
echo [5] Exit
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto quick_launch
if "%choice%"=="2" goto complete_install
if "%choice%"=="3" goto powershell_install
if "%choice%"=="4" goto manual_guide
if "%choice%"=="5" goto exit

echo Invalid choice! Please try again.
pause
goto start

:quick_launch
echo.
echo ========================================
echo   Quick Launch
echo ========================================
echo.

REM Try to find and run the application
if exist "StudentManagementSystem.exe" (
    echo Found EXE file! Starting...
    StudentManagementSystem.exe
    goto end
)

if exist "dist\StudentManagementSystem.exe" (
    echo Found EXE in dist folder! Starting...
    dist\StudentManagementSystem.exe
    goto end
)

if exist "LAUNCH_APP.bat" (
    echo Found launcher! Starting...
    call LAUNCH_APP.bat
    goto end
)

python --version >nul 2>&1
if not errorlevel 1 (
    if exist "main.py" (
        echo Starting with Python...
        python main.py
        goto end
    )
)

echo No ready-to-run version found!
echo Please choose option 2 for complete installation.
pause
goto start

:complete_install
echo.
echo ========================================
echo   Complete Installation
echo ========================================
echo.
echo This will download and install everything needed.
echo Please make sure you have internet connection.
echo.
pause

call DOWNLOAD_AND_INSTALL_ALL.bat
goto end

:powershell_install
echo.
echo ========================================
echo   PowerShell Installation
echo ========================================
echo.
echo Starting PowerShell installation script...
echo.

powershell -ExecutionPolicy Bypass -File Install-Everything.ps1
goto end

:manual_guide
echo.
echo ========================================
echo   Manual Setup Guide
echo ========================================
echo.
echo Step 1: Install Python
echo ----------------------
echo 1. Go to: https://python.org/downloads
echo 2. Download Python 3.8 or newer
echo 3. During installation, CHECK "Add Python to PATH"
echo 4. Restart your computer after installation
echo.
echo Step 2: Install Required Packages
echo ---------------------------------
echo 1. Open Command Prompt (cmd)
echo 2. Run: pip install PyQt5
echo 3. Run: pip install reportlab openpyxl Pillow
echo.
echo Step 3: Run Application
echo ----------------------
echo 1. Double-click: LAUNCH_APP.bat
echo 2. Or run: python main.py
echo.
echo Login Credentials:
echo Username: admin
echo Password: admin123
echo.
pause
goto start

:exit
exit

:end
echo.
echo ========================================
echo   Session Complete
echo ========================================
echo.
echo Login credentials:
echo Username: admin
echo Password: admin123
echo.
echo For help, see:
echo - USER_GUIDE.md
echo - READ_ME_FIRST.txt
echo.
pause

:start
