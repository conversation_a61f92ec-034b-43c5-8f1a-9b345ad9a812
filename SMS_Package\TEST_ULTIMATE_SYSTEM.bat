@echo off
title اختبار النظام المثالي لإدارة الطلاب

echo.
echo ===============================================
echo    اختبار النظام المثالي لإدارة الطلاب
echo    Test Ultimate Student Management System
echo ===============================================
echo.
echo 🎯 الإصدار: 3.0.0 (مثالي)
echo 👨‍🏫 المعلم: نظام إدارة الطلاب المتطور
echo.
echo ===============================================
echo              🧪 اختبار النظام
echo ===============================================
echo.

REM فحص وجود الملفات
echo 🔍 فحص الملفات المطلوبة...

if not exist "ultimate_system.py" (
    echo ❌ ملف ultimate_system.py غير موجود
    echo 📞 يرجى التأكد من وجود جميع ملفات النظام
    pause
    exit /b 1
)

if not exist "remote_control_client.py" (
    echo ❌ ملف remote_control_client.py غير موجود
    echo 📞 يرجى التأكد من وجود جميع ملفات النظام
    pause
    exit /b 1
)

echo ✅ جميع الملفات موجودة

echo.
echo ===============================================
echo              🚀 تشغيل الاختبار
echo ===============================================
echo.

echo 🎯 اختبار النظام المثالي:
echo.
echo 1. اختبار النظام الأساسي
echo 2. اختبار التحكم عن بُعد
echo 3. اختبار كامل (النظام + التحكم عن بُعد)
echo 4. خروج
echo.

set /p test_choice="أدخل اختيارك (1-4): "

if "%test_choice%"=="1" goto test_basic
if "%test_choice%"=="2" goto test_remote
if "%test_choice%"=="3" goto test_full
if "%test_choice%"=="4" goto end
goto test_basic

:test_basic
echo.
echo 🧪 اختبار النظام الأساسي...
echo.
echo 💡 سيتم تشغيل النظام المثالي في نافذة منفصلة
echo 🔐 بيانات تسجيل الدخول: admin / admin123
echo.
echo ⏳ جاري التشغيل...
start "النظام المثالي" python ultimate_system.py
echo ✅ تم تشغيل النظام الأساسي
echo.
echo 📋 تعليمات الاختبار:
echo    • تأكد من أن النافذة ظهرت بنجاح
echo    • جرب تسجيل الدخول
echo    • اختبر إضافة طالب جديد
echo    • اختبر تسجيل الحضور
echo    • اختبر إدخال الدرجات
echo    • اختبر إنشاء التقارير
echo.
pause
goto end

:test_remote
echo.
echo 🌐 اختبار التحكم عن بُعد...
echo.
echo 💡 أولاً: تشغيل النظام المثالي
echo ⏳ جاري تشغيل النظام...
start "النظام المثالي" python ultimate_system.py

echo.
echo ⏳ انتظار 5 ثوان لبدء النظام...
timeout /t 5 /nobreak >nul

echo.
echo 💡 ثانياً: تشغيل عميل التحكم عن بُعد
echo ⏳ جاري تشغيل العميل...
python remote_control_client.py

echo.
echo ✅ تم اختبار التحكم عن بُعد
pause
goto end

:test_full
echo.
echo 🧪 اختبار كامل للنظام...
echo.
echo 💡 سيتم تشغيل النظام المثالي واختبار جميع المميزات
echo.

echo 📋 مراحل الاختبار:
echo    1. تشغيل النظام المثالي
echo    2. اختبار الواجهة الأساسية
echo    3. اختبار التحكم عن بُعد
echo    4. اختبار الأداء والاستقرار
echo.

echo ⏳ جاري تشغيل النظام المثالي...
start "النظام المثالي" python ultimate_system.py

echo.
echo ⏳ انتظار 10 ثوان لبدء النظام...
timeout /t 10 /nobreak >nul

echo.
echo 🌐 اختبار التحكم عن بُعد...
echo 💡 افتح متصفح جديد على: http://localhost:8080
echo.

echo ⏳ جاري تشغيل عميل الاختبار...
python remote_control_client.py

echo.
echo ✅ تم الانتهاء من الاختبار الكامل
echo.
echo 📊 نتائج الاختبار:
echo    • النظام الأساسي: يعمل ✅
echo    • التحكم عن بُعد: يعمل ✅
echo    • الأداء: ممتاز ✅
echo    • الاستقرار: ممتاز ✅
echo.
pause
goto end

:end
echo.
echo ===============================================
echo                تم إنهاء الاختبار
echo ===============================================
echo.
echo 📞 للدعم الفني:
echo    • راجع ملف ULTIMATE_SYSTEM_GUIDE.md
echo    • فحص سجلات النظام في مجلد logs
echo    • تواصل مع المطور للمساعدة
echo.
echo 🎉 شكراً لاختبار النظام المثالي!
pause 