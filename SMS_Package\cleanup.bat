@echo off
title تنظيف ملفات النظام

echo ===================================================
echo    تنظيف ملفات نظام إدارة المدارس
echo    سيتم حذف الملفات غير الضرورية
echo ===================================================

:: List of files and directories to keep
set "KEEP_FILES=main.py Run_School_Management.bat cleanup.bat requirements.txt README.md"
set "KEEP_DIRS=src data"

:: Remove all batch files except the ones we want to keep
for %%f in (*.bat) do (
    set "KEEP=0"
    for %%k in (%KEEP_FILES%) do if /i "%%~nxf"=="%%~nxk" set "KEEP=1"
    if "!KEEP!"=="0" (
        echo حذف الملف: %%~nxf
        del /f /q "%%f" >nul 2>&1
    )
)

:: Remove other unnecessary files
del /f /q *.md *.txt *.pyc *.pyo *.log *.bak *.tmp 2>nul

:: Remove empty directories
for /d %%d in (*) do (
    set "KEEP=0"
    for %%k in (%KEEP_DIRS%) do if /i "%%~nxd"=="%%~nxk" set "KEEP=1"
    if "!KEEP!"=="0" (
        echo حذف المجلد: %%~nxd
        rd /s /q "%%d" 2>nul
    )
)

echo.
echo ===================================================
echo    تم تنظيف الملفات بنجاح!
echo    الملفات المتبقية:
echo    - main.py: الملف الرئيسي للتطبيق
echo    - Run_School_Management.bat: لتشغيل التطبيق
echo    - cleanup.bat: لتنظيف الملفات
echo    - src/: مجلد الكود المصدري
echo    - data/: مجلد قاعدة البيانات
echo ===================================================

echo.
pause
