#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل النظام المحسن
Fix Enhanced System Issues

هذا الملف يصلح المشاكل المكتشفة في النظام المحسن
"""

import sys
import os
import subprocess
from pathlib import Path

def check_python_version():
    """فحص إصدار Python"""
    print("🐍 فحص إصدار Python...")
    
    if sys.version_info < (3, 6):
        print("❌ يتطلب Python 3.6 أو أحدث")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_required_packages():
    """تثبيت المتطلبات الأساسية"""
    print("📦 تثبيت المتطلبات الأساسية...")
    
    basic_packages = [
        'PyQt5',
        'reportlab',
        'openpyxl', 
        'Pillow',
        'requests'
    ]
    
    for package in basic_packages:
        try:
            print(f"📥 تثبيت {package}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                         check=True, capture_output=True)
            print(f"✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"⚠️ فشل في تثبيت {package}")

def install_optional_packages():
    """تثبيت المتطلبات الاختيارية"""
    print("🔧 تثبيت المتطلبات المحسنة (اختيارية)...")
    
    optional_packages = [
        'Flask',
        'Flask-SocketIO',
        'SQLAlchemy',
        'psycopg2-binary',
        'PyMySQL',
        'python-telegram-bot',
        'google-cloud-storage',
        'boto3',
        'dropbox',
        'packaging',
        'cryptography',
        'eventlet'
    ]
    
    installed_count = 0
    for package in optional_packages:
        try:
            print(f"📥 تثبيت {package}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                         check=True, capture_output=True)
            print(f"✅ تم تثبيت {package}")
            installed_count += 1
        except subprocess.CalledProcessError:
            print(f"⚠️ فشل في تثبيت {package} (اختياري)")
    
    print(f"📊 تم تثبيت {installed_count} من {len(optional_packages)} مكتبة اختيارية")

def create_missing_directories():
    """إنشاء المجلدات المفقودة"""
    print("📁 إنشاء المجلدات المطلوبة...")
    
    directories = [
        'data',
        'logs', 
        'exports',
        'backups',
        'temp',
        'src/web_server/templates',
        'src/web_server/static'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ مجلد {directory}")

def create_missing_init_files():
    """إنشاء ملفات __init__.py المفقودة"""
    print("📄 إنشاء ملفات __init__.py...")
    
    init_files = [
        'src/__init__.py',
        'src/web_server/__init__.py',
        'src/cloud_sync/__init__.py', 
        'src/telegram_bot/__init__.py',
        'src/updater/__init__.py'
    ]
    
    for init_file in init_files:
        init_path = Path(init_file)
        if not init_path.exists():
            init_path.parent.mkdir(parents=True, exist_ok=True)
            with open(init_path, 'w', encoding='utf-8') as f:
                f.write('# -*- coding: utf-8 -*-\n')
            print(f"✅ {init_file}")

def test_imports():
    """اختبار الاستيرادات"""
    print("🧪 اختبار الاستيرادات...")
    
    # اختبار الاستيرادات الأساسية
    basic_imports = [
        ('PyQt5.QtWidgets', 'QApplication'),
        ('reportlab.pdfgen', 'canvas'),
        ('openpyxl', 'Workbook'),
        ('PIL', 'Image'),
        ('requests', None)
    ]
    
    for module, attr in basic_imports:
        try:
            imported = __import__(module, fromlist=[attr] if attr else [])
            if attr:
                getattr(imported, attr)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - مطلوب للتشغيل الأساسي")
        except AttributeError:
            print(f"⚠️ {module}.{attr} - مشكلة في الاستيراد")
    
    # اختبار الاستيرادات المحسنة
    enhanced_imports = [
        ('flask', 'Flask'),
        ('flask_socketio', 'SocketIO'),
        ('sqlalchemy', 'create_engine'),
        ('telegram', 'Bot')
    ]
    
    print("\n🔧 اختبار المميزات المحسنة:")
    for module, attr in enhanced_imports:
        try:
            imported = __import__(module, fromlist=[attr] if attr else [])
            if attr:
                getattr(imported, attr)
            print(f"✅ {module} - متاح")
        except ImportError:
            print(f"⚠️ {module} - غير متاح (اختياري)")

def check_file_structure():
    """فحص هيكل الملفات"""
    print("📋 فحص هيكل الملفات...")
    
    required_files = [
        'main.py',
        'enhanced_main.py',
        'src/ui/login_window.py',
        'src/ui/main_window.py',
        'src/database/database_manager.py',
        'src/models/student.py',
        'src/models/attendance.py',
        'src/models/grades.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print("\n❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    return True

def create_simple_config_files():
    """إنشاء ملفات إعدادات بسيطة"""
    print("⚙️ إنشاء ملفات الإعدادات...")
    
    # ملف إعدادات قاعدة البيانات
    db_config = {
        "database_type": "sqlite",
        "sqlite": {
            "database_path": "data/students.db"
        }
    }
    
    with open('database_config.json', 'w', encoding='utf-8') as f:
        import json
        json.dump(db_config, f, indent=2, ensure_ascii=False)
    print("✅ database_config.json")
    
    # ملف إعدادات السحابة
    cloud_config = {
        "enabled": False,
        "providers": {
            "google_drive": {"enabled": False},
            "dropbox": {"enabled": False}
        }
    }
    
    with open('cloud_config.json', 'w', encoding='utf-8') as f:
        json.dump(cloud_config, f, indent=2, ensure_ascii=False)
    print("✅ cloud_config.json")
    
    # ملف إعدادات Telegram
    telegram_config = {
        "bot_token": "",
        "enabled": False,
        "admin_chat_ids": [],
        "authorized_chat_ids": []
    }
    
    with open('telegram_config.json', 'w', encoding='utf-8') as f:
        json.dump(telegram_config, f, indent=2, ensure_ascii=False)
    print("✅ telegram_config.json")

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح مشاكل النظام المحسن")
    print("=" * 50)
    
    # فحص Python
    if not check_python_version():
        return False
    
    # تثبيت المتطلبات
    install_required_packages()
    install_optional_packages()
    
    # إنشاء المجلدات
    create_missing_directories()
    
    # إنشاء ملفات __init__.py
    create_missing_init_files()
    
    # إنشاء ملفات الإعدادات
    create_simple_config_files()
    
    # فحص هيكل الملفات
    if not check_file_structure():
        print("\n❌ هيكل الملفات غير مكتمل")
        return False
    
    # اختبار الاستيرادات
    test_imports()
    
    print("\n" + "=" * 50)
    print("✅ تم إصلاح جميع المشاكل المكتشفة")
    print("\n🚀 يمكنك الآن تشغيل النظام باستخدام:")
    print("   • START_ULTIMATE_SYSTEM.bat")
    print("   • python enhanced_main.py")
    print("   • python main.py")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ فشل في إصلاح بعض المشاكل")
        input("اضغط Enter للخروج...")
        sys.exit(1)
    else:
        print("\n🎉 النظام جاهز للاستخدام!")
        input("اضغط Enter للخروج...")
