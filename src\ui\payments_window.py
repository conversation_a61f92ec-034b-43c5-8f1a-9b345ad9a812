# -*- coding: utf-8 -*-
"""
نافذة إدارة المدفوعات الشهرية
Monthly Payments Management Window
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                            QComboBox, QMessageBox, QFrame, QGroupBox, QFormLayout,
                            QHeaderView, QAbstractItemView, QSplitter, QDateEdit,
                            QDoubleSpinBox, QTextEdit, QSpinBox)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTime
from PyQt5.QtGui import QFont
from datetime import datetime, date

from ..models.student import Student
from ..models.groups import Groups
from ..database.database_manager import DatabaseManager
from ..utils.styles import get_form_style, get_table_style, get_arabic_font_style
from ..utils.message_boxes import show_error_message, show_success_message, show_warning_message, show_confirmation_dialog

class PaymentsWindow(QWidget):
    """نافذة إدارة المدفوعات الشهرية"""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.student_model = Student(db_manager)
        self.groups_model = Groups(db_manager)
        
        # إعدادات الأسعار الافتراضية
        self.default_geography_fee = 100.0
        self.default_history_fee = 100.0
        
        self.init_ui()
        self.load_students()
        self.load_payments()
        self.load_settings()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة المدفوعات الشهرية - نظام إدارة الطلاب")
        self.setGeometry(100, 100, 1400, 900)  # Increased window size
        
        # تحميل الخطوط العربية
        self.arabic_font = QFont("Arial", 10)
        self.arabic_font.setBold(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # شريط الإحصائيات العلوي
        stats_frame = self.create_stats_frame()
        main_layout.addWidget(stats_frame)
        
        # تقسيم الشاشة إلى قسمين: النموذج والجدول
        splitter = QSplitter(Qt.Vertical)
        
        # القسم العلوي: نموذج إدخال الدفعة
        form_group = QGroupBox("تسجيل دفعة جديدة")
        form_layout = QVBoxLayout()
        
        # نموذج البيانات
        form = QFormLayout()
        form.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)
        
        # حقل البحث عن الطالب
        self.student_search = QLineEdit()
        self.student_search.setPlaceholderText("ابحث بالاسم أو كود الطالب...")
        self.student_search.textChanged.connect(self.filter_students)
        form.addRow("بحث عن الطالب:", self.student_search)
        
        # اختيار الطالب
        self.student_combo = QComboBox()
        self.student_combo.setEditable(True)
        self.student_combo.currentIndexChanged.connect(self.on_student_selected)
        form.addRow("اختر الطالب:", self.student_combo)
        
        # معلومات الطالب
        student_info_layout = QHBoxLayout()
        
        # معلومات الطالب الأساسية
        info_group = QGroupBox("معلومات الطالب")
        info_layout = QFormLayout()
        
        self.student_code_label = QLabel("-")
        self.group_label = QLabel("-")
        
        info_layout.addRow("كود الطالب:", self.student_code_label)
        info_layout.addRow("المجموعة:", self.group_label)
        
        info_group.setLayout(info_layout)
        student_info_layout.addWidget(info_group)
        
        # معلومات الاتصال
        contact_group = QGroupBox("معلومات الاتصال")
        contact_layout = QFormLayout()
        
        self.student_phone_label = QLabel("-")
        self.guardian_phone_label = QLabel("-")
        
        contact_layout.addRow("هاتف الطالب:", self.student_phone_label)
        contact_layout.addRow("هاتف ولي الأمر:", self.guardian_phone_label)
        
        contact_group.setLayout(contact_layout)
        student_info_layout.addWidget(contact_group)
        
        form.addRow(student_info_layout)
        
        # تفاصيل الدفعة
        payment_group = QGroupBox("تفاصيل الدفعة")
        payment_layout = QFormLayout()
        
        # تاريخ الدفع
        self.payment_date = QDateEdit()
        self.payment_date.setCalendarPopup(True)
        self.payment_date.setDate(QDate.currentDate())
        self.payment_date.setDisplayFormat("dd/MM/yyyy")
        
        # حقول المدفوعات
        self.geography_fee_input = QDoubleSpinBox()
        self.geography_fee_input.setRange(0, 10000)
        self.geography_fee_input.setValue(self.default_geography_fee)
        self.geography_fee_input.setSuffix(" ج.م")
        self.geography_fee_input.valueChanged.connect(self.calculate_total)
        
        self.history_fee_input = QDoubleSpinBox()
        self.history_fee_input.setRange(0, 10000)
        self.history_fee_input.setValue(self.default_history_fee)
        self.history_fee_input.setSuffix(" ج.م")
        self.history_fee_input.valueChanged.connect(self.calculate_total)
        
        # ملء قائمة الشهر/السنة
        self.month_year_combo = QComboBox()
        self.populate_month_year_combo()
        
        # ملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(60)
        
        # إجمالي المبلغ
        self.total_label = QLabel("0.0 ج.م")
        self.total_label.setStyleSheet("font-weight: bold; color: #27ae60; font-size: 14px;")
        
        # إضافة الحقول إلى التخطيط
        payment_layout.addRow("تاريخ الدفع:", self.payment_date)
        payment_layout.addRow("مادة الجغرافيا:", self.geography_fee_input)
        payment_layout.addRow("مادة التاريخ:", self.history_fee_input)
        payment_layout.addRow("الشهر/السنة:", self.month_year_combo)
        payment_layout.addRow("الملاحظات:", self.notes_input)
        payment_layout.addRow("<b>الإجمالي:</b>", self.total_label)
        
        payment_group.setLayout(payment_layout)
        
        # أزرار التحكم
        button_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ الدفعة")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        self.save_btn.clicked.connect(self.save_payment)
        
        self.clear_btn = QPushButton("مسح النموذج")
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        self.clear_btn.clicked.connect(self.clear_form)
        
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.clear_btn)
        
        # إضافة كل شيء إلى التخطيط الرئيسي للنموذج
        form_layout.addLayout(form)
        form_layout.addWidget(payment_group)
        form_layout.addLayout(button_layout)
        
        form_group.setLayout(form_layout)
        
        # القسم السفلي: جدول المدفوعات
        table_group = QGroupBox("سجل المدفوعات")
        table_layout = QVBoxLayout()
        
        # شريط البحث والفلترة
        filter_layout = QHBoxLayout()
        
        # فلتر المجموعة
        self.group_filter = QComboBox()
        self.group_filter.addItem("كل المجموعات")
        self.group_filter.currentTextChanged.connect(self.filter_payments)
        
        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالاسم، الكود، أو المجموعة...")
        self.search_input.textChanged.connect(self.search_payments)
        
        # فلتر الشهر
        self.month_filter = QComboBox()
        self.month_filter.addItem("كل الشهور")
        self.populate_month_filter()
        self.month_filter.currentTextChanged.connect(self.filter_payments)
        
        # زر تحديث البيانات
        refresh_btn = QPushButton("تحديث")
        refresh_btn.clicked.connect(self.load_payments)
        
        filter_layout.addWidget(QLabel("المجموعة:"))
        filter_layout.addWidget(self.group_filter)
        filter_layout.addWidget(QLabel("الشهر:"))
        filter_layout.addWidget(self.month_filter)
        filter_layout.addWidget(self.search_input)
        filter_layout.addWidget(refresh_btn)
        
        # جدول المدفوعات
        self.payments_table = QTableWidget()
        self.payments_table.setColumnCount(11)
        self.payments_table.setHorizontalHeaderLabels([
            "الكود", "الاسم", "المجموعة", "هاتف الطالب", "هاتف ولي الأمر",
            "جغرافيا", "تاريخ", "الإجمالي", "التاريخ", "الشهر/السنة", "إجراءات"
        ])
        
        # تحسين مظهر الجدول
        header = self.payments_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # الكود
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # الاسم
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # المجموعة
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # هاتف الطالب
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # هاتف ولي الأمر
        header.setSectionResizeMode(10, QHeaderView.ResizeToContents)  # الإجراءات
        
        self.payments_table.setAlternatingRowColors(True)
        self.payments_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        
        # إضافة العناصر إلى تخطيط الجدول
        table_layout.addLayout(filter_layout)
        table_layout.addWidget(self.payments_table)
        
        table_group.setLayout(table_layout)
        
        # إضافة الأقسام إلى المقسم
        splitter.addWidget(form_group)
        splitter.addWidget(table_group)
        
        # تحديد نسب المقسم
        splitter.setSizes([350, 500])
        
        # إضافة المقسم إلى التخطيط الرئيسي
        main_layout.addWidget(splitter)
        
        # تعيين التخطيط الرئيسي للنافذة
        self.setLayout(main_layout)
        
        # تحميل الإعدادات
        self.load_settings()
        
        # تحميل البيانات
        self.load_students()
        self.load_payments()
        self.populate_group_filter()
        
    def create_stats_frame(self):
        """إنشاء إطار الإحصائيات"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #3498db, stop:1 #2980b9);
                border-radius: 10px;
                margin: 5px;
                padding: 10px;
            }
            QLabel {
                color: white;
                font-weight: bold;
                background: transparent;
            }
        """)
        
        layout = QHBoxLayout()
        
        # إجمالي المدفوعات اليوم
        self.today_total_label = QLabel("إجمالي اليوم: 0 جنيه")
        self.today_total_label.setStyleSheet("font-size: 16px;")
        layout.addWidget(self.today_total_label)
        
        # إجمالي المدفوعات الشهر
        self.month_total_label = QLabel("إجمالي الشهر: 0 جنيه")
        self.month_total_label.setStyleSheet("font-size: 16px;")
        layout.addWidget(self.month_total_label)
        
        # عدد الطلاب الذين دفعوا اليوم
        self.today_students_label = QLabel("طلاب اليوم: 0")
        self.today_students_label.setStyleSheet("font-size: 16px;")
        layout.addWidget(self.today_students_label)
        
        # عدد الطلاب الذين دفعوا هذا الشهر
        self.month_students_label = QLabel("طلاب الشهر: 0")
        self.month_students_label.setStyleSheet("font-size: 16px;")
        layout.addWidget(self.month_students_label)
        
        frame.setLayout(layout)
        return frame
    
    def load_students(self):
        """تحميل قائمة الطلاب"""
        try:
            self.student_combo.clear()
            self.student_combo.addItem("اختر الطالب...", None)
            
            # جلب قائمة الطلاب مع معلومات المجموعة
            query = """
            SELECT s.*, g.name as group_name 
            FROM students s
            LEFT JOIN groups g ON s.group_id = g.id
            WHERE s.is_active = 1
            ORDER BY s.full_name
            """
            
            students = self.db_manager.fetch_all(query)
            
            for student in students:
                display_text = f"{student['full_name']} ({student['student_code']})"
                self.student_combo.addItem(display_text, student)
                
        except Exception as e:
            show_error_message(self, "خطأ", f"فشل في تحميل قائمة الطلاب: {str(e)}")
    
    def filter_students(self):
        """تصفية قائمة الطلاب حسب البحث"""
        search_text = self.student_search.text().strip().lower()
        
        if not search_text:
            # إظهار كل الطلاب إذا كان حقل البحث فارغاً
            for i in range(1, self.student_combo.count()):
                self.student_combo.setItemData(i, self.student_combo.itemData(i), Qt.UserRole)
            return
            
        # تصفية الطلاب حسب نص البحث
        for i in range(1, self.student_combo.count()):
            student = self.student_combo.itemData(i)
            if student:
                match = (search_text in student['full_name'].lower() or 
                        search_text in student['student_code'].lower() or
                        search_text in student.get('phone', '').lower() or
                        search_text in student.get('guardian_phone', '').lower())
                self.student_combo.setItemData(i, student if match else None, Qt.UserRole)
    
    def load_payments(self):
        """تحميل المدفوعات"""
        try:
            # مسح الجدول
            self.payments_table.setRowCount(0)
            
            # بناء الاستعلام لجلب المدفوعات مع معلومات الطلاب
            query = """
            SELECT p.*, s.full_name, s.phone, s.guardian_phone, g.name as group_name
            FROM monthly_payments p
            JOIN students s ON p.student_id = s.id
            LEFT JOIN groups g ON s.group_id = g.id
            ORDER BY p.payment_date DESC, p.created_at DESC
            """
            
            payments = self.db_manager.fetch_all(query)
            
            for payment in payments:
                self.add_payment_to_table(payment)
                
            # تحديث الإحصائيات
            self.update_statistics()
            
        except Exception as e:
            show_error_message(self, "خطأ", f"فشل في تحميل المدفوعات: {str(e)}")
    
    def add_payment_to_table(self, payment):
        """إضافة دفعة إلى الجدول"""
        try:
            row = self.payments_table.rowCount()
            self.payments_table.insertRow(row)
            
            # إضافة البيانات إلى الصف
            self.payments_table.setItem(row, 0, QTableWidgetItem(payment.get('student_code', '')))
            self.payments_table.setItem(row, 1, QTableWidgetItem(payment.get('full_name', '')))
            self.payments_table.setItem(row, 2, QTableWidgetItem(payment.get('group_name', 'لا توجد مجموعة')))
            self.payments_table.setItem(row, 3, QTableWidgetItem(payment.get('phone', 'لا يوجد')))
            self.payments_table.setItem(row, 4, QTableWidgetItem(payment.get('guardian_phone', 'لا يوجد')))
            self.payments_table.setItem(row, 5, QTableWidgetItem(f"{payment.get('geography_fee', 0):.1f}"))
            self.payments_table.setItem(row, 6, QTableWidgetItem(f"{payment.get('history_fee', 0):.1f}"))
            self.payments_table.setItem(row, 7, QTableWidgetItem(f"{payment.get('total_amount', 0):.1f}"))
            
            # تنسيق التاريخ والوقت
            payment_date = payment.get('payment_date', '')
            if isinstance(payment_date, str):
                payment_date = payment_date.split()[0] if ' ' in payment_date else payment_date
            self.payments_table.setItem(row, 8, QTableWidgetItem(str(payment_date)))
            
            self.payments_table.setItem(row, 9, QTableWidgetItem(payment.get('month_year', '')))
            
            # إضافة أزرار الإجراءات
            actions_widget = QWidget()
            actions_layout = QHBoxLayout()
            actions_layout.setContentsMargins(5, 2, 5, 2)
            
            # زر حذف الدفعة
            delete_btn = QPushButton("حذف")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 3px 8px;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            delete_btn.clicked.connect(lambda _, pid=payment['id']: self.delete_payment(pid))
            
            actions_layout.addWidget(delete_btn)
            actions_widget.setLayout(actions_layout)
            
            self.payments_table.setCellWidget(row, 10, actions_widget)
            
        except Exception as e:
            print(f"Error adding payment to table: {str(e)}")
    
    def search_payments(self):
        """البحث في المدفوعات"""
        search_text = self.search_input.text().strip().lower()
        
        for row in range(self.payments_table.rowCount()):
            match_found = False
            
            # البحث في الأعمدة النصية (الكود، الاسم، المجموعة، الهواتف)
            for col in range(5):  # الأعمدة من 0 إلى 4 تحتوي على بيانات نصية
                item = self.payments_table.item(row, col)
                if item and search_text in item.text().lower():
                    match_found = True
                    break
            
            # إظهار/إخفاء الصفوف بناءً على نتيجة البحث
            self.payments_table.setRowHidden(row, not match_found)
    
    def populate_month_year_combo(self):
        """ملء قائمة الشهر/السنة"""
        current_date = datetime.now()
        current_month = current_date.month
        current_year = current_date.year

        months = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]

        # إضافة الشهر الحالي والشهور القادمة
        for i in range(12):
            month_num = ((current_month - 1 + i) % 12) + 1
            year = current_year + ((current_month - 1 + i) // 12)
            month_name = months[month_num - 1]
            self.month_year_combo.addItem(f"{month_name} {year}")

        # تحديد الشهر الحالي كافتراضي
        current_month_name = months[current_month - 1]
        self.month_year_combo.setCurrentText(f"{current_month_name} {current_year}")

    def populate_month_filter(self):
        """ملء فلتر الشهور"""
        try:
            query = "SELECT DISTINCT month_year FROM monthly_payments ORDER BY created_at DESC"
            result = self.db_manager.execute_query_with_fetch(query, fetch=True)

            for row in result:
                if row[0]:
                    self.month_filter_combo.addItem(row[0])
        except Exception as e:
            print(f"خطأ في تحميل فلتر الشهور: {e}")

    def on_student_selected(self):
        """عند اختيار طالب"""
        current_data = self.student_combo.currentData()
        if current_data:
            self.student_code_label.setText(current_data['student_code'])
            group_name = current_data.get('group_name', 'لا توجد مجموعة')
            self.group_label.setText(group_name)
            
            # عرض أرقام الهواتف
            student_phone = current_data.get('phone', 'لا يوجد')
            guardian_phone = current_data.get('guardian_phone', 'لا يوجد')
            
            self.student_phone_label.setText(student_phone)
            self.guardian_phone_label.setText(guardian_phone)
        else:
            self.student_code_label.setText("-")
            self.group_label.setText("-")
            self.student_phone_label.setText("-")
            self.guardian_phone_label.setText("-")

    def calculate_total(self):
        """حساب الإجمالي تلقائياً"""
        geography_fee = self.geography_fee_input.value()
        history_fee = self.history_fee_input.value()
        total = geography_fee + history_fee
        self.total_label.setText(f"{total:.1f} ج.م")

    def save_payment(self):
        """حفظ الدفعة"""
        # التحقق من البيانات
        if self.student_combo.currentIndex() == 0:
            show_warning_message(self, "تحذير", "يرجى اختيار الطالب")
            return

        student_data = self.student_combo.currentData()
        if not student_data:
            show_warning_message(self, "تحذير", "بيانات الطالب غير صحيحة")
            return

        # جمع البيانات
        geography_fee = self.geography_fee_input.value()
        history_fee = self.history_fee_input.value()
        total_amount = geography_fee + history_fee

        if total_amount <= 0:
            show_warning_message(self, "تحذير", "يجب أن يكون المبلغ أكبر من صفر")
            return

        payment_date = self.payment_date.date().toString("yyyy-MM-dd")
        payment_time = datetime.now().strftime("%H:%M:%S")
        month_year = self.month_year_combo.currentText()
        notes = self.notes_input.toPlainText().strip()

        try:
            # إدراج الدفعة في قاعدة البيانات
            query = """
            INSERT INTO monthly_payments
            (student_id, student_code, student_name, group_name, geography_fee,
             history_fee, total_amount, payment_date, payment_time, month_year, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            params = (
                student_data['id'],
                student_data['student_code'],
                student_data['full_name'],
                student_data.get('group_name', 'لا توجد مجموعة'),
                geography_fee,
                history_fee,
                total_amount,
                payment_date,
                payment_time,
                month_year,
                notes
            )

            self.db_manager.execute_query(query, params)

            show_success_message(self, "نجح", f"تم تسجيل دفعة {total_amount:.1f} ج.م بنجاح")

            # إعادة تحميل البيانات
            self.load_payments()
            self.update_statistics()
            self.clear_form()

        except Exception as e:
            show_error_message(self, "خطأ", f"فشل في حفظ الدفعة: {str(e)}")

    def clear_form(self):
        """مسح النموذج"""
        self.student_combo.setCurrentIndex(0)
        self.student_code_label.setText("-")
        self.group_label.setText("-")
        self.student_phone_label.setText("-")
        self.guardian_phone_label.setText("-")
        self.payment_date.setDate(QDate.currentDate())
        self.geography_fee_input.setValue(self.default_geography_fee)
        self.history_fee_input.setValue(self.default_history_fee)
        self.notes_input.clear()
        self.calculate_total()

    def delete_payment(self, payment_id):
        """حذف دفعة"""
        if show_confirmation_dialog(self, "تأكيد الحذف", "هل تريد حذف هذه الدفعة؟"):
            try:
                query = "DELETE FROM monthly_payments WHERE id = ?"
                self.db_manager.execute_query(query, (payment_id,))

                show_success_message(self, "نجح", "تم حذف الدفعة بنجاح")
                self.load_payments()

            except Exception as e:
                show_error_message(self, "خطأ", f"فشل في حذف الدفعة: {str(e)}")

    def filter_payments(self):
        """فلترة المدفوعات حسب المجموعة والشهر"""
        selected_group = self.group_filter.currentText()
        selected_month = self.month_filter.currentText()

        for row in range(self.payments_table.rowCount()):
            show_row = True

            # فلترة حسب المجموعة
            if selected_group != "كل المجموعات":
                group_item = self.payments_table.item(row, 2)  # عمود المجموعة
                if group_item:
                    if group_item.text() != selected_group:
                        show_row = False

            # فلترة حسب الشهر
            if show_row and selected_month != "كل الشهور":
                month_item = self.payments_table.item(row, 9)  # عمود الشهر/السنة
                if month_item:
                    if month_item.text() != selected_month:
                        show_row = False

            self.payments_table.setRowHidden(row, not show_row)

    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            today = date.today().strftime("%Y-%m-%d")
            current_month = datetime.now().strftime("%B %Y")

            # إجمالي اليوم
            query_today = "SELECT SUM(total_amount), COUNT(*) FROM monthly_payments WHERE payment_date = ?"
            result_today = self.db_manager.execute_query_with_fetch(query_today, (today,), fetch=True)

            today_total = result_today[0][0] if result_today[0][0] else 0
            today_count = result_today[0][1] if result_today[0][1] else 0

            # إجمالي الشهر
            query_month = "SELECT SUM(total_amount), COUNT(*) FROM monthly_payments WHERE month_year LIKE ?"
            month_pattern = f"%{datetime.now().strftime('%Y')}%"
            result_month = self.db_manager.execute_query_with_fetch(query_month, (month_pattern,), fetch=True)

            month_total = result_month[0][0] if result_month[0][0] else 0
            month_count = result_month[0][1] if result_month[0][1] else 0

            # تحديث التسميات
            self.today_total_label.setText(f"إجمالي اليوم: {today_total:.1f} ج.م")
            self.today_students_label.setText(f"طلاب اليوم: {today_count}")
            self.month_total_label.setText(f"إجمالي الشهر: {month_total:.1f} ج.م")
            self.month_students_label.setText(f"طلاب الشهر: {month_count}")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def save_settings(self):
        """حفظ إعدادات الأسعار"""
        try:
            self.default_geography_fee = self.default_geo_input.value()
            self.default_history_fee = self.default_hist_input.value()

            # حفظ في قاعدة البيانات
            queries = [
                ("INSERT OR REPLACE INTO settings (setting_key, setting_value) VALUES (?, ?)",
                 ("default_geography_fee", str(self.default_geography_fee))),
                ("INSERT OR REPLACE INTO settings (setting_key, setting_value) VALUES (?, ?)",
                 ("default_history_fee", str(self.default_history_fee)))
            ]

            for query, params in queries:
                self.db_manager.execute_query(query, params)

            # تحديث النموذج
            self.geography_fee_input.setValue(self.default_geography_fee)
            self.history_fee_input.setValue(self.default_history_fee)
            self.calculate_total()

            show_success_message(self, "نجح", "تم حفظ إعدادات الأسعار بنجاح")

        except Exception as e:
            show_error_message(self, "خطأ", f"فشل في حفظ الإعدادات: {str(e)}")

    def load_settings(self):
        """تحميل إعدادات الأسعار"""
        try:
            # تحميل سعر الجغرافيا
            query = "SELECT setting_value FROM settings WHERE setting_key = ?"
            result = self.db_manager.execute_query_with_fetch(query, ("default_geography_fee",), fetch=True)
            if result:
                self.default_geography_fee = float(result[0][0])
                self.default_geo_input.setValue(self.default_geography_fee)
                self.geography_fee_input.setValue(self.default_geography_fee)

            # تحميل سعر التاريخ
            result = self.db_manager.execute_query_with_fetch(query, ("default_history_fee",), fetch=True)
            if result:
                self.default_history_fee = float(result[0][0])
                self.default_hist_input.setValue(self.default_history_fee)
                self.history_fee_input.setValue(self.default_history_fee)

            self.calculate_total()

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def populate_group_filter(self):
        """تعبئة قائمة فلتر المجموعات"""
        try:
            groups = self.groups_model.get_group_names()
            self.group_filter_combo.clear()
            self.group_filter_combo.addItem("كل المجموعات")
            for group in groups:
                self.group_filter_combo.addItem(group)
        except Exception as e:
            print(f"خطأ في تحميل المجموعات: {e}")

    def showEvent(self, event):
        """عند إظهار النافذة"""
        super().showEvent(event)
        self.load_students()
        self.load_payments()
        self.populate_group_filter()
