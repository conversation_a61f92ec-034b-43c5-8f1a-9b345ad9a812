@echo off
chcp 65001 >nul
title نظام إدارة الطلاب - تشغيل مع الإصلاح

cls
color 0F
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة الطلاب                        ║
echo ║              تشغيل مع إصلاح قاعدة البيانات                 ║
echo ║                   نظام إدارة الطلاب المتطور                           ║
echo ║              نظام شامل لإدارة الطلاب                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 سيتم إصلاح قاعدة البيانات أولاً...
echo.

REM التحقق من وجود Python
py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python!
    echo.
    echo 🔧 تشغيل أداة إصلاح قاعدة البيانات...
    py fix_database.py
    echo.
    echo 🚀 تشغيل نظام إدارة الطلاب...
    echo.
    py main.py
    goto end
)

python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python!
    echo.
    echo 🔧 تشغيل أداة إصلاح قاعدة البيانات...
    python fix_database.py
    echo.
    echo 🚀 تشغيل نظام إدارة الطلاب...
    echo.
    python main.py
    goto end
)

python3 --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python3!
    echo.
    echo 🔧 تشغيل أداة إصلاح قاعدة البيانات...
    python3 fix_database.py
    echo.
    echo 🚀 تشغيل نظام إدارة الطلاب...
    echo.
    python3 main.py
    goto end
)

echo ❌ لم يتم العثور على Python!
echo.
echo 💡 يرجى تثبيت Python أولاً من:
echo https://python.org/downloads
echo.
start https://python.org/downloads

:end
echo.
echo 🎉 شكراً لاستخدام نظام إدارة الطلاب!
echo.
pause
