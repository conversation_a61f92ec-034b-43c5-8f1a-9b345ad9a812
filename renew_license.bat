@echo off
title تجديد ترخيص نظام إدارة المدارس

echo ===================================================
echo    تجديد الترخيص الشهري - School Management System
echo    الإصدار 1.0.0
echo ===================================================

:: Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: Python غير مثبت أو غير مضاف إلى متغيرات النظام.
    echo الرجاء تثبيت Python 3.8 أو أحدث من الموقع الرسمي.
    pause
    exit /b 1
)

:: Run the license renewal tool
echo.
echo جاري تشغيل أداة تجديد الترخيص...
python -m src.utils.license_renewal

:: Show result
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم تنفيذ العملية بنجاح!
) else (
    echo.
    echo ❌ حدث خطأ أثناء تنفيذ الأداة.
)

echo.
pause
