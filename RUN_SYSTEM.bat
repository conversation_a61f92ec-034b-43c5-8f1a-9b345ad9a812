@echo off
chcp 65001 >nul 2>&1
title نظام إدارة الطلاب المتطور

REM الانتقال إلى مجلد البرنامج
cd /d "%~dp0"

REM تشغيل البرنامج مباشرة
if exist "main.py" (
    REM محاولة التشغيل بـ pythonw (الأفضل للتطبيقات الرسومية)
    pythonw main.py && exit /b 0
    
    REM إذا فشل pythonw، جرب py launcher
    py main.py && exit /b 0
    
    REM إذا فشل py، جرب python عادي
    python main.py && exit /b 0
    
    REM إذا فشل python، جرب python3
    python3 main.py && exit /b 0
    
    REM إذا فشلت جميع الطرق
    cls
    echo.
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║                        خطأ في التشغيل                       ║
    echo ╚══════════════════════════════════════════════════════════════╝
    echo.
    echo ❌ فشل في تشغيل نظام إدارة الطلاب
    echo.
    echo 💡 الحلول المقترحة:
    echo    1. تثبيت Python من Microsoft Store
    echo    2. تثبيت Python من python.org
    echo    3. إعادة تشغيل الكمبيوتر
    echo    4. تشغيل الملف كمدير
    echo.
    echo 📞 للدعم الفني: 01225396729
    echo 📧 فيسبوك: H - TECH
    echo.
    pause
) else (
    echo ❌ خطأ: ملف main.py غير موجود
    echo تأكد من وجود جميع ملفات البرنامج
    pause
)

exit
