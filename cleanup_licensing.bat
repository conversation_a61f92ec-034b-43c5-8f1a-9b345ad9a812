@echo off
title تنظيف ملفات الترخيص غير الضرورية

echo ===================================================
echo    تنظيف ملفات الترخيص والملفات غير الضرورية
echo    School Management System
echo ===================================================

echo.
echo ⚠️ هذا الإجراء سيقوم بحذف جميع ملفات الترخيص والملفات غير الضرورية
echo.
pause

:: حذف ملفات الترخيص
echo.
echo حذف ملفات الترخيص...
if exist "SMS_Package\src\licensing" rmdir /s /q "SMS_Package\src\licensing"
if exist "SMS_Package\src\ui\license_*.py" del /q "SMS_Package\src\ui\license_*.py"
if exist "SMS_Package\src\utils\license_*.py" del /q "SMS_Package\src\utils\license_*.py"
if exist "SMS_Package\test_license.py" del /q "SMS_Package\test_license.py"

if exist "src\licensing" rmdir /s /q "src\licensing"
if exist "src\ui\license_*.py" del /q "src\ui\license_*.py"
if exist "src\utils\license_*.py" del /q "src\utils\license_*.py"
if exist "test_license.py" del /q "test_license.py"

:: حذف الملفات المؤقتة
echo.
echo حذف الملفات المؤقتة...
del /q *.pyc 2>nul
del /q *.pyo 2>nul
del /q *.pyd 2>nul
del /q *.py~ 2>nul
del /q *.bak 2>nul
del /q *.log 2>nul

:: حذف مجلد __pycache__
for /d /r . %%d in (__pycache__) do if exist "%%d" rmdir /s /q "%%d"

echo.
echo ✅ تم تنظيف الملفات بنجاح!
echo.
pause
