@echo off
chcp 65001 >nul
title نظام إدارة الطلاب المتطور

REM إخفاء النافذة والتشغيل المباشر
if not DEFINED IS_MINIMIZED set IS_MINIMIZED=1 && start "" /min "%~dpnx0" %* && exit

REM التشغيل المباشر بدون رسائل
cd /d "%~dp0"

REM محاولة تشغيل البرنامج بطرق مختلفة
py --version >nul 2>&1
if %errorlevel% equ 0 (
    py main.py >nul 2>&1
    if %errorlevel% equ 0 exit
)

python --version >nul 2>&1
if %errorlevel% equ 0 (
    python main.py >nul 2>&1
    if %errorlevel% equ 0 exit
)

python3 --version >nul 2>&1
if %errorlevel% equ 0 (
    python3 main.py >nul 2>&1
    if %errorlevel% equ 0 exit
)

REM إذا فشل التشغيل، عرض رسالة خطأ
echo خطأ: لم يتم العثور على Python أو فشل في تشغيل البرنامج
echo يرجى التأكد من تثبيت Python بشكل صحيح
pause
exit
