@echo off
title اختبار تسجيل الدخول

echo ===================================================
echo    اختبار نظام تسجيل الدخول - School Management System
echo    الإصدار 1.0.0
echo ===================================================

:: Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: Python غير مثبت أو غير مضاف إلى متغيرات النظام.
    echo الرجاء تثبيت Python 3.8 أو أحدث من الموقع الرسمي.
    pause
    exit /b 1
)

:: Install required packages if not installed
python -m pip install --upgrade pip
pip install -r requirements.txt

:: Run the test script
echo.
echo جاري تشغيل اختبارات تسجيل الدخول...
python test_login.py

:: Show result
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم تنفيذ جميع الاختبارات بنجاح!
) else (
    echo.
    echo ❌ فشل بعض الاختبارات. الرجاء مراجعة الرسائل أعلاه.
)

echo.
pause
