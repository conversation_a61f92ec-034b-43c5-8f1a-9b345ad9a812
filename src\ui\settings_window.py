# -*- coding: utf-8 -*-
"""
نافذة الإعدادات
Settings Window
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QComboBox, QMessageBox,
                            QFrame, QGroupBox, QFormLayout, QTabWidget,
                            QCheckBox, QSpinBox, QTextEdit, QFileDialog,
                            QListWidget, QListWidgetItem, QScrollArea)
from PyQt5.QtCore import Qt, QUrl
from PyQt5.QtGui import QFont, QDesktopServices
import webbrowser
import json
import os

from ..database.database_manager import DatabaseManager
from ..utils.auth import AuthManager

class SettingsWindow(QWidget):
    """نافذة الإعدادات"""
    
    def __init__(self, db_manager: DatabaseManager, auth_manager: AuthManager):
        super().__init__()
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        
        self.init_ui()
        self.setup_styles()
        self.load_settings()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إعدادات التطبيق")
        self.setGeometry(100, 100, 800, 600)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # التبويبات
        tabs = QTabWidget()
        
        # تبويب الإعدادات العامة
        general_tab = self.create_general_tab()
        tabs.addTab(general_tab, "الإعدادات العامة")
        
        # تبويب إعدادات المعلم
        teacher_tab = self.create_teacher_tab()
        tabs.addTab(teacher_tab, "إعدادات المعلم")
        
        # تبويب إعدادات قاعدة البيانات
        database_tab = self.create_database_tab()
        tabs.addTab(database_tab, "قاعدة البيانات")

        # تبويب إعدادات المواد
        subjects_tab = self.create_subjects_tab()
        tabs.addTab(subjects_tab, "إعدادات المواد")

        # تبويب معلومات المطور
        developer_tab = self.create_developer_tab()
        tabs.addTab(developer_tab, "معلومات المطور")
        
        # تبويب إعدادات الأمان
        security_tab = self.create_security_tab()
        tabs.addTab(security_tab, "الأمان")

        # تبويب حول التطبيق
        about_tab = self.create_about_tab()
        tabs.addTab(about_tab, "حول التطبيق")

        main_layout.addWidget(tabs)
        
        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ الإعدادات")
        self.save_button.clicked.connect(self.save_settings)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.close)
        
        self.reset_button = QPushButton("استعادة الافتراضي")
        self.reset_button.clicked.connect(self.reset_to_defaults)
        
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.reset_button)
        
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
    
    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات المدرسة
        school_group = QGroupBox("معلومات المدرسة")
        school_layout = QFormLayout()
        
        self.school_name_input = QLineEdit()
        self.school_name_input.setPlaceholderText("أدخل اسم المدرسة")
        school_layout.addRow("اسم المدرسة:", self.school_name_input)
        
        self.current_year_input = QLineEdit()
        self.current_year_input.setPlaceholderText("مثال: 2024-2025")
        school_layout.addRow("العام الدراسي:", self.current_year_input)
        
        school_group.setLayout(school_layout)
        layout.addWidget(school_group)
        
        # إعدادات التطبيق
        app_group = QGroupBox("إعدادات التطبيق")
        app_layout = QFormLayout()
        
        self.language_combo = QComboBox()
        self.language_combo.addItems(["العربية", "English"])
        app_layout.addRow("اللغة:", self.language_combo)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["فاتح", "داكن", "تلقائي"])
        app_layout.addRow("المظهر:", self.theme_combo)
        
        self.auto_backup_checkbox = QCheckBox("النسخ الاحتياطي التلقائي")
        app_layout.addRow("", self.auto_backup_checkbox)
        
        self.backup_interval_spinbox = QSpinBox()
        self.backup_interval_spinbox.setRange(1, 30)
        self.backup_interval_spinbox.setValue(7)
        self.backup_interval_spinbox.setSuffix(" أيام")
        app_layout.addRow("فترة النسخ الاحتياطي:", self.backup_interval_spinbox)
        
        app_group.setLayout(app_layout)
        layout.addWidget(app_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def create_teacher_tab(self):
        """إنشاء تبويب إعدادات المعلم"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # معلومات المعلم
        teacher_group = QGroupBox("معلومات المعلم")
        teacher_layout = QFormLayout()
        
        self.teacher_name_input = QLineEdit()
        self.teacher_name_input.setPlaceholderText("أدخل اسم المعلم")
        teacher_layout.addRow("اسم المعلم:", self.teacher_name_input)
        
        self.teacher_email_input = QLineEdit()
        self.teacher_email_input.setPlaceholderText("البريد الإلكتروني (اختياري)")
        teacher_layout.addRow("البريد الإلكتروني:", self.teacher_email_input)
        
        self.teacher_phone_input = QLineEdit()
        self.teacher_phone_input.setPlaceholderText("رقم الهاتف (اختياري)")
        teacher_layout.addRow("رقم الهاتف:", self.teacher_phone_input)
        
        teacher_group.setLayout(teacher_layout)
        layout.addWidget(teacher_group)
        
        # إعدادات المواد
        subjects_group = QGroupBox("إعدادات المواد")
        subjects_layout = QFormLayout()
        
        self.geography_enabled_checkbox = QCheckBox("تفعيل مادة الجغرافيا")
        self.geography_enabled_checkbox.setChecked(True)
        subjects_layout.addRow("", self.geography_enabled_checkbox)
        
        self.history_enabled_checkbox = QCheckBox("تفعيل مادة التاريخ")
        self.history_enabled_checkbox.setChecked(True)
        subjects_layout.addRow("", self.history_enabled_checkbox)
        
        self.passing_grade_spinbox = QSpinBox()
        self.passing_grade_spinbox.setRange(30, 70)
        self.passing_grade_spinbox.setValue(50)
        self.passing_grade_spinbox.setSuffix("%")
        subjects_layout.addRow("درجة النجاح:", self.passing_grade_spinbox)
        
        subjects_group.setLayout(subjects_layout)
        layout.addWidget(subjects_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def create_database_tab(self):
        """إنشاء تبويب إعدادات قاعدة البيانات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # معلومات قاعدة البيانات
        db_info_group = QGroupBox("معلومات قاعدة البيانات")
        db_info_layout = QFormLayout()
        
        self.db_path_label = QLabel("جاري التحميل...")
        db_info_layout.addRow("مسار قاعدة البيانات:", self.db_path_label)
        
        self.db_size_label = QLabel("جاري الحساب...")
        db_info_layout.addRow("حجم قاعدة البيانات:", self.db_size_label)
        
        self.total_students_label = QLabel("0")
        db_info_layout.addRow("إجمالي الطلاب:", self.total_students_label)
        
        self.total_records_label = QLabel("0")
        db_info_layout.addRow("إجمالي السجلات:", self.total_records_label)
        
        db_info_group.setLayout(db_info_layout)
        layout.addWidget(db_info_group)
        
        # عمليات قاعدة البيانات
        db_operations_group = QGroupBox("عمليات قاعدة البيانات")
        db_operations_layout = QVBoxLayout()
        
        # النسخ الاحتياطي
        backup_layout = QHBoxLayout()
        self.backup_button = QPushButton("إنشاء نسخة احتياطية")
        self.backup_button.clicked.connect(self.create_backup)
        
        self.restore_button = QPushButton("استعادة من نسخة احتياطية")
        self.restore_button.clicked.connect(self.restore_backup)
        
        backup_layout.addWidget(self.backup_button)
        backup_layout.addWidget(self.restore_button)
        db_operations_layout.addLayout(backup_layout)
        
        # تنظيف قاعدة البيانات
        cleanup_layout = QHBoxLayout()
        self.optimize_button = QPushButton("تحسين قاعدة البيانات")
        self.optimize_button.clicked.connect(self.optimize_database)
        
        self.vacuum_button = QPushButton("ضغط قاعدة البيانات")
        self.vacuum_button.clicked.connect(self.vacuum_database)
        
        cleanup_layout.addWidget(self.optimize_button)
        cleanup_layout.addWidget(self.vacuum_button)
        db_operations_layout.addLayout(cleanup_layout)
        
        db_operations_group.setLayout(db_operations_layout)
        layout.addWidget(db_operations_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def create_security_tab(self):
        """إنشاء تبويب إعدادات الأمان"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # تغيير كلمة المرور
        password_group = QGroupBox("تغيير كلمة المرور")
        password_layout = QFormLayout()
        
        self.current_password_input = QLineEdit()
        self.current_password_input.setEchoMode(QLineEdit.Password)
        password_layout.addRow("كلمة المرور الحالية:", self.current_password_input)
        
        self.new_password_input = QLineEdit()
        self.new_password_input.setEchoMode(QLineEdit.Password)
        password_layout.addRow("كلمة المرور الجديدة:", self.new_password_input)
        
        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setEchoMode(QLineEdit.Password)
        password_layout.addRow("تأكيد كلمة المرور:", self.confirm_password_input)
        
        self.change_password_button = QPushButton("تغيير كلمة المرور")
        self.change_password_button.clicked.connect(self.change_password)
        password_layout.addRow("", self.change_password_button)
        
        password_group.setLayout(password_layout)
        layout.addWidget(password_group)
        
        # إعدادات الأمان
        security_group = QGroupBox("إعدادات الأمان")
        security_layout = QFormLayout()
        
        self.auto_logout_checkbox = QCheckBox("تسجيل خروج تلقائي")
        security_layout.addRow("", self.auto_logout_checkbox)
        
        self.logout_timeout_spinbox = QSpinBox()
        self.logout_timeout_spinbox.setRange(5, 120)
        self.logout_timeout_spinbox.setValue(30)
        self.logout_timeout_spinbox.setSuffix(" دقيقة")
        security_layout.addRow("مهلة تسجيل الخروج:", self.logout_timeout_spinbox)
        
        self.remember_login_checkbox = QCheckBox("تذكر تسجيل الدخول")
        security_layout.addRow("", self.remember_login_checkbox)
        
        security_group.setLayout(security_layout)
        layout.addWidget(security_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def load_settings(self):
        """تحميل الإعدادات من قاعدة البيانات"""
        try:
            # تحميل الإعدادات العامة
            school_name = self.db_manager.get_setting('school_name') or "مدرسة النجاح"
            self.school_name_input.setText(school_name)

            current_year = self.db_manager.get_setting('current_year') or "2024-2025"
            self.current_year_input.setText(current_year)

            # تحميل إعدادات المعلم
            teacher_name = self.db_manager.get_setting('teacher_name') or "نظام إدارة الطلاب المتطور"
            self.teacher_name_input.setText(teacher_name)

            teacher_email = self.db_manager.get_setting('teacher_email') or ""
            self.teacher_email_input.setText(teacher_email)

            teacher_phone = self.db_manager.get_setting('teacher_phone') or ""
            self.teacher_phone_input.setText(teacher_phone)

            # تحميل معلومات قاعدة البيانات
            self.load_database_info()

        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"فشل في تحميل بعض الإعدادات: {str(e)}")

    def load_database_info(self):
        """تحميل معلومات قاعدة البيانات"""
        try:
            import os
            from ..models.student import Student

            # مسار قاعدة البيانات
            self.db_path_label.setText(self.db_manager.db_path)

            # حجم قاعدة البيانات
            if os.path.exists(self.db_manager.db_path):
                size_bytes = os.path.getsize(self.db_manager.db_path)
                size_mb = size_bytes / (1024 * 1024)
                self.db_size_label.setText(f"{size_mb:.2f} ميجابايت")
            else:
                self.db_size_label.setText("غير موجود")

            # إحصائيات
            student_model = Student(self.db_manager)
            stats = student_model.get_student_statistics()

            self.total_students_label.setText(str(stats.get('total_students', 0)))

            # حساب إجمالي السجلات
            total_records = self.db_manager.fetch_one("""
                SELECT
                    (SELECT COUNT(*) FROM students) +
                    (SELECT COUNT(*) FROM attendance) +
                    (SELECT COUNT(*) FROM grades) as total
            """)

            self.total_records_label.setText(str(total_records.get('total', 0) if total_records else 0))

        except Exception as e:
            print(f"خطأ في تحميل معلومات قاعدة البيانات: {e}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # حفظ الإعدادات العامة
            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            """, ('school_name', self.school_name_input.text(), 'اسم المدرسة'))

            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            """, ('current_year', self.current_year_input.text(), 'العام الدراسي الحالي'))

            # حفظ إعدادات المعلم
            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            """, ('teacher_name', self.teacher_name_input.text(), 'اسم المعلم'))

            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            """, ('teacher_email', self.teacher_email_input.text(), 'بريد المعلم الإلكتروني'))

            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            """, ('teacher_phone', self.teacher_phone_input.text(), 'هاتف المعلم'))

            # حفظ إعدادات المواد
            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            """, ('geography_enabled', str(self.geography_enabled_checkbox.isChecked()), 'تفعيل مادة الجغرافيا'))

            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            """, ('history_enabled', str(self.history_enabled_checkbox.isChecked()), 'تفعيل مادة التاريخ'))

            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            """, ('passing_grade', str(self.passing_grade_spinbox.value()), 'درجة النجاح'))

            QMessageBox.information(self, "تم", "تم حفظ الإعدادات بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات: {str(e)}")

    def reset_to_defaults(self):
        """استعادة الإعدادات الافتراضية"""
        reply = QMessageBox.question(
            self, 'تأكيد',
            'هل تريد استعادة جميع الإعدادات إلى القيم الافتراضية؟',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # استعادة القيم الافتراضية
            self.school_name_input.setText("مدرسة النجاح")
            self.current_year_input.setText("2024-2025")
            self.teacher_name_input.setText("نظام إدارة الطلاب المتطور")
            self.teacher_email_input.clear()
            self.teacher_phone_input.clear()
            self.geography_enabled_checkbox.setChecked(True)
            self.history_enabled_checkbox.setChecked(True)
            self.passing_grade_spinbox.setValue(50)
            self.language_combo.setCurrentIndex(0)
            self.theme_combo.setCurrentIndex(0)
            self.auto_backup_checkbox.setChecked(False)
            self.backup_interval_spinbox.setValue(7)

    def change_password(self):
        """تغيير كلمة المرور"""
        current_password = self.current_password_input.text()
        new_password = self.new_password_input.text()
        confirm_password = self.confirm_password_input.text()

        # التحقق من البيانات
        if not current_password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال كلمة المرور الحالية")
            return

        if not new_password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال كلمة المرور الجديدة")
            return

        if new_password != confirm_password:
            QMessageBox.warning(self, "خطأ", "كلمة المرور الجديدة وتأكيدها غير متطابقين")
            return

        if len(new_password) < 6:
            QMessageBox.warning(self, "خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
            return

        # تغيير كلمة المرور
        if self.auth_manager.change_password(current_password, new_password):
            QMessageBox.information(self, "تم", "تم تغيير كلمة المرور بنجاح")

            # مسح الحقول
            self.current_password_input.clear()
            self.new_password_input.clear()
            self.confirm_password_input.clear()
        else:
            QMessageBox.critical(self, "خطأ", "كلمة المرور الحالية غير صحيحة")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            import shutil
            from datetime import datetime

            # اختيار مكان الحفظ
            backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ النسخة الاحتياطية",
                backup_name,
                "Database Files (*.db)"
            )

            if file_path:
                # نسخ قاعدة البيانات
                shutil.copy2(self.db_manager.db_path, file_path)
                QMessageBox.information(self, "تم", f"تم إنشاء النسخة الاحتياطية في:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة من نسخة احتياطية"""
        reply = QMessageBox.warning(
            self, 'تحذير',
            'استعادة النسخة الاحتياطية ستحذف جميع البيانات الحالية!\nهل تريد المتابعة؟',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                import shutil

                # اختيار ملف النسخة الاحتياطية
                file_path, _ = QFileDialog.getOpenFileName(
                    self, "اختيار النسخة الاحتياطية",
                    "",
                    "Database Files (*.db)"
                )

                if file_path:
                    # إغلاق الاتصال الحالي
                    self.db_manager.disconnect()

                    # استعادة النسخة الاحتياطية
                    shutil.copy2(file_path, self.db_manager.db_path)

                    # إعادة الاتصال
                    self.db_manager.connect()

                    QMessageBox.information(self, "تم", "تم استعادة النسخة الاحتياطية بنجاح")

                    # تحديث المعلومات
                    self.load_database_info()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في استعادة النسخة الاحتياطية: {str(e)}")

    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            # تحليل قاعدة البيانات
            self.db_manager.execute_query("ANALYZE")
            QMessageBox.information(self, "تم", "تم تحسين قاعدة البيانات بنجاح")

            # تحديث المعلومات
            self.load_database_info()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحسين قاعدة البيانات: {str(e)}")

    def vacuum_database(self):
        """ضغط قاعدة البيانات"""
        try:
            # ضغط قاعدة البيانات
            self.db_manager.execute_query("VACUUM")
            QMessageBox.information(self, "تم", "تم ضغط قاعدة البيانات بنجاح")

            # تحديث المعلومات
            self.load_database_info()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في ضغط قاعدة البيانات: {str(e)}")

    def create_about_tab(self):
        """إنشاء تبويب حول التطبيق"""
        widget = QWidget()
        layout = QVBoxLayout()

        # معلومات التطبيق
        app_info_group = QGroupBox("معلومات التطبيق")
        app_info_layout = QVBoxLayout()

        # عنوان التطبيق
        title_label = QLabel("نظام إدارة الطلاب")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                text-align: center;
                margin: 10px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        app_info_layout.addWidget(title_label)

        # الإصدار
        version_label = QLabel("الإصدار 4.0 - الإصدار الكامل النهائي")
        version_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #34495e;
                text-align: center;
                margin: 5px;
            }
        """)
        version_label.setAlignment(Qt.AlignCenter)
        app_info_layout.addWidget(version_label)

        app_info_group.setLayout(app_info_layout)
        layout.addWidget(app_info_group)

        # معلومات المطور
        developer_group = QGroupBox("معلومات المطور")
        developer_layout = QVBoxLayout()

        # اسم المطور
        developer_name = QLabel("Eng / Hossam Osama")
        developer_name.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #27ae60;
                text-align: center;
                margin: 10px;
            }
        """)
        developer_name.setAlignment(Qt.AlignCenter)
        developer_layout.addWidget(developer_name)

        # التخصص
        specialty_label = QLabel("مهندس برمجيات - مطور تطبيقات")
        specialty_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #34495e;
                text-align: center;
                margin: 5px;
            }
        """)
        specialty_label.setAlignment(Qt.AlignCenter)
        developer_layout.addWidget(specialty_label)

        developer_group.setLayout(developer_layout)
        layout.addWidget(developer_group)

        # معلومات المستخدم المستهدف
        target_group = QGroupBox("مصمم خصيصاً لـ")
        target_layout = QVBoxLayout()

        target_label = QLabel("نظام إدارة الطلاب المتطور")
        target_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #e74c3c;
                text-align: center;
                margin: 10px;
            }
        """)
        target_label.setAlignment(Qt.AlignCenter)
        target_layout.addWidget(target_label)

        target_desc = QLabel("نظام شامل لإدارة الطلاب")
        target_desc.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #34495e;
                text-align: center;
                margin: 5px;
            }
        """)
        target_desc.setAlignment(Qt.AlignCenter)
        target_layout.addWidget(target_desc)

        target_group.setLayout(target_layout)
        layout.addWidget(target_group)

        # حقوق النشر
        copyright_label = QLabel("© 2024 - جميع الحقوق محفوظة")
        copyright_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #95a5a6;
                text-align: center;
                margin: 20px;
            }
        """)
        copyright_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(copyright_label)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def setup_styles(self):
        """تطبيق الأنماط"""
        style = """
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }

        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px;
            border-radius: 4px;
            font-weight: bold;
        }

        QPushButton:hover {
            background-color: #2980b9;
        }

        QPushButton:pressed {
            background-color: #21618c;
        }

        QLineEdit, QComboBox, QSpinBox {
            padding: 5px;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
        }

        QLineEdit:focus, QComboBox:focus, QSpinBox:focus {
            border-color: #3498db;
        }

        QTabWidget::pane {
            border: 1px solid #bdc3c7;
            background-color: white;
        }

        QTabBar::tab {
            background-color: #ecf0f1;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }

        QTabBar::tab:selected {
            background-color: white;
            border-bottom: 2px solid #3498db;
        }

        QTabBar::tab:hover {
            background-color: #d5dbdb;
        }
        """

        self.setStyleSheet(style)

    def create_subjects_tab(self):
        """إنشاء تبويب إعدادات المواد"""
        tab = QWidget()
        layout = QVBoxLayout()

        # عنوان التبويب
        title = QLabel("إعدادات المواد الدراسية")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 20px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # مجموعة المواد الحالية
        current_subjects_group = QGroupBox("المواد الحالية")
        current_subjects_layout = QVBoxLayout()

        # قائمة المواد
        self.subjects_list = QListWidget()
        self.subjects_list.setMinimumHeight(200)

        # تحميل المواد الحالية
        self.load_subjects()

        current_subjects_layout.addWidget(self.subjects_list)
        current_subjects_group.setLayout(current_subjects_layout)
        layout.addWidget(current_subjects_group)

        # مجموعة إضافة مادة جديدة
        add_subject_group = QGroupBox("إضافة مادة جديدة")
        add_subject_layout = QFormLayout()

        # حقل اسم المادة
        self.new_subject_name = QLineEdit()
        self.new_subject_name.setPlaceholderText("أدخل اسم المادة...")
        add_subject_layout.addRow("اسم المادة:", self.new_subject_name)

        # حقل وصف المادة
        self.new_subject_description = QLineEdit()
        self.new_subject_description.setPlaceholderText("وصف المادة (اختياري)...")
        add_subject_layout.addRow("الوصف:", self.new_subject_description)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        add_button = QPushButton("إضافة المادة")
        add_button.clicked.connect(self.add_subject)
        add_button.setStyleSheet("background-color: #27ae60; color: white; padding: 8px 16px; border-radius: 5px;")

        remove_button = QPushButton("حذف المادة المحددة")
        remove_button.clicked.connect(self.remove_subject)
        remove_button.setStyleSheet("background-color: #e74c3c; color: white; padding: 8px 16px; border-radius: 5px;")

        buttons_layout.addWidget(add_button)
        buttons_layout.addWidget(remove_button)

        add_subject_layout.addRow(buttons_layout)
        add_subject_group.setLayout(add_subject_layout)
        layout.addWidget(add_subject_group)

        # مجموعة المواد الافتراضية
        default_subjects_group = QGroupBox("المواد الافتراضية")
        default_subjects_layout = QVBoxLayout()

        default_subjects_text = QLabel("يمكنك إضافة المواد الافتراضية التالية:")
        default_subjects_layout.addWidget(default_subjects_text)

        default_subjects = [
            "الرياضيات", "العلوم", "اللغة العربية", "اللغة الإنجليزية",
            "التاريخ", "الجغرافيا", "الفيزياء", "الكيمياء", "الأحياء",
            "الحاسوب", "التربية الإسلامية", "التربية الفنية", "التربية الرياضية"
        ]

        default_buttons_layout = QHBoxLayout()
        for i, subject in enumerate(default_subjects):
            if i % 4 == 0:
                default_buttons_layout = QHBoxLayout()
                default_subjects_layout.addLayout(default_buttons_layout)

            btn = QPushButton(subject)
            btn.clicked.connect(lambda checked, s=subject: self.add_default_subject(s))
            btn.setStyleSheet("background-color: #3498db; color: white; padding: 5px 10px; border-radius: 3px; margin: 2px;")
            default_buttons_layout.addWidget(btn)

        default_subjects_group.setLayout(default_subjects_layout)
        layout.addWidget(default_subjects_group)

        tab.setLayout(layout)
        return tab

    def create_developer_tab(self):
        """إنشاء تبويب معلومات المطور"""
        tab = QWidget()
        layout = QVBoxLayout()

        # إنشاء منطقة قابلة للتمرير
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout()

        # عنوان التبويب
        title = QLabel("معلومات المطور")
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #2c3e50; margin-bottom: 20px;")
        title.setAlignment(Qt.AlignCenter)
        scroll_layout.addWidget(title)

        # معلومات المطور
        developer_info_group = QGroupBox("تم تطوير هذا النظام بواسطة")
        developer_info_layout = QVBoxLayout()

        # اسم المطور
        developer_name = QLabel("👨‍💻 Eng / Hossam Osama")
        developer_name.setStyleSheet("font-size: 20px; font-weight: bold; color: #3498db; margin: 10px;")
        developer_name.setAlignment(Qt.AlignCenter)
        developer_info_layout.addWidget(developer_name)

        # معلومات التواصل
        contact_info = QLabel("للتواصل والدعم الفني:")
        contact_info.setStyleSheet("font-size: 16px; color: #34495e; margin: 10px;")
        contact_info.setAlignment(Qt.AlignCenter)
        developer_info_layout.addWidget(contact_info)

        # أزرار التواصل
        contact_buttons_layout = QVBoxLayout()

        # زر واتساب
        whatsapp_layout = QHBoxLayout()
        whatsapp_label = QLabel("📱 تواصل مباشر على واتساب:")
        whatsapp_label.setStyleSheet("font-size: 14px; color: #2c3e50;")

        whatsapp_button = QPushButton("📲 اضغط هنا للتواصل")
        whatsapp_button.clicked.connect(self.open_whatsapp)
        whatsapp_button.setStyleSheet("""
            QPushButton {
                background-color: #25D366;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 8px;
                border: none;
            }
            QPushButton:hover {
                background-color: #128C7E;
            }
        """)

        phone_label = QLabel("📞 01225396729")
        phone_label.setStyleSheet("font-size: 14px; color: #2c3e50; font-weight: bold;")

        whatsapp_layout.addWidget(whatsapp_label)
        whatsapp_layout.addWidget(whatsapp_button)
        whatsapp_layout.addWidget(phone_label)
        contact_buttons_layout.addLayout(whatsapp_layout)

        # زر فيسبوك
        facebook_layout = QHBoxLayout()
        facebook_label = QLabel("📘 تابع صفحتنا على فيسبوك:")
        facebook_label.setStyleSheet("font-size: 14px; color: #2c3e50;")

        facebook_button = QPushButton("👉 H - TECH")
        facebook_button.clicked.connect(self.open_facebook)
        facebook_button.setStyleSheet("""
            QPushButton {
                background-color: #1877F2;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 8px;
                border: none;
            }
            QPushButton:hover {
                background-color: #166FE5;
            }
        """)

        facebook_layout.addWidget(facebook_label)
        facebook_layout.addWidget(facebook_button)
        facebook_layout.addStretch()
        contact_buttons_layout.addLayout(facebook_layout)

        developer_info_layout.addLayout(contact_buttons_layout)
        developer_info_group.setLayout(developer_info_layout)
        scroll_layout.addWidget(developer_info_group)

        # معلومات النظام
        system_info_group = QGroupBox("معلومات النظام")
        system_info_layout = QFormLayout()

        system_info_layout.addRow("اسم النظام:", QLabel("نظام إدارة الطلاب المتطور"))
        system_info_layout.addRow("الإصدار:", QLabel("2.0.0 (محسن)"))
        system_info_layout.addRow("تاريخ التطوير:", QLabel("2025"))
        system_info_layout.addRow("لغة البرمجة:", QLabel("Python + PyQt5"))
        system_info_layout.addRow("قاعدة البيانات:", QLabel("SQLite / PostgreSQL / MySQL"))

        system_info_group.setLayout(system_info_layout)
        scroll_layout.addWidget(system_info_group)

        # مميزات النظام
        features_group = QGroupBox("مميزات النظام")
        features_layout = QVBoxLayout()

        features_text = """
🎯 المميزات الرئيسية:
• إدارة شاملة للطلاب والدرجات والحضور
• واجهة ويب للوصول عن بُعد
• نظام إشعارات Telegram
• مزامنة سحابية تلقائية
• تقارير احترافية قابلة للطباعة
• نظام تحديث تلقائي
• دعم قواعد بيانات متعددة
• واجهة متجاوبة تعمل على جميع الأجهزة

🔧 التقنيات المستخدمة:
• Python 3.6+ مع PyQt5
• Flask للخادم الويب
• SQLAlchemy لقواعد البيانات
• Socket.IO للتحديثات المباشرة
• Bootstrap 5 للواجهة الويب
        """

        features_label = QLabel(features_text)
        features_label.setStyleSheet("font-size: 12px; color: #2c3e50; line-height: 1.5;")
        features_label.setWordWrap(True)
        features_layout.addWidget(features_label)

        features_group.setLayout(features_layout)
        scroll_layout.addWidget(features_group)

        # إعداد منطقة التمرير
        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)

        layout.addWidget(scroll_area)
        tab.setLayout(layout)
        return tab

    def load_subjects(self):
        """تحميل المواد من ملف الإعدادات"""
        try:
            subjects_file = "subjects_config.json"
            if os.path.exists(subjects_file):
                with open(subjects_file, 'r', encoding='utf-8') as f:
                    subjects_data = json.load(f)
                    subjects = subjects_data.get('subjects', [])
            else:
                # المواد الافتراضية
                subjects = [
                    {"name": "الجغرافيا", "description": "مادة الجغرافيا"},
                    {"name": "التاريخ", "description": "مادة التاريخ"}
                ]
                self.save_subjects(subjects)

            self.subjects_list.clear()
            for subject in subjects:
                item_text = f"{subject['name']}"
                if subject.get('description'):
                    item_text += f" - {subject['description']}"

                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, subject)
                self.subjects_list.addItem(item)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل المواد: {str(e)}")

    def save_subjects(self, subjects):
        """حفظ المواد في ملف الإعدادات"""
        try:
            subjects_data = {"subjects": subjects}
            with open("subjects_config.json", 'w', encoding='utf-8') as f:
                json.dump(subjects_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في حفظ المواد: {str(e)}")

    def add_subject(self):
        """إضافة مادة جديدة"""
        name = self.new_subject_name.text().strip()
        description = self.new_subject_description.text().strip()

        if not name:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المادة")
            return

        # فحص إذا كانت المادة موجودة
        for i in range(self.subjects_list.count()):
            item = self.subjects_list.item(i)
            subject_data = item.data(Qt.UserRole)
            if subject_data['name'] == name:
                QMessageBox.warning(self, "تحذير", "هذه المادة موجودة بالفعل")
                return

        # إضافة المادة الجديدة
        new_subject = {"name": name, "description": description}

        item_text = name
        if description:
            item_text += f" - {description}"

        item = QListWidgetItem(item_text)
        item.setData(Qt.UserRole, new_subject)
        self.subjects_list.addItem(item)

        # حفظ المواد
        subjects = []
        for i in range(self.subjects_list.count()):
            item = self.subjects_list.item(i)
            subjects.append(item.data(Qt.UserRole))

        self.save_subjects(subjects)

        # مسح الحقول
        self.new_subject_name.clear()
        self.new_subject_description.clear()

        QMessageBox.information(self, "نجح", f"تم إضافة المادة '{name}' بنجاح")

    def remove_subject(self):
        """حذف المادة المحددة"""
        current_item = self.subjects_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مادة للحذف")
            return

        subject_data = current_item.data(Qt.UserRole)
        subject_name = subject_data['name']

        reply = QMessageBox.question(self, "تأكيد الحذف",
                                   f"هل أنت متأكد من حذف المادة '{subject_name}'؟",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            row = self.subjects_list.row(current_item)
            self.subjects_list.takeItem(row)

            # حفظ المواد المحدثة
            subjects = []
            for i in range(self.subjects_list.count()):
                item = self.subjects_list.item(i)
                subjects.append(item.data(Qt.UserRole))

            self.save_subjects(subjects)
            QMessageBox.information(self, "نجح", f"تم حذف المادة '{subject_name}' بنجاح")

    def add_default_subject(self, subject_name):
        """إضافة مادة افتراضية"""
        # فحص إذا كانت المادة موجودة
        for i in range(self.subjects_list.count()):
            item = self.subjects_list.item(i)
            subject_data = item.data(Qt.UserRole)
            if subject_data['name'] == subject_name:
                QMessageBox.information(self, "معلومة", f"المادة '{subject_name}' موجودة بالفعل")
                return

        # إضافة المادة
        new_subject = {"name": subject_name, "description": f"مادة {subject_name}"}

        item = QListWidgetItem(f"{subject_name} - مادة {subject_name}")
        item.setData(Qt.UserRole, new_subject)
        self.subjects_list.addItem(item)

        # حفظ المواد
        subjects = []
        for i in range(self.subjects_list.count()):
            item = self.subjects_list.item(i)
            subjects.append(item.data(Qt.UserRole))

        self.save_subjects(subjects)
        QMessageBox.information(self, "نجح", f"تم إضافة المادة '{subject_name}' بنجاح")

    def open_whatsapp(self):
        """فتح واتساب للتواصل"""
        try:
            phone_number = "201225396729"  # رقم الهاتف مع كود الدولة
            message = "مرحباً، أريد الاستفسار عن نظام إدارة الطلاب"

            # رابط واتساب
            whatsapp_url = f"https://wa.me/{phone_number}?text={message}"

            # فتح الرابط
            webbrowser.open(whatsapp_url)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في فتح واتساب: {str(e)}")

    def open_facebook(self):
        """فتح صفحة فيسبوك"""
        try:
            facebook_url = "https://web.facebook.com/profile.php?id=61578888731370"
            webbrowser.open(facebook_url)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في فتح فيسبوك: {str(e)}")
