#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام تجديد الترخيص الشهري

هذا البرنامج المساعد يتيح تجديد ترخيص نظام إدارة المدارس بشكل شهري.
"""

import sys
import os
import json
from datetime import datetime, timedelta
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.licensing.license_manager import LicenseManager
from src.licensing.states import LicenseState
from src.config import LICENSE_FILE, LOG_FILE, LICENSE_DIR

# Set up logging
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename=LOG_FILE,
    encoding='utf-8'
)
logger = logging.getLogger(__name__)

class LicenseRenewal:
    """فئة مساعدة لتجديد الترخيص الشهري."""
    
    def __init__(self):
        self.license_manager = LicenseManager()
        self.license_file = Path(LICENSE_FILE)
        self.license_dir = Path(LICENSE_DIR)
        self.license_dir.mkdir(parents=True, exist_ok=True)
    
    def check_renewal_needed(self):
        """التحقق مما إذا كان الترخيص يحتاج إلى تجديد."""
        try:
            if not self.license_file.exists():
                return False, "لا يوجد ملف ترخيص."
            
            with open(self.license_file, 'r', encoding='utf-8') as f:
                license_data = json.load(f)
            
            expires_at = datetime.fromisoformat(license_data.get('expires_at', ''))
            days_remaining = (expires_at - datetime.now()).days
            
            if days_remaining > 7:  # لا حاجة للتجديد إذا كان متبقي أكثر من أسبوع
                return False, f"الترخيص ساري المفعول حتى {expires_at.strftime('%Y-%m-%d')} (متبقي {days_remaining} يوم)"
            
            return True, f"الترخيص يحتاج إلى تجديد. سينتهي في {expires_at.strftime('%Y-%m-%d')}"
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من حالة التجديد: {str(e)}")
            return False, f"حدث خطأ: {str(e)}"
    
    def renew_license(self, license_key: str):
        """تجديد الترخيص باستخدام مفتاح الترخيص.
        
        Args:
            license_key (str): مفتاح الترخيص الجديد.
            
        Returns:
            tuple: (success, message)
        """
        try:
            # TODO: تنفيذ اتصال بخادم الترخيص للتحقق من المفتاح وتجديد الترخيص
            # هذا مجرد تنفيذ تجريبي
            
            # حفظ نسخة احتياطية من الترخيص الحالي
            backup_file = self.license_dir / f"license_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.bak"
            if self.license_file.exists():
                import shutil
                shutil.copy2(self.license_file, backup_file)
                logger.info(f"تم إنشاء نسخة احتياطية من الترخيص في: {backup_file}")
            
            # تنشيط الترخيص الجديد
            success, message = self.license_manager.activate_offline(license_key)
            
            if success:
                logger.info("تم تجديد الترخيص بنجاح")
                return True, "تم تجديد الترخيص بنجاح"
            else:
                logger.error(f"فشل تجديد الترخيص: {message}")
                return False, f"فشل تجديد الترخيص: {message}"
                
        except Exception as e:
            logger.error(f"خطأ أثناء تجديد الترخيص: {str(e)}")
            return False, f"حدث خطأ أثناء تجديد الترخيص: {str(e)}"
    
    def get_license_info(self):
        """الحصول على معلومات الترخيص الحالي."""
        try:
            if not self.license_file.exists():
                return "لا يوجد ترخيص مفعل."
            
            with open(self.license_file, 'r', encoding='utf-8') as f:
                license_data = json.load(f)
            
            info = []
            info.append("معلومات الترخيص الحالي:")
            info.append("-" * 50)
            
            if 'customer_name' in license_data:
                info.append(f"العميل: {license_data['customer_name']}")
            
            if 'license_type' in license_data:
                info.append(f"نوع الترخيص: {license_data['license_type']}")
            
            if 'issued_at' in license_data:
                issued = datetime.fromisoformat(license_data['issued_at'])
                info.append(f"تاريخ الإصدار: {issued.strftime('%Y-%m-%d')}")
            
            if 'expires_at' in license_data:
                expires = datetime.fromisoformat(license_data['expires_at'])
                days_remaining = (expires - datetime.now()).days
                info.append(f"تاريخ الانتهاء: {expires.strftime('%Y-%m-%d')} (متبقي {days_remaining} يوم)")
            
            if 'device_id' in license_data:
                info.append(f"معرف الجهاز: {license_data['device_id']}")
            
            return "\n".join(info)
            
        except Exception as e:
            logger.error(f"خطأ في قراءة معلومات الترخيص: {str(e)}")
            return f"حدث خطأ في قراءة معلومات الترخيص: {str(e)}"

def main():
    """الدالة الرئيسية لبرنامج تجديد الترخيص."""
    print("=" * 50)
    print("نظام تجديد الترخيص الشهري - School Management System")
    print("=" * 50)
    print()
    
    renewal = LicenseRenewal()
    
    while True:
        print("\n" + "-" * 50)
        print("1. التحقق من حالة الترخيص")
        print("2. تجديد الترخيص")
        print("3. عرض معلومات الترخيص")
        print("4. الخروج")
        print("-" * 50)
        
        choice = input("\nالرجاء اختيار رقم الإجراء: ").strip()
        
        if choice == "1":
            # التحقق من حالة الترخيص
            needed, message = renewal.check_renewal_needed()
            print(f"\n{message}")
            
        elif choice == "2":
            # تجديد الترخيص
            license_key = input("\nالرجاء إدخال مفتاح الترخيص الجديد: ").strip()
            if not license_key:
                print("خطأ: يجب إدخال مفتاح ترخيص صالح.")
                continue
                
            print("\nجاري تجديد الترخيص، الرجاء الانتظار...")
            success, message = renewal.renew_license(license_key)
            print(f"\n{message}")
            
        elif choice == "3":
            # عرض معلومات الترخيص
            info = renewal.get_license_info()
            print(f"\n{info}")
            
        elif choice == "4":
            # الخروج
            print("\nشكرًا لاستخدامك نظام إدارة المدارس. مع السلامة!")
            break
            
        else:
            print("\nخطأ: اختيار غير صالح. الرجاء المحاولة مرة أخرى.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nتم إيقاف البرنامج بواسطة المستخدم.")
        sys.exit(0)
    except Exception as e:
        logger.error(f"حدث خطأ غير متوقع: {str(e)}")
        print(f"\nحدث خطأ غير متوقع: {str(e)}")
        sys.exit(1)
