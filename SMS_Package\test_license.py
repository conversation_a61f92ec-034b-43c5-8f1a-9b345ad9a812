#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for the School Management System licensing module.

This script tests the basic functionality of the licensing system,
including license activation, validation, and feature access control.
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from src.licensing.license_manager import LicenseManager
from src.licensing.states import LicenseState

def print_license_info(license_manager):
    """Print current license information."""
    print("\n" + "="*50)
    print("معلومات الترخيص الحالية:")
    print("-"*50)
    
    state = license_manager.get_license_state()
    info = license_manager.get_license_info()
    
    print(f"حالة الترخيص: {state.name} ({state.display_name_ar()})")
    print(f"صالح: {'نعم' if license_manager.is_license_valid() else 'لا'}")
    
    if state != LicenseState.UNLICENSED:
        print(f"\nمعلومات الترخيص:")
        for key, value in info.items():
            print(f"  - {key}: {value}")
    
    print("="*50 + "\n")

def test_demo_activation():
    """Test demo license activation."""
    print("\nاختبار تفعيل النسخة التجريبية...")
    
    # Create a new license manager (this will create a new device fingerprint)
    license_manager = LicenseManager()
    
    # Activate demo license
    print("\nجاري تفعيل النسخة التجريبية...")
    success, message = license_manager.activate_demo()
    
    if success:
        print("✅ تم تفعيل النسخة التجريبية بنجاح!")
        print_license_info(license_manager)
        
        # Test feature access
        print("\nاختبار صلاحيات الميزات:")
        print(f"- يمكن إضافة طالب: {'نعم' if license_manager.can_add_student() else 'لا'}")
        print(f"- يمكن إضافة مستخدم: {'نعم' if license_manager.can_add_user() else 'لا'}")
        print(f"- يمكن استخدام النسخة الكاملة: {'نعم' if license_manager.is_full_version() else 'لا'}")
        
    else:
        print(f"❌ فشل تفعيل النسخة التجريبية: {message}")
    
    return success

def test_license_validation():
    """Test license validation."""
    print("\nاختبار التحقق من صحة الترخيص...")
    
    license_manager = LicenseManager()
    state = license_manager.get_license_state()
    
    if state == LicenseState.UNLICENSED:
        print("⚠️  لا يوجد ترخيص مفعل حاليًا.")
        return False
    
    is_valid = license_manager.is_license_valid()
    print(f"حالة الترخيص: {'✅ صالح' if is_valid else '❌ غير صالح'}")
    
    if is_valid:
        print("معلومات الترخيص:")
        info = license_manager.get_license_info()
        for key, value in info.items():
            print(f"  - {key}: {value}")
    
    return is_valid

def main():
    """Main test function."""
    print("="*50)
    print("اختبار نظام الترخيص - School Management System")
    print("="*50)
    
    # Create necessary directories
    os.makedirs("data", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    
    # Test 1: Check current license status
    print("\n1. التحقق من حالة الترخيص الحالية:")
    license_manager = LicenseManager()
    print_license_info(license_manager)
    
    # Test 2: Activate demo license if not already activated
    if license_manager.get_license_state() == LicenseState.UNLICENSED:
        if not test_demo_activation():
            print("\n❌ فشل اختبار تفعيل النسخة التجريبية.")
            return 1
    
    # Test 3: Validate license
    if not test_license_validation():
        print("\n❌ فشل اختبار التحقق من صحة الترخيص.")
        return 1
    
    print("\n✅ تم تنفيذ جميع الاختبارات بنجاح!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
