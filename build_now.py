#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء التطبيق فوراً
Build Application Now
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_pyinstaller():
    """تثبيت PyInstaller"""
    try:
        import PyInstaller
        print("PyInstaller is already installed")
        return True
    except ImportError:
        print("Installing PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("Failed to install PyInstaller")
            return False

def build_exe():
    """بناء ملف EXE"""
    print("Building EXE file...")
    
    # إنشاء ملف spec بسيط
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('src', 'src'), ('data', 'data')],
    hiddenimports=['PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'sqlite3'],
    hookspath=[],
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='StudentManagementSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('simple.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    # تشغيل PyInstaller
    try:
        cmd = ['pyinstaller', '--clean', '--noconfirm', 'simple.spec']
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("EXE built successfully!")
            return True
        else:
            print("Build failed:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"Error building EXE: {e}")
        return False

def create_package():
    """إنشاء حزمة التوزيع"""
    if not Path('dist/StudentManagementSystem.exe').exists():
        print("EXE file not found!")
        return False
    
    print("Creating distribution package...")
    
    # إنشاء ملف README
    readme_content = """# Student Management System - نظام إدارة الطلاب

## How to Run - طريقة التشغيل:
1. Double-click StudentManagementSystem.exe
2. Login with: admin / admin123

## Features - المميزات:
- Student Management - إدارة الطلاب
- Attendance Tracking - تسجيل الحضور
- Grade Management - إدارة الدرجات
- Reports - التقارير

Created for Advanced Student Management System - تم التطوير لنظام إدارة الطلاب المتطور
Comprehensive Student Management - نظام شامل لإدارة الطلاب
"""
    
    with open('dist/README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    # نسخ الملفات المهمة
    files_to_copy = ['USER_GUIDE.md', 'INSTALLATION.md']
    for file_name in files_to_copy:
        if Path(file_name).exists():
            shutil.copy2(file_name, 'dist/')
    
    # إنشاء المجلدات المطلوبة
    dirs_to_create = ['logs', 'backups', 'exports']
    for dir_name in dirs_to_create:
        (Path('dist') / dir_name).mkdir(exist_ok=True)
        (Path('dist') / dir_name / '.gitkeep').touch()
    
    print("Package created successfully!")
    return True

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("Building Student Management System EXE")
    print("=" * 50)
    
    # تثبيت PyInstaller
    if not install_pyinstaller():
        return False
    
    # بناء ملف EXE
    if not build_exe():
        return False
    
    # إنشاء حزمة التوزيع
    if not create_package():
        return False
    
    # معلومات النجاح
    exe_path = Path('dist/StudentManagementSystem.exe')
    if exe_path.exists():
        file_size = exe_path.stat().st_size / (1024 * 1024)
        
        print("\n" + "=" * 50)
        print("SUCCESS! EXE built successfully!")
        print("=" * 50)
        print(f"EXE file: {exe_path}")
        print(f"File size: {file_size:.1f} MB")
        print(f"Distribution folder: dist/")
        print()
        print("How to use:")
        print("1. Copy the 'dist' folder to any Windows computer")
        print("2. Double-click StudentManagementSystem.exe")
        print("3. Login with: admin / admin123")
        print()
        print("The application is ready for distribution!")
        
        # تنظيف الملفات المؤقتة
        if Path('build').exists():
            shutil.rmtree('build')
        if Path('simple.spec').exists():
            Path('simple.spec').unlink()
        
        # فتح مجلد التوزيع
        try:
            subprocess.run(['explorer', 'dist'], check=False)
        except:
            pass
        
        return True
    else:
        print("Failed to create EXE file")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\nEXE file is ready!")
    else:
        print("\nFailed to build EXE")
