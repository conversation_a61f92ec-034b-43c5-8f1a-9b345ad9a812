#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة المدارس - School Management System

هو نظام متكامل لإدارة المدارس يشمل إدارة الطلاب والمعلمين والصفوف 
والحضور والغياب والدرجات والامتحانات والمدفوعات.
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon

# Add src directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.ui.main_window import MainWindow
from src.database.database_manager import DatabaseManager

def main():
    """الدالة الرئيسية لتشغيل التطبيق."""
    try:
        # Create application instance
        app = QApplication(sys.argv)
        
        # Set application properties
        app.setApplicationName("نظام إدارة المدارس")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("نظام إدارة المدارس المتطور")
        
        # Set Arabic font and RTL layout
        font = QFont("Arial", 10)
        app.setFont(font)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # Initialize database
        print("📊 جاري تهيئة قاعدة البيانات...")
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # Create and show main window
        print("🚀 جاري تحميل النافذة الرئيسية...")
        main_window = MainWindow()
        main_window.show()
        
        print("✅ تم تشغيل التطبيق بنجاح!")
        
        # Start the application event loop
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
