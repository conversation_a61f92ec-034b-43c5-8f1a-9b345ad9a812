from enum import Enum, auto

class LicenseState(Enum):
    """
    حالات الترخيص المختلفة
    """
    UNLICENSED = auto()      # لم يتم تفعيل الترخيص بعد
    DEMO = auto()           # نسخة تجريبية محدودة الميزات
    TRIAL = auto()          # فترة تجريبية كاملة الميزات
    ACTIVE = auto()         # ترخيص كامل مفعل
    GRACE = auto()          # فترة سماح بعد انتهاء الترخيص
    EXPIRED = auto()        # انتهت صلاحية الترخيص
    BLOCKED = auto()        # تم إيقاف الترخيص
    
    def is_active(self):
        """
        التحقق مما إذا كانت حالة الترخيص تسمح باستخدام التطبيق
        """
        return self in [LicenseState.ACTIVE, LicenseState.TRIAL, LicenseState.GRACE, LicenseState.DEMO]
    
    def is_restricted(self):
        """
        التحقق مما إذا كانت حالة الترخيص مقيدة (عرض فقط أو ميزات محدودة)
        """
        return self in [LicenseState.DEMO, LicenseState.GRACE]
    
    def is_expired(self):
        """
        التحقق مما إذا كانت حالة الترخيص منتهية
        """
        return self in [LicenseState.EXPIRED, LicenseState.BLOCKED]
    
    def get_display_name(self):
        """
        الحصول على اسم الحالة بالعربية
        """
        names = {
            LicenseState.UNLICENSED: "غير مفعل",
            LicenseState.DEMO: "تجريبي",
            LicenseState.TRIAL: "تجربة مجانية",
            LicenseState.ACTIVE: "مفعل",
            LicenseState.GRACE: "فترة سماح",
            LicenseState.EXPIRED: "منتهي الصلاحية",
            LicenseState.BLOCKED: "موقوف"
        }
        return names.get(self, "غير معروف")
