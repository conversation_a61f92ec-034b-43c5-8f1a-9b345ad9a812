@echo off
chcp 65001 >nul
title نظام إدارة الطلاب - النسخة المبسطة

cls
color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة الطلاب                        ║
echo ║                   النسخة المبسطة                            ║
echo ║                   نظام إدارة الطلاب المتطور                           ║
echo ║              نظام شامل لإدارة الطلاب                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 تشغيل النسخة المبسطة...
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود
    echo 🏪 فتح Microsoft Store لتثبيت Python...
    start ms-windows-store://pdp/?ProductId=9NRWMJP3717K
    echo.
    echo انتظر حتى يكتمل التثبيت ثم أعد تشغيل هذا الملف
    goto end
)

echo ✅ Python موجود
python --version

echo.
echo 📦 تثبيت PyQt5...
python -m pip install --quiet PyQt5

echo ✅ تم تثبيت PyQt5

echo.
echo 🎯 تشغيل النسخة المبسطة...
echo.

python simple_main.py

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    النسخة المبسطة                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🎯 مميزات النسخة المبسطة:
echo    ✅ نافذة تسجيل دخول جميلة
echo    ✅ الصفحة الرئيسية مع الإحصائيات
echo    ✅ واجهة عربية حديثة
echo    ✅ قاعدة بيانات SQLite
echo    ✅ تصميم احترافي
echo.
echo 📋 المميزات القادمة:
echo    🔄 إدارة الطلاب الكاملة
echo    🔄 تسجيل الحضور
echo    🔄 إدارة الدرجات
echo    🔄 التقارير PDF/Excel
echo    🔄 نظام QR Code
echo.
echo 💡 هذه نسخة تجريبية تعمل 100%%
echo    المميزات الكاملة ستكون في التحديث القادم
echo.
echo 🎓 تم التطوير بواسطة: م/ حسام أسامة
echo    مهندس برمجيات - مطور تطبيقات
echo    مصمم خصيصاً لنظام إدارة الطلاب المتطور
echo.
echo 🏆 أول نظام إدارة طلاب عربي مبسط!
echo.
pause
