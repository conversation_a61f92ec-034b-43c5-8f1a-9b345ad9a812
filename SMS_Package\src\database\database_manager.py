# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
Database Manager for Student Management System
"""

import sqlite3
import os
from datetime import datetime
from typing import Optional, List, Dict, Any

class DatabaseManager:
    """مدير قاعدة البيانات الرئيسي"""
    
    def __init__(self, db_path: str = None):
        """
        تهيئة مدير قاعدة البيانات
        
        Args:
            db_path: مسار قاعدة البيانات (اختياري)
        """
        if db_path is None:
            # إنشاء مجلد البيانات إذا لم يكن موجوداً
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data')
            os.makedirs(data_dir, exist_ok=True)
            db_path = os.path.join(data_dir, 'students.db')
        
        self.db_path = db_path
        self.connection: Optional[sqlite3.Connection] = None
    
    def connect(self) -> sqlite3.Connection:
        """إنشاء اتصال بقاعدة البيانات"""
        if self.connection is None:
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        return self.connection
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, query: str, params: tuple = ()) -> sqlite3.Cursor:
        """تنفيذ استعلام SQL"""
        conn = self.connect()
        cursor = conn.cursor()
        cursor.execute(query, params)
        conn.commit()
        return cursor
    
    def fetch_all(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """جلب جميع النتائج من استعلام"""
        cursor = self.execute_query(query, params)
        rows = cursor.fetchall()
        return [dict(row) for row in rows]

    def execute_query_with_fetch(self, query: str, params: tuple = (), fetch: bool = False):
        """تنفيذ استعلام مع خيار الجلب"""
        try:
            conn = self.connect()
            cursor = conn.cursor()
            cursor.execute(query, params)

            if fetch:
                result = cursor.fetchall()
                conn.commit()
                return result
            else:
                conn.commit()
                return cursor
        except sqlite3.Error as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            raise
    
    def fetch_one(self, query: str, params: tuple = ()) -> Optional[Dict[str, Any]]:
        """جلب نتيجة واحدة من استعلام"""
        cursor = self.execute_query(query, params)
        row = cursor.fetchone()
        return dict(row) if row else None
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        self.create_tables()
        self.insert_default_data()
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        
        # جدول الطلاب
        students_table = """
        CREATE TABLE IF NOT EXISTS students (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_code TEXT UNIQUE NOT NULL,
            full_name TEXT NOT NULL,
            gender TEXT NOT NULL CHECK (gender IN ('ذكر', 'أنثى')),
            stage TEXT NOT NULL CHECK (stage IN ('إعدادي', 'ثانوي')),
            grade TEXT NOT NULL,
            group_name TEXT DEFAULT 'لا توجد مجموعة',
            phone TEXT,
            parent_phone TEXT,
            geography_score REAL DEFAULT 0,
            history_score REAL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول الحضور
        attendance_table = """
        CREATE TABLE IF NOT EXISTS attendance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            attendance_date DATE NOT NULL,
            status TEXT NOT NULL CHECK (status IN ('حاضر', 'غائب', 'متأخر')),
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students (id) ON DELETE CASCADE,
            UNIQUE(student_id, attendance_date)
        )
        """
        
        # جدول الدرجات (تفصيلي)
        grades_table = """
        CREATE TABLE IF NOT EXISTS grades (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            subject TEXT NOT NULL CHECK (subject IN ('جغرافيا', 'تاريخ')),
            exam_type TEXT NOT NULL,
            score REAL NOT NULL,
            max_score REAL NOT NULL,
            exam_date DATE,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students (id) ON DELETE CASCADE
        )
        """
        
        # جدول الإعدادات
        settings_table = """
        CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_key TEXT UNIQUE NOT NULL,
            setting_value TEXT NOT NULL,
            description TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول المجموعات
        groups_table = """
        CREATE TABLE IF NOT EXISTS groups (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            group_name TEXT NOT NULL UNIQUE,
            description TEXT,
            max_students INTEGER DEFAULT 30,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1
        )
        """

        # جدول المستخدمين (للأدمين)
        users_table = """
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            full_name TEXT NOT NULL,
            role TEXT DEFAULT 'admin',
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP
        )
        """

        # جدول المدفوعات الشهرية
        payments_table = """
        CREATE TABLE IF NOT EXISTS monthly_payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            student_code TEXT NOT NULL,
            student_name TEXT NOT NULL,
            group_name TEXT,
            geography_fee REAL DEFAULT 0,
            history_fee REAL DEFAULT 0,
            total_amount REAL DEFAULT 0,
            payment_date DATE NOT NULL,
            payment_time TIME NOT NULL,
            month_year TEXT NOT NULL,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students (id)
        )
        """

        # تنفيذ إنشاء الجداول
        tables = [students_table, attendance_table, grades_table, settings_table, groups_table, users_table, payments_table]
        
        for table in tables:
            self.execute_query(table)
        
        print("✅ تم إنشاء جداول قاعدة البيانات بنجاح")

        # إضافة الحقول الجديدة إذا لم تكن موجودة
        self.add_missing_columns()

    def add_missing_columns(self):
        """إضافة الأعمدة المفقودة للجداول الموجودة"""
        try:
            # التحقق من وجود الأعمدة الجديدة في جدول الطلاب
            result = self.execute_query_with_fetch("PRAGMA table_info(students)", fetch=True)
            if result:
                columns = [column[1] for column in result]

                if 'phone' not in columns:
                    self.execute_query("ALTER TABLE students ADD COLUMN phone TEXT")
                    print("✅ تم إضافة عمود رقم الهاتف")

                if 'parent_phone' not in columns:
                    self.execute_query("ALTER TABLE students ADD COLUMN parent_phone TEXT")
                    print("✅ تم إضافة عمود رقم ولي الأمر")

                if 'group_name' not in columns:
                    self.execute_query("ALTER TABLE students ADD COLUMN group_name TEXT DEFAULT 'لا توجد مجموعة'")
                    print("✅ تم إضافة عمود المجموعة")

        except Exception as e:
            print(f"⚠️ خطأ في إضافة الأعمدة: {e}")

    def insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        
        # التحقق من وجود المستخدم الافتراضي
        admin_exists = self.fetch_one("SELECT id FROM users WHERE username = ?", ("admin",))
        
        if not admin_exists:
            # إنشاء المستخدم الافتراضي (admin)
            # كلمة المرور الافتراضية: admin123
            import hashlib
            password_hash = hashlib.sha256("admin123".encode()).hexdigest()
            
            self.execute_query("""
                INSERT INTO users (username, password_hash, full_name, role)
                VALUES (?, ?, ?, ?)
            """, ("admin", password_hash, "نظام إدارة الطلاب المتطور", "admin"))
        
        # إدراج الإعدادات الافتراضية
        default_settings = [
            ("teacher_name", "نظام إدارة الطلاب المتطور", "اسم المعلم"),
            ("school_name", "مدرسة النجاح", "اسم المدرسة"),
            ("current_year", "2024-2025", "العام الدراسي الحالي"),
            ("app_version", "1.0", "إصدار التطبيق"),
        ]
        
        for key, value, desc in default_settings:
            existing = self.fetch_one("SELECT id FROM settings WHERE setting_key = ?", (key,))
            if not existing:
                self.execute_query("""
                    INSERT INTO settings (setting_key, setting_value, description)
                    VALUES (?, ?, ?)
                """, (key, value, desc))
        
        print("✅ تم إدراج البيانات الافتراضية بنجاح")
    
    def get_setting(self, key: str) -> Optional[str]:
        """جلب قيمة إعداد معين"""
        result = self.fetch_one("SELECT setting_value FROM settings WHERE setting_key = ?", (key,))
        return result['setting_value'] if result else None
    
    def update_setting(self, key: str, value: str) -> bool:
        """تحديث قيمة إعداد معين"""
        try:
            self.execute_query("""
                UPDATE settings 
                SET setting_value = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE setting_key = ?
            """, (value, key))
            return True
        except Exception as e:
            print(f"خطأ في تحديث الإعداد: {e}")
            return False
    
    def __del__(self):
        """تنظيف الموارد عند حذف الكائن"""
        self.disconnect()
