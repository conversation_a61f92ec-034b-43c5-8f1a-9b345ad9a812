#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة تسجيل الدخول
Login Dialog
"""

import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QMessageBox, QCheckBox,
                            QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPixmap, QColor, QPainter, QPen, QPainterPath

class LoginDialog(QDialog):
    """نافذة تسجيل الدخول"""
    login_successful = pyqtSignal(dict)  # إشارة عند نجاح تسجيل الدخول
    
    def __init__(self, auth_manager, parent=None):
        super().__init__(parent)
        self.auth_manager = auth_manager
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - نظام إدارة المدارس")
        self.setFixedSize(500, 600)

        # تطبيق خلفية متدرجة للنافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
            }
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(25)

        # رسالة الترحيب العصرية
        welcome_label = QLabel("Hello in SMS")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet("""
            QLabel {
                font-size: 32px;
                font-weight: bold;
                color: white;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                margin-bottom: 10px;
                font-family: 'Arial', sans-serif;
            }
        """)

        # شعار التطبيق
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)

        # محاولة تحميل الشعار أو استخدام نص بديل
        logo_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                               'resources', 'logo.png')

        if os.path.exists(logo_path):
            logo_pixmap = QPixmap(logo_path)
            logo_label.setPixmap(logo_pixmap.scaled(120, 120, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        else:
            # إنشاء شعار افتراضي عصري
            logo_label.setText("🎓\nSMS")
            logo_label.setStyleSheet("""
                QLabel {
                    font-size: 28px;
                    font-weight: bold;
                    color: white;
                    padding: 25px;
                    background: rgba(255, 255, 255, 0.1);
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 20px;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
                }
            """)
        
        # حاوية الحقول مع خلفية واضحة
        from PyQt5.QtWidgets import QWidget
        fields_container = QWidget()
        fields_container.setStyleSheet("""
            QWidget {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 20px;
                padding: 30px;
                margin: 10px;
            }
        """)

        fields_layout = QVBoxLayout(fields_container)
        fields_layout.setSpacing(20)
        fields_layout.setContentsMargins(30, 30, 30, 30)

        # حقل اسم المستخدم مع أيقونة
        username_label = QLabel("👤 اسم المستخدم")
        username_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                background: transparent;
                padding: 0px;
                margin: 0px;
            }
        """)

        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم...")
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 15px 20px;
                border: 2px solid #bdc3c7;
                border-radius: 12px;
                font-size: 16px;
                min-height: 20px;
                background: white;
                color: #2c3e50;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
                outline: none;
            }
            QLineEdit:hover {
                border: 2px solid #85929e;
            }
        """)

        # حقل كلمة المرور مع أيقونة
        password_label = QLabel("🔒 كلمة المرور")
        password_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                background: transparent;
                padding: 0px;
                margin: 0px;
            }
        """)

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور...")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 18px 20px;
                border: 2px solid #e0e6ed;
                border-radius: 15px;
                font-size: 16px;
                min-height: 25px;
                min-width: 350px;
                background: white;
                color: #2c3e50;
            }
            QLineEdit:focus {
                border: 2px solid #667eea;
                box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
            }
            QLineEdit:hover {
                border: 2px solid #a8b3d9;
            }
        """)

        # تذكرني مع تصميم عصري
        self.remember_me = QCheckBox("تذكرني")
        self.remember_me.setStyleSheet("""
            QCheckBox {
                color: #2c3e50;
                font-size: 14px;
                background: transparent;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #667eea;
            }
            QCheckBox::indicator:checked {
                background: #667eea;
                border: 2px solid #667eea;
            }
        """)

        # إضافة الحقول إلى الحاوية
        fields_layout.addWidget(username_label)
        fields_layout.addWidget(self.username_input)
        fields_layout.addSpacing(10)
        fields_layout.addWidget(password_label)
        fields_layout.addWidget(self.password_input)
        fields_layout.addWidget(self.remember_me, 0, Qt.AlignRight)

        # زر تسجيل الدخول العصري
        self.login_button = QPushButton("🚀 تسجيل الدخول")
        self.login_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                padding: 18px 40px;
                border: none;
                border-radius: 25px;
                min-width: 250px;
                font-weight: bold;
                font-size: 18px;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #5a67d8, stop:1 #6b46c1);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4c51bf, stop:1 #553c9a);
                transform: translateY(0px);
            }
        """)
        
        # إضافة العناصر إلى التخطيط الرئيسي
        layout.addStretch(1)
        layout.addWidget(welcome_label, 0, Qt.AlignCenter)
        layout.addSpacing(10)
        layout.addWidget(logo_label, 0, Qt.AlignCenter)
        layout.addSpacing(30)
        layout.addWidget(fields_container, 0, Qt.AlignCenter)
        layout.addSpacing(20)
        layout.addWidget(self.login_button, 0, Qt.AlignCenter)
        layout.addStretch(1)

        # تعبئة اسم المستخدم إذا كان محفوظاً
        self.load_saved_credentials()
        
    def setup_connections(self):
        """إعداد اتصالات الإشارات"""
        self.login_button.clicked.connect(self.attempt_login)
        self.password_input.returnPressed.connect(self.attempt_login)
        
    def load_saved_credentials(self):
        """تحميل بيانات الاعتماد المحفوظة"""
        # يمكن إضافة وظيفة لتحميل بيانات الاعتماد المحفوظة
        self.username_input.setText("admin")
        
    def attempt_login(self):
        """محاولة تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال اسم المستخدم وكلمة المرور")
            return
            
        success, message = self.auth_manager.login(username, password)
        
        if success:
            if self.remember_me.isChecked():
                # حفظ بيانات تسجيل الدخول
                pass
            self.accept()
        else:
            QMessageBox.critical(self, "خطأ في تسجيل الدخول", message)
            self.password_input.clear()
            self.password_input.setFocus()

    def keyPressEvent(self, event):
        """معالجة ضغطات المفاتيح"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.attempt_login()
        else:
            super().keyPressEvent(event)
