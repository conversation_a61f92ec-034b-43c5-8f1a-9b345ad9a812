#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة تسجيل الدخول
Login Dialog
"""

import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QMessageBox, QCheckBox,
                            QSpacerItem, QSizePolicy, QWidget)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPixmap, QColor, QPainter, QPen, QPainterPath

class LoginDialog(QDialog):
    """نافذة تسجيل الدخول"""
    login_successful = pyqtSignal(dict)  # إشارة عند نجاح تسجيل الدخول
    
    def __init__(self, auth_manager, parent=None):
        super().__init__(parent)
        self.auth_manager = auth_manager
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - نظام إدارة المدارس")
        self.setFixedSize(600, 700)

        # تطبيق خلفية متدرجة للنافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
            }
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(50, 50, 50, 50)
        layout.setSpacing(30)

        # رسالة الترحيب العصرية
        welcome_label = QLabel("Hello in SMS")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet("""
            QLabel {
                font-size: 36px;
                font-weight: bold;
                color: white;
                margin-bottom: 15px;
                font-family: 'Arial', sans-serif;
            }
        """)

        # شعار التطبيق
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)

        # محاولة تحميل الشعار أو استخدام نص بديل
        logo_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                               'resources', 'logo.png')

        if os.path.exists(logo_path):
            logo_pixmap = QPixmap(logo_path)
            logo_label.setPixmap(logo_pixmap.scaled(120, 120, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        else:
            # إنشاء شعار افتراضي عصري
            logo_label.setText("🎓\nSMS")
            logo_label.setStyleSheet("""
                QLabel {
                    font-size: 32px;
                    font-weight: bold;
                    color: white;
                    padding: 30px;
                    background: rgba(255, 255, 255, 0.15);
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 25px;
                }
            """)
        
        # حقل اسم المستخدم
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("اسم المستخدم")
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                min-width: 250px;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
            }
        """)
        
        # حقل كلمة المرور
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                min-width: 250px;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
            }
        """)
        
        # تذكرني
        self.remember_me = QCheckBox("تذكرني")
        self.remember_me.setStyleSheet("""
            QCheckBox {
                color: #2c3e50;
            }
        """)
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 10px;
                border: none;
                border-radius: 5px;
                min-width: 200px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #2472a4;
            }
        """)
        
        # إضافة العناصر إلى التخطيط
        layout.addStretch()
        layout.addWidget(logo_label, 0, Qt.AlignCenter)
        layout.addSpacing(20)
        layout.addWidget(QLabel("اسم المستخدم:"))
        layout.addWidget(self.username_input)
        layout.addSpacing(10)
        layout.addWidget(QLabel("كلمة المرور:"))
        layout.addWidget(self.password_input)
        layout.addWidget(self.remember_me, 0, Qt.AlignRight)
        layout.addSpacing(20)
        layout.addWidget(self.login_button, 0, Qt.AlignCenter)
        layout.addStretch()
        
        # تعبئة اسم المستخدم إذا كان محفوظاً
        self.load_saved_credentials()
        
    def setup_connections(self):
        """إعداد اتصالات الإشارات"""
        self.login_button.clicked.connect(self.attempt_login)
        self.password_input.returnPressed.connect(self.attempt_login)
        
    def load_saved_credentials(self):
        """تحميل بيانات الاعتماد المحفوظة"""
        # يمكن إضافة وظيفة لتحميل بيانات الاعتماد المحفوظة
        self.username_input.setText("admin")
        
    def attempt_login(self):
        """محاولة تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال اسم المستخدم وكلمة المرور")
            return
            
        success, message = self.auth_manager.login(username, password)
        
        if success:
            if self.remember_me.isChecked():
                # حفظ بيانات تسجيل الدخول
                pass
            self.accept()
        else:
            QMessageBox.critical(self, "خطأ في تسجيل الدخول", message)
            self.password_input.clear()
            self.password_input.setFocus()

    def keyPressEvent(self, event):
        """معالجة ضغطات المفاتيح"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.attempt_login()
        else:
            super().keyPressEvent(event)
