@echo off
title اختبار تشغيل النظام

echo ===================================================
echo    اختبار تشغيل نظام إدارة المدارس
echo    الإصدار 1.0.0
echo ===================================================

echo.
echo جاري تشغيل التطبيق...
echo.

:: Run the application and log output
python main.py 2> error_log.txt

:: Check if there were any errors
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل التطبيق.
    echo    راجع ملف error_log.txt لمزيد من التفاصيل.
) else (
    echo.
    echo ✅ تم تشغيل التطبيق بنجاح!
)

echo.
pause
