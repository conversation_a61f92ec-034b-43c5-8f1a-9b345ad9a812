@echo off
chcp 65001 >nul
title تثبيت Python

cls
color 0E
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      تثبيت Python                           ║
echo ║                   نظام إدارة الطلاب المتطور                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص Python...
echo.

python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ Python موجود ويعمل!
    python --version
    goto install_packages
)

py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ Python موجود ويعمل!
    py --version
    set PYTHON_CMD=py
    goto install_packages
)

echo ❌ Python غير موجود
echo.
echo 🏪 تثبيت Python من Microsoft Store...
start ms-windows-store://pdp/?ProductId=9NRWMJP3717K

echo.
echo انتظر حتى يكتمل التثبيت ثم اضغط أي مفتاح...
pause

python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم تثبيت Python!
    python --version
    set PYTHON_CMD=python
    goto install_packages
)

py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم تثبيت Python!
    py --version
    set PYTHON_CMD=py
    goto install_packages
)

echo ❌ لم يتم التثبيت
echo 🌐 فتح الموقع الرسمي...
start https://python.org/downloads
echo.
echo تعليمات:
echo 1. حمل Python
echo 2. ثبته مع تحديد "Add Python to PATH"
echo 3. أعد تشغيل الكمبيوتر
echo 4. شغل هذا الملف مرة أخرى
echo.
goto end

:install_packages
echo.
echo 📦 تثبيت PyQt5...
%PYTHON_CMD% -m pip install PyQt5

echo.
echo ✅ تم تثبيت كل شيء!
echo.
echo الآن يمكنك تشغيل النظام من:
echo - RUN_COMPACT_FINAL.bat
echo - أو أي ملف RUN_*.bat
echo.

:end
pause
