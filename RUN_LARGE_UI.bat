@echo off
chcp 65001 >nul
title نظام إدارة الطلاب - واجهة كبيرة وواضحة

cls
color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                نظام إدارة الطلاب                            ║
echo ║                واجهة كبيرة وواضحة                          ║
echo ║                نظام إدارة الطلاب المتطور                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 تشغيل النظام بواجهة كبيرة وواضحة...
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود
    goto end
)

echo ✅ Python موجود
python --version

echo.
echo 📦 تثبيت المكتبات...
python -m pip install --quiet PyQt5 qrcode[pil] Pillow

echo.
echo 🎯 تشغيل النظام بواجهة كبيرة...
echo.

REM تشغيل النظام مع إعدادات الخط الكبير
python -c "
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

try:
    app = QApplication(sys.argv)
    app.setApplicationName('نظام إدارة الطلاب')
    
    # تكبير الخط الأساسي
    large_font = QFont('Arial', 16)  # خط كبير
    app.setFont(large_font)
    
    # إعداد DPI للشاشات عالية الدقة
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app.setLayoutDirection(Qt.RightToLeft)
    
    from src.database.database_manager import DatabaseManager
    from src.ui.login_window import LoginWindow
    
    print('📊 تهيئة قاعدة البيانات...')
    db_manager = DatabaseManager()
    db_manager.initialize_database()
    
    print('🔐 فتح نافذة تسجيل الدخول...')
    login_window = LoginWindow()
    
    # تكبير نافذة تسجيل الدخول
    login_window.setFixedSize(500, 400)
    
    # تطبيق تنسيق كبير
    login_window.setStyleSheet('''
        QWidget {
            background-color: #f0f0f0;
            font-family: Arial;
            font-size: 16px;
        }
        QLabel {
            color: #2c3e50;
            font-size: 18px;
            font-weight: bold;
            padding: 8px;
        }
        QLineEdit {
            padding: 15px;
            border: 3px solid #bdc3c7;
            border-radius: 8px;
            font-size: 16px;
            background-color: white;
            min-height: 20px;
        }
        QLineEdit:focus {
            border-color: #3498db;
        }
        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 18px;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            min-height: 25px;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
        QPushButton:pressed {
            background-color: #21618c;
        }
    ''')
    
    login_window.show()
    
    print('✅ تم تشغيل التطبيق بواجهة كبيرة!')
    sys.exit(app.exec_())
    
except Exception as e:
    print(f'❌ خطأ: {e}')
    import traceback
    traceback.print_exc()
"

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    الواجهة الكبيرة والواضحة                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🔍 مميزات الواجهة الكبيرة:
echo    ✅ خط كبير وواضح (حجم 16-18)
echo    ✅ أزرار كبيرة وسهلة الضغط
echo    ✅ حقول إدخال واسعة
echo    ✅ ألوان واضحة ومتباينة
echo    ✅ مساحات كافية بين العناصر
echo    ✅ دعم الشاشات عالية الدقة
echo.
echo 💡 نصائح للاستخدام:
echo    • استخدم Ctrl + لتكبير النص أكثر
echo    • استخدم Ctrl - لتصغير النص
echo    • استخدم Ctrl 0 للعودة للحجم الطبيعي
echo.
echo 🎓 تم التطوير بواسطة: م/ حسام أسامة
echo.
pause
