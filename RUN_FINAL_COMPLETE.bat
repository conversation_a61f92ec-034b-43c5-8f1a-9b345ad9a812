@echo off
chcp 65001 >nul
title نظام إدارة الطلاب - الإصدار النهائي الشامل المتكامل

cls
color 0F
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة الطلاب                        ║
echo ║           الإصدار النهائي الشامل المتكامل v6.0              ║
echo ║                   نظام إدارة الطلاب المتطور                           ║
echo ║              نظام شامل لإدارة الطلاب                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎉 المميزات الجديدة في هذا الإصدار:
echo.
echo ✅ نظام إدارة المدفوعات الشهرية الكامل
echo ✅ حساب تلقائي للمبالغ والإحصائيات
echo ✅ تسجيل تلقائي للتاريخ والوقت والشهر
echo ✅ إعدادات أسعار الجغرافيا والتاريخ
echo ✅ رسائل تلقائية للغياب والدرجات الممتازة/المنخفضة
echo ✅ إحصائيات يومية وشهرية للمدفوعات
echo ✅ حل جميع مشاكل قاعدة البيانات
echo ✅ واجهة متكاملة وشاملة 100%
echo.

echo 🚀 بدء تشغيل النظام المتكامل...
echo.

REM محاولة تشغيل البرنامج بطرق مختلفة

REM الطريقة الأولى: py launcher
py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python!
    py --version
    echo.
    echo 🎯 تشغيل نظام إدارة الطلاب المتكامل...
    echo.
    py main.py
    goto end
)

REM الطريقة الثانية: python command
python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python!
    python --version
    echo.
    echo 🎯 تشغيل نظام إدارة الطلاب المتكامل...
    echo.
    python main.py
    goto end
)

REM الطريقة الثالثة: python3 command
python3 --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python3!
    python3 --version
    echo.
    echo 🎯 تشغيل نظام إدارة الطلاب المتكامل...
    echo.
    python3 main.py
    goto end
)

echo ❌ لم يتم العثور على Python!
echo.
echo 💡 الحلول المتاحة:
echo.
echo 1️⃣ تثبيت Python من Microsoft Store:
echo    - افتح Microsoft Store
echo    - ابحث عن "Python"
echo    - ثبت Python 3.9 أو أحدث
echo.
echo 2️⃣ تثبيت Python من الموقع الرسمي:
echo    - اذهب إلى: https://python.org/downloads
echo    - حمل وثبت Python
echo    - تأكد من تحديد "Add Python to PATH"
echo.

echo 🌐 فتح صفحة تحميل Python...
start https://python.org/downloads

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  دليل الاستخدام المتكامل                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🧭 شريط التنقل الشامل:
echo    🏠 الرئيسية - لوحة التحكم الرئيسية
echo    👥 إدارة الطلاب - إضافة وتعديل الطلاب مع المجموعات
echo    👨‍👩‍👧‍👦 إدارة المجموعات - تنظيم الطلاب في مجموعات
echo    💰 إدارة المدفوعات - تسجيل ومتابعة المدفوعات الشهرية
echo    📋 تسجيل الحضور - تسجيل حضور مع رسائل تلقائية
echo    📊 إدارة الدرجات - إدخال درجات مع رسائل تلقائية
echo    📈 التقارير - تقارير شاملة PDF/Excel
echo    ⚙️ الإعدادات - إعدادات النظام ومعلومات المطور
echo    🚪 تسجيل الخروج - الخروج الآمن
echo.
echo 💰 نظام المدفوعات الجديد:
echo.
echo 🔹 تسجيل المدفوعات:
echo    • اختيار الطالب والمجموعة
echo    • تحديد رسوم الجغرافيا والتاريخ
echo    • حساب تلقائي للإجمالي
echo    • تسجيل تلقائي للتاريخ والوقت
echo    • اختيار الشهر والسنة
echo.
echo 🔹 الإحصائيات التلقائية:
echo    • إجمالي مدفوعات اليوم
echo    • إجمالي مدفوعات الشهر
echo    • عدد الطلاب الذين دفعوا
echo    • فلترة حسب الشهر والبحث
echo.
echo 🔹 إعدادات الأسعار:
echo    • تحديد سعر الجغرافيا الافتراضي
echo    • تحديد سعر التاريخ الافتراضي
echo    • حفظ الإعدادات في قاعدة البيانات
echo.
echo 📱 نظام الرسائل التلقائية المحسن:
echo.
echo 🔹 رسائل الحضور:
echo    • إشعار تلقائي عند غياب الطالب
echo    • إرسال لولي الأمر فوراً
echo.
echo 🔹 رسائل الدرجات:
echo    • تهنئة للدرجات الممتازة (85% فأكثر)
echo    • تنبيه للدرجات المنخفضة (أقل من 50%)
echo    • إرسال تلقائي عبر WhatsApp و Telegram
echo.
echo 📚 جميع المميزات المتكاملة:
echo    ✅ إدارة الطلاب مع المجموعات والهواتف
echo    ✅ نظام مدفوعات شهرية متكامل
echo    ✅ تسجيل حضور مع رسائل تلقائية
echo    ✅ إدارة درجات مع رسائل ذكية
echo    ✅ إدارة مجموعات متقدمة
echo    ✅ أيقونات WhatsApp و Telegram
echo    ✅ تقارير احترافية PDF/Excel
echo    ✅ نظام نسخ احتياطي متقدم
echo    ✅ واجهة عربية محسنة بالكامل
echo    ✅ خلفيات بيضاء واضحة
echo    ✅ شريط تنقل متطور
echo    ✅ إحصائيات تلقائية شاملة
echo.
echo 🎓 تم التطوير والتحسين بواسطة:
echo    Eng / Hossam Osama
echo    مهندس برمجيات - مطور تطبيقات
echo.
echo 🎯 مصمم خصيصاً لنظام إدارة الطلاب المتطور
echo    نظام شامل لإدارة الطلاب
echo.
echo 📞 للدعم الفني: راجع تبويب "حول التطبيق" في الإعدادات
echo.
echo 🎉 استمتع بالنظام المتكامل الشامل!
echo    أقوى نظام إدارة طلاب عربي متكامل
echo.
pause
