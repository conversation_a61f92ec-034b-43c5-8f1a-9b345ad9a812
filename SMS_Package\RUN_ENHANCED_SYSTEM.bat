@echo off
chcp 65001 > nul
title نظام إدارة الطلاب المحسن

echo.
echo ===============================================
echo    🎓 نظام إدارة الطلاب المحسن 🎓
echo    نظام إدارة الطلاب المتطور
echo ===============================================
echo.
echo 📚 نظام إدارة الطلاب المتطور – دروس، امتحانات، ومتابعة مستمرة.
echo.
echo ===============================================
echo           المميزات الجديدة في الإصدار 2.0
echo ===============================================
echo.
echo ✅ دعم الشبكات والوصول عن بُعد
echo ✅ قاعدة بيانات متقدمة (PostgreSQL/MySQL)
echo ✅ مزامنة سحابية تلقائية
echo ✅ بوت Telegram للإشعارات
echo ✅ نظام تحديث تلقائي
echo ✅ واجهة ويب متجاوبة
echo ✅ تحديثات مباشرة في الوقت الفعلي
echo ✅ دعم متعدد المستخدمين
echo.
echo ===============================================

REM فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo 📥 يرجى تثبيت Python أولاً من: https://python.org
    echo.
    pause
    exit /b 1
)

echo 🔍 فحص Python... ✅
echo.

REM فحص وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متاح
    echo 📥 يرجى تثبيت pip أولاً
    echo.
    pause
    exit /b 1
)

echo 🔍 فحص pip... ✅
echo.

REM تثبيت المتطلبات الأساسية
echo 📦 تثبيت المتطلبات الأساسية...
pip install PyQt5 reportlab openpyxl Pillow requests --quiet --disable-pip-version-check
if errorlevel 1 (
    echo ⚠️ تحذير: فشل في تثبيت بعض المتطلبات الأساسية
)

REM تثبيت متطلبات الخادم الويب
echo 🌐 تثبيت متطلبات الخادم الويب...
pip install Flask Flask-SocketIO python-socketio eventlet --quiet --disable-pip-version-check
if errorlevel 1 (
    echo ⚠️ تحذير: فشل في تثبيت متطلبات الخادم الويب
)

REM تثبيت متطلبات قاعدة البيانات المتقدمة
echo 📊 تثبيت متطلبات قاعدة البيانات المتقدمة...
pip install SQLAlchemy psycopg2-binary PyMySQL --quiet --disable-pip-version-check
if errorlevel 1 (
    echo ⚠️ تحذير: فشل في تثبيت متطلبات قاعدة البيانات المتقدمة
)

REM تثبيت متطلبات المزامنة السحابية
echo ☁️ تثبيت متطلبات المزامنة السحابية...
pip install google-cloud-storage boto3 dropbox --quiet --disable-pip-version-check
if errorlevel 1 (
    echo ⚠️ تحذير: فشل في تثبيت متطلبات المزامنة السحابية
)

REM تثبيت متطلبات Telegram
echo 🤖 تثبيت متطلبات بوت Telegram...
pip install python-telegram-bot --quiet --disable-pip-version-check
if errorlevel 1 (
    echo ⚠️ تحذير: فشل في تثبيت متطلبات Telegram
)

REM تثبيت متطلبات التحديث التلقائي
echo 🔄 تثبيت متطلبات التحديث التلقائي...
pip install packaging cryptography --quiet --disable-pip-version-check
if errorlevel 1 (
    echo ⚠️ تحذير: فشل في تثبيت متطلبات التحديث التلقائي
)

echo.
echo ===============================================
echo              🚀 تشغيل النظام المحسن
echo ===============================================
echo.

REM إنشاء المجلدات المطلوبة
if not exist "data" mkdir data
if not exist "logs" mkdir logs
if not exist "exports" mkdir exports
if not exist "backups" mkdir backups
if not exist "temp" mkdir temp

REM فحص وجود الملف الرئيسي المحسن
if not exist "enhanced_main.py" (
    echo ❌ ملف enhanced_main.py غير موجود
    echo 📁 يرجى التأكد من وجود جميع ملفات النظام
    echo.
    pause
    exit /b 1
)

echo 🎯 بدء تشغيل النظام المحسن...
echo.
echo 💡 ملاحظات مهمة:
echo    • سيتم فتح نافذة تسجيل الدخول
echo    • الخادم الويب سيعمل على المنفذ 5000
echo    • يمكن الوصول للنظام من: http://localhost:5000
echo    • أيقونة النظام ستظهر في شريط المهام
echo    • جميع المميزات الجديدة ستكون متاحة
echo.
echo 🔐 بيانات تسجيل الدخول الافتراضية:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.

REM تشغيل النظام المحسن
python enhanced_main.py

REM في حالة فشل التشغيل، جرب النظام الأساسي
if errorlevel 1 (
    echo.
    echo ⚠️ فشل في تشغيل النظام المحسن
    echo 🔄 محاولة تشغيل النظام الأساسي...
    echo.
    
    if exist "main.py" (
        python main.py
    ) else (
        echo ❌ لم يتم العثور على ملف main.py
        echo 📞 يرجى التواصل مع الدعم الفني
    )
)

echo.
echo ===============================================
echo                تم إنهاء البرنامج
echo ===============================================
echo.
pause
