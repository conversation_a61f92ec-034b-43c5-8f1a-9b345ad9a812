# -*- coding: utf-8 -*-
"""
نافذة نقل الطلاب بين المجموعات
Group Transfer Window

إمكانية نقل الطلاب مع تحديث جميع البيانات تلقائياً
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QComboBox, QTableWidget, QTableWidgetItem,
                            QMessageBox, QHeaderView, QGroupBox, QFormLayout,
                            QLineEdit, QTextEdit, QFrame, QProgressBar,
                            QCheckBox, QListWidget, QListWidgetItem, QSplitter)
from PyQt5.QtCore import Qt, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QColor, QIcon
from datetime import datetime

from ..models.student import Student
from ..models.groups import Groups
from ..database.database_manager import DatabaseManager

class GroupTransferWindow(QWidget):
    """نافذة نقل الطلاب بين المجموعات"""
    
    # إشارة لتحديث البيانات في النوافذ الأخرى
    student_transferred = pyqtSignal(int, str, str)  # student_id, old_group, new_group
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
        self.student_model = Student(db_manager)
        self.groups_model = Groups(db_manager)
        
        self.selected_students = []
        
        self.init_ui()
        self.apply_styles()
        self.load_groups()
        self.load_students()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("نقل الطلاب بين المجموعات")
        self.setGeometry(100, 100, 1200, 800)
        
        layout = QVBoxLayout()
        
        # عنوان النافذة
        title_label = QLabel("🔄 نقل الطلاب بين المجموعات")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("titleLabel")
        layout.addWidget(title_label)
        
        # التخطيط الرئيسي
        main_splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - اختيار الطلاب
        left_widget = self.create_students_selection_widget()
        main_splitter.addWidget(left_widget)
        
        # الجانب الأيمن - معلومات النقل
        right_widget = self.create_transfer_info_widget()
        main_splitter.addWidget(right_widget)
        
        # تحديد نسب العرض
        main_splitter.setSizes([600, 600])
        
        layout.addWidget(main_splitter)
        self.setLayout(layout)
    
    def create_students_selection_widget(self):
        """إنشاء ويدجت اختيار الطلاب"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # فلاتر البحث
        filters_group = QGroupBox("فلاتر البحث")
        filters_layout = QFormLayout()
        
        # فلتر المجموعة الحالية
        self.current_group_combo = QComboBox()
        self.current_group_combo.addItem("جميع المجموعات")
        self.current_group_combo.currentTextChanged.connect(self.filter_students_by_group)
        filters_layout.addRow("المجموعة الحالية:", self.current_group_combo)
        
        # البحث بالاسم أو الكود
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالاسم أو الكود...")
        self.search_input.textChanged.connect(self.search_students)
        search_btn = QPushButton("🔍 بحث")
        search_btn.clicked.connect(self.search_students)
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(search_btn)
        filters_layout.addRow("البحث:", search_layout)
        
        filters_group.setLayout(filters_layout)
        layout.addWidget(filters_group)
        
        # جدول الطلاب
        students_group = QGroupBox("قائمة الطلاب")
        students_layout = QVBoxLayout()
        
        # أزرار التحديد
        selection_buttons_layout = QHBoxLayout()
        
        select_all_btn = QPushButton("✅ تحديد الكل")
        select_all_btn.clicked.connect(self.select_all_students)
        
        deselect_all_btn = QPushButton("❌ إلغاء التحديد")
        deselect_all_btn.clicked.connect(self.deselect_all_students)
        
        selection_buttons_layout.addWidget(select_all_btn)
        selection_buttons_layout.addWidget(deselect_all_btn)
        selection_buttons_layout.addStretch()
        
        students_layout.addLayout(selection_buttons_layout)
        
        # جدول الطلاب
        self.students_table = QTableWidget()
        self.students_table.setColumnCount(6)
        self.students_table.setHorizontalHeaderLabels([
            "تحديد", "الكود", "الاسم", "المجموعة الحالية", "الصف", "الهاتف"
        ])
        
        # إعدادات الجدول
        header = self.students_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.students_table.setAlternatingRowColors(True)
        self.students_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        students_layout.addWidget(self.students_table)
        students_group.setLayout(students_layout)
        layout.addWidget(students_group)
        
        widget.setLayout(layout)
        return widget
    
    def create_transfer_info_widget(self):
        """إنشاء ويدجت معلومات النقل"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # معلومات النقل
        transfer_group = QGroupBox("معلومات النقل")
        transfer_layout = QFormLayout()
        
        # المجموعة الجديدة
        self.new_group_combo = QComboBox()
        self.new_group_combo.addItem("اختر المجموعة الجديدة", None)
        transfer_layout.addRow("المجموعة الجديدة:", self.new_group_combo)
        
        # سبب النقل
        self.transfer_reason_input = QTextEdit()
        self.transfer_reason_input.setMaximumHeight(100)
        self.transfer_reason_input.setPlaceholderText("اختياري: سبب نقل الطلاب...")
        transfer_layout.addRow("سبب النقل:", self.transfer_reason_input)
        
        # خيارات إضافية
        options_layout = QVBoxLayout()
        
        self.update_attendance_checkbox = QCheckBox("تحديث سجلات الحضور")
        self.update_attendance_checkbox.setChecked(True)
        options_layout.addWidget(self.update_attendance_checkbox)
        
        self.update_grades_checkbox = QCheckBox("تحديث سجلات الدرجات")
        self.update_grades_checkbox.setChecked(True)
        options_layout.addWidget(self.update_grades_checkbox)
        
        self.send_notification_checkbox = QCheckBox("إرسال إشعار لأولياء الأمور")
        self.send_notification_checkbox.setChecked(False)
        options_layout.addWidget(self.send_notification_checkbox)
        
        transfer_layout.addRow("خيارات:", options_layout)
        
        transfer_group.setLayout(transfer_layout)
        layout.addWidget(transfer_group)
        
        # معاينة النقل
        preview_group = QGroupBox("معاينة النقل")
        preview_layout = QVBoxLayout()
        
        self.preview_label = QLabel("لم يتم تحديد أي طلاب للنقل")
        self.preview_label.setObjectName("previewLabel")
        self.preview_label.setWordWrap(True)
        preview_layout.addWidget(self.preview_label)
        
        # قائمة الطلاب المحددين
        self.selected_students_list = QListWidget()
        self.selected_students_list.setMaximumHeight(200)
        preview_layout.addWidget(self.selected_students_list)
        
        preview_group.setLayout(preview_layout)
        layout.addWidget(preview_group)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.transfer_btn = QPushButton("🔄 تنفيذ النقل")
        self.transfer_btn.clicked.connect(self.execute_transfer)
        self.transfer_btn.setEnabled(False)
        
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.clicked.connect(self.close)
        
        buttons_layout.addWidget(self.transfer_btn)
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # إحصائيات
        stats_group = QGroupBox("إحصائيات المجموعات")
        stats_layout = QVBoxLayout()
        
        self.stats_label = QLabel("جاري تحميل الإحصائيات...")
        self.stats_label.setWordWrap(True)
        stats_layout.addWidget(self.stats_label)
        
        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)
        
        widget.setLayout(layout)
        return widget
    
    def load_groups(self):
        """تحميل قائمة المجموعات"""
        try:
            groups = self.groups_model.get_all_groups()
            
            # تحديث قائمة المجموعة الحالية
            self.current_group_combo.clear()
            self.current_group_combo.addItem("جميع المجموعات")
            for group in groups:
                self.current_group_combo.addItem(f"{group['group_name']} ({group['student_count']} طالب)")
            
            # تحديث قائمة المجموعة الجديدة
            self.new_group_combo.clear()
            self.new_group_combo.addItem("اختر المجموعة الجديدة", None)
            for group in groups:
                available_slots = group['max_students'] - group['student_count']
                self.new_group_combo.addItem(
                    f"{group['group_name']} ({available_slots} مكان متاح)", 
                    group['group_name']
                )
            
            self.update_group_statistics()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المجموعات: {str(e)}")
    
    def load_students(self):
        """تحميل قائمة الطلاب"""
        try:
            students = self.student_model.get_all_students()
            
            # ترتيب الطلاب حسب الكود
            students.sort(key=lambda x: int(x['student_code']) if x['student_code'].isdigit() else 0)
            
            self.students_table.setRowCount(len(students))
            
            for row, student in enumerate(students):
                # خانة التحديد
                checkbox = QCheckBox()
                checkbox.stateChanged.connect(self.update_selection)
                self.students_table.setCellWidget(row, 0, checkbox)
                
                # بيانات الطالب
                self.students_table.setItem(row, 1, QTableWidgetItem(student['student_code']))
                self.students_table.setItem(row, 2, QTableWidgetItem(student['full_name']))
                self.students_table.setItem(row, 3, QTableWidgetItem(student.get('group_name', 'لا توجد مجموعة')))
                self.students_table.setItem(row, 4, QTableWidgetItem(student['grade']))
                self.students_table.setItem(row, 5, QTableWidgetItem(student.get('phone', 'غير محدد')))
                
                # حفظ معرف الطالب
                self.students_table.item(row, 1).setData(Qt.UserRole, student['id'])
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الطلاب: {str(e)}")
    
    def filter_students_by_group(self):
        """فلترة الطلاب حسب المجموعة"""
        selected_group_text = self.current_group_combo.currentText()
        
        if selected_group_text == "جميع المجموعات":
            # إظهار جميع الطلاب
            for row in range(self.students_table.rowCount()):
                self.students_table.setRowHidden(row, False)
        else:
            # استخراج اسم المجموعة من النص
            group_name = selected_group_text.split(" (")[0]
            
            for row in range(self.students_table.rowCount()):
                group_item = self.students_table.item(row, 3)
                if group_item:
                    show_row = group_item.text() == group_name
                    self.students_table.setRowHidden(row, not show_row)

    def select_all_students(self):
        """تحديد جميع الطلاب المرئيين"""
        for row in range(self.students_table.rowCount()):
            if not self.students_table.isRowHidden(row):
                checkbox = self.students_table.cellWidget(row, 0)
                if checkbox:
                    checkbox.setChecked(True)

    def deselect_all_students(self):
        """إلغاء تحديد جميع الطلاب"""
        for row in range(self.students_table.rowCount()):
            checkbox = self.students_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)

    def update_selection(self):
        """تحديث قائمة الطلاب المحددين"""
        self.selected_students = []
        self.selected_students_list.clear()

        for row in range(self.students_table.rowCount()):
            checkbox = self.students_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                student_id = self.students_table.item(row, 1).data(Qt.UserRole)
                student_code = self.students_table.item(row, 1).text()
                student_name = self.students_table.item(row, 2).text()
                current_group = self.students_table.item(row, 3).text()

                student_info = {
                    'id': student_id,
                    'code': student_code,
                    'name': student_name,
                    'current_group': current_group
                }

                self.selected_students.append(student_info)

                # إضافة إلى قائمة المعاينة
                list_item = QListWidgetItem(f"{student_code} - {student_name} (من: {current_group})")
                self.selected_students_list.addItem(list_item)

        # تحديث النص والأزرار
        count = len(self.selected_students)
        if count == 0:
            self.preview_label.setText("لم يتم تحديد أي طلاب للنقل")
            self.transfer_btn.setEnabled(False)
        else:
            self.preview_label.setText(f"تم تحديد {count} طالب للنقل")
            self.transfer_btn.setEnabled(True)

    def update_group_statistics(self):
        """تحديث إحصائيات المجموعات"""
        try:
            stats = self.groups_model.get_group_statistics()

            stats_text = f"📊 إحصائيات المجموعات:\n\n"
            stats_text += f"• إجمالي المجموعات: {stats['total_groups']}\n\n"

            for group_stat in stats['by_group']:
                stats_text += f"📁 {group_stat['group_name']}:\n"
                stats_text += f"   • عدد الطلاب: {group_stat['student_count']}\n"
                stats_text += f"   • السعة القصوى: {group_stat['max_students']}\n"
                stats_text += f"   • نسبة الامتلاء: {group_stat['fill_percentage']:.1f}%\n\n"

            self.stats_label.setText(stats_text)

        except Exception as e:
            self.stats_label.setText(f"خطأ في تحميل الإحصائيات: {str(e)}")

    def execute_transfer(self):
        """تنفيذ عملية النقل"""
        if not self.selected_students:
            QMessageBox.warning(self, "تحذير", "لم يتم تحديد أي طلاب للنقل")
            return

        new_group_name = self.new_group_combo.currentData()
        if not new_group_name:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار المجموعة الجديدة")
            return

        # التأكيد من المستخدم
        count = len(self.selected_students)
        reply = QMessageBox.question(
            self, "تأكيد النقل",
            f"هل أنت متأكد من نقل {count} طالب إلى مجموعة '{new_group_name}'؟\n\n"
            "سيتم تحديث جميع البيانات المرتبطة بالطلاب.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # بدء عملية النقل
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(count)
        self.progress_bar.setValue(0)

        self.transfer_btn.setEnabled(False)

        successful_transfers = 0
        failed_transfers = []

        for i, student in enumerate(self.selected_students):
            try:
                # نقل الطالب
                success = self.groups_model.move_student_to_group(student['id'], new_group_name)

                if success:
                    successful_transfers += 1

                    # إرسال إشارة التحديث
                    self.student_transferred.emit(student['id'], student['current_group'], new_group_name)

                    # إرسال إشعار إذا كان مطلوباً
                    if self.send_notification_checkbox.isChecked():
                        self.send_transfer_notification(student, new_group_name)

                else:
                    failed_transfers.append(student['name'])

            except Exception as e:
                failed_transfers.append(f"{student['name']} ({str(e)})")

            # تحديث شريط التقدم
            self.progress_bar.setValue(i + 1)

        # إخفاء شريط التقدم
        self.progress_bar.setVisible(False)
        self.transfer_btn.setEnabled(True)

        # عرض النتائج
        self.show_transfer_results(successful_transfers, failed_transfers)

        # تحديث البيانات
        self.load_groups()
        self.load_students()
        self.update_selection()

    def send_transfer_notification(self, student, new_group_name):
        """إرسال إشعار نقل الطالب"""
        try:
            # هنا يمكن إضافة كود إرسال الرسائل
            # مثل SMS أو WhatsApp أو البريد الإلكتروني
            pass
        except Exception as e:
            print(f"خطأ في إرسال الإشعار: {e}")

    def show_transfer_results(self, successful_count, failed_list):
        """عرض نتائج عملية النقل"""
        if successful_count > 0 and not failed_list:
            # نجح النقل بالكامل
            QMessageBox.information(
                self, "تم النقل بنجاح",
                f"تم نقل {successful_count} طالب بنجاح إلى المجموعة الجديدة.\n\n"
                "تم تحديث جميع البيانات المرتبطة."
            )
        elif successful_count > 0 and failed_list:
            # نجح جزئياً
            failed_names = "\n".join(failed_list[:5])  # أول 5 فقط
            if len(failed_list) > 5:
                failed_names += f"\n... و {len(failed_list) - 5} آخرين"

            QMessageBox.warning(
                self, "نقل جزئي",
                f"تم نقل {successful_count} طالب بنجاح.\n\n"
                f"فشل في نقل {len(failed_list)} طالب:\n{failed_names}"
            )
        else:
            # فشل كامل
            QMessageBox.critical(
                self, "فشل في النقل",
                "فشل في نقل جميع الطلاب المحددين.\n"
                "يرجى المحاولة مرة أخرى أو التحقق من البيانات."
            )

    def apply_styles(self):
        """تطبيق الأنماط"""
        style = """
        QWidget {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            font-size: 12px;
            background-color: #f8f9fa;
        }

        #titleLabel {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        #previewLabel {
            font-size: 14px;
            font-weight: bold;
            color: #34495e;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 4px;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
            background-color: white;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #2c3e50;
        }

        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: bold;
        }

        QPushButton:hover {
            background-color: #2980b9;
        }

        QPushButton:pressed {
            background-color: #21618c;
        }

        QPushButton:disabled {
            background-color: #bdc3c7;
            color: #7f8c8d;
        }

        QTableWidget {
            background-color: white;
            border: 1px solid #bdc3c7;
            border-radius: 6px;
            gridline-color: #ecf0f1;
        }

        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #ecf0f1;
        }

        QTableWidget::item:selected {
            background-color: #3498db;
            color: white;
        }

        QHeaderView::section {
            background-color: #34495e;
            color: white;
            padding: 10px;
            border: none;
            font-weight: bold;
        }

        QComboBox, QLineEdit {
            padding: 8px;
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            background-color: white;
        }

        QComboBox:focus, QLineEdit:focus {
            border-color: #3498db;
        }

        QTextEdit {
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            background-color: white;
            padding: 5px;
        }

        QTextEdit:focus {
            border-color: #3498db;
        }

        QListWidget {
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            background-color: white;
        }

        QListWidget::item {
            padding: 5px;
            border-bottom: 1px solid #ecf0f1;
        }

        QListWidget::item:selected {
            background-color: #3498db;
            color: white;
        }

        QCheckBox {
            spacing: 5px;
        }

        QCheckBox::indicator {
            width: 18px;
            height: 18px;
        }

        QCheckBox::indicator:unchecked {
            border: 2px solid #bdc3c7;
            border-radius: 3px;
            background-color: white;
        }

        QCheckBox::indicator:checked {
            border: 2px solid #27ae60;
            border-radius: 3px;
            background-color: #27ae60;
        }

        QProgressBar {
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
        }

        QProgressBar::chunk {
            background-color: #27ae60;
            border-radius: 3px;
        }
        """

        self.setStyleSheet(style)
    
    def search_students(self):
        """البحث في الطلاب"""
        search_text = self.search_input.text().strip().lower()
        
        if not search_text:
            # إظهار جميع الطلاب
            for row in range(self.students_table.rowCount()):
                self.students_table.setRowHidden(row, False)
            return
        
        for row in range(self.students_table.rowCount()):
            show_row = False
            
            # البحث في الكود والاسم
            for col in [1, 2]:  # الكود والاسم
                item = self.students_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            
            self.students_table.setRowHidden(row, not show_row)
