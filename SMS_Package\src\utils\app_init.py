import sys
import logging
from typing import Optional, <PERSON><PERSON>
from datetime import datetime, timed<PERSON>ta

from PyQt5.QtWidgets import QApplication, QMessageBox

from ..licensing.license_manager import LicenseManager, LicenseState
from ..ui.license_check_dialog import LicenseCheckDialog
from ..config import APP_NAME, APP_VERSION, LOG_FILE

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_license(license_manager: LicenseManager) -> Tuple[bool, str]:
    """
    Check if the application has a valid license
    
    Returns:
        Tuple[bool, str]: (is_valid, message)
    """
    try:
        state = license_manager.get_license_state()
        
        # If unlicensed, show activation dialog
        if state == LicenseState.UNLICENSED:
            return False, "الرجاء تفعيل الترخيص لاستخدام التطبيق"
            
        # Check if license is valid
        if not license_manager.is_license_valid():
            return False, "الترخيص غير صالح أو منتهي الصلاحية"
            
        # Check if license is expired
        if state in [LicenseState.EXPIRED, LicenseState.BLOCKED]:
            return False, "انتهت صلاحية الترخيص. الرجاء التجديد"
            
        # Check if in grace period
        if state == LicenseState.GRACE:
            days_left = license_manager.get_license_info().get('days_remaining', 0)
            return True, f"فترة السماح: {days_left} أيام متبقية"
            
        # Check if demo version
        if state == LicenseState.DEMO:
            days_left = license_manager.get_license_info().get('days_remaining', 0)
            return True, f"النسخة التجريبية: {days_left} أيام متبقية"
            
        # Valid license
        return True, "الترخيص صالح"
        
    except Exception as e:
        logger.error(f"Error checking license: {str(e)}")
        return False, f"خطأ في التحقق من الترخيص: {str(e)}"

def show_license_dialog(license_manager: LicenseManager, parent=None) -> bool:
    """
    Show license check/activation dialog
    
    Returns:
        bool: True if user has a valid license and wants to continue, False otherwise
    """
    try:
        dialog = LicenseCheckDialog(license_manager, parent)
        result = dialog.exec_()
        
        # If user clicked continue
        if result == LicenseCheckDialog.Accepted:
            return True
            
        # If user clicked quit or closed the dialog
        return False
        
    except Exception as e:
        logger.error(f"Error showing license dialog: {str(e)}")
        return False

def init_application() -> Tuple[bool, Optional[LicenseManager], str]:
    """
    Initialize the application with license check
    
    Returns:
        Tuple[bool, Optional[LicenseManager], str]: 
            (success, license_manager, message)
    """
    try:
        # Initialize license manager
        license_manager = LicenseManager()
        
        # Check license validity
        is_valid, message = check_license(license_manager)
        
        # If license is not valid, show activation dialog
        if not is_valid:
            logger.warning(f"License check failed: {message}")
            return False, None, message
            
        return True, license_manager, "تم تهيئة التطبيق بنجاح"
        
    except Exception as e:
        error_msg = f"فشل تهيئة التطبيق: {str(e)}"
        logger.error(error_msg)
        return False, None, error_msg

def run_application() -> int:
    """
    Main entry point for the application
    
    Returns:
        int: Exit code (0 for success, non-zero for error)
    """
    try:
        # Create Qt application
        app = QApplication(sys.argv)
        app.setApplicationName(APP_NAME)
        app.setApplicationVersion(APP_VERSION)
        
        # Initialize license
        success, license_manager, message = init_application()
        
        # If initialization failed, show error and exit
        if not success:
            QMessageBox.critical(
                None,
                "خطأ",
                f"لا يمكن بدء التطبيق:\n{message}"
            )
            return 1
            
        # Show license dialog
        if not show_license_dialog(license_manager):
            logger.info("Application closed by user")
            return 0
            
        # TODO: Initialize and show main application window
        # main_window = MainWindow(license_manager)
        # main_window.show()
        
        # Run application event loop
        return app.exec_()
        
    except Exception as e:
        logger.critical(f"Fatal error: {str(e)}", exc_info=True)
        return 1
    finally:
        # Cleanup resources
        logger.info("Application shutdown")

if __name__ == "__main__":
    sys.exit(run_application())
