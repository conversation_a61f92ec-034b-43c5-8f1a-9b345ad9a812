@echo off
chcp 65001 >nul
title نظام إدارة الطلاب - بدون QR Code

cls
color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة الطلاب                        ║
echo ║                   بدون نظام QR Code                         ║
echo ║                   نظام إدارة الطلاب المتطور                           ║
echo ║              نظام شامل لإدارة الطلاب                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 تشغيل النظام الأساسي بدون QR Code...
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود
    goto error
)

echo ✅ Python موجود
python --version

echo.
echo 📦 تثبيت المكتبات الأساسية فقط...

REM تثبيت المكتبات الأساسية فقط
python -m pip install --quiet PyQt5

echo ✅ تم تثبيت PyQt5

echo.
echo 🎯 تشغيل النظام...
echo.

REM إنشاء ملف تشغيل مؤقت بدون QR Code
python -c "
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# تعطيل استيراد QR Code مؤقتاً
import src.ui.main_window
original_import = src.ui.main_window.__import__

def safe_import(name, *args, **kwargs):
    if 'qr_attendance_window' in name:
        # إرجاع كلاس فارغ بدلاً من QR Code
        class DummyQRWindow:
            def __init__(self, *args, **kwargs):
                pass
        return type('module', (), {'QRAttendanceWindow': DummyQRWindow})()
    return original_import(name, *args, **kwargs)

src.ui.main_window.__import__ = safe_import

try:
    app = QApplication(sys.argv)
    app.setApplicationName('نظام إدارة الطلاب')
    app.setApplicationVersion('1.0')
    app.setOrganizationName('نظام إدارة الطلاب المتطور')
    
    font = QFont('Segoe UI', 10)
    app.setFont(font)
    app.setLayoutDirection(Qt.RightToLeft)
    
    from src.database.database_manager import DatabaseManager
    from src.ui.login_window import LoginWindow
    
    print('📊 تهيئة قاعدة البيانات...')
    db_manager = DatabaseManager()
    db_manager.initialize_database()
    
    print('🔐 فتح نافذة تسجيل الدخول...')
    login_window = LoginWindow()
    login_window.show()
    
    print('✅ تم تشغيل التطبيق بنجاح!')
    sys.exit(app.exec_())
    
except Exception as e:
    print(f'❌ خطأ: {e}')
    import traceback
    traceback.print_exc()
"

if not errorlevel 1 goto success

echo ❌ فشل في تشغيل النظام
goto error

:success
echo.
echo ✅ تم تشغيل النظام بنجاح!
goto end

:error
echo.
echo ❌ فشل في تشغيل النظام
echo.
echo 🔧 جرب:
echo 1. RUN_COMPACT_FINAL.bat
echo 2. START_EVERYTHING.bat
echo 3. تأكد من تثبيت Python
echo.

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      معلومات النظام                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🏠 المميزات المتاحة (بدون QR Code):
echo    📊 الصفحة الرئيسية مع الإحصائيات
echo    👥 إدارة الطلاب الكاملة
echo    📋 تسجيل الحضور اليدوي
echo    📊 إدارة الدرجات
echo    📈 التقارير PDF و Excel
echo    ⚙️ الإعدادات
echo.
echo 💡 ملاحظة: نظام QR Code معطل مؤقتاً لحل مشاكل المكتبات
echo.
echo 🎓 تم التطوير بواسطة: م/ حسام أسامة
echo.
pause
