@echo off
chcp 65001 >nul
title نظام إدارة الطلاب المحدث - نظام إدارة الطلاب المتطور

cls
color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                نظام إدارة الطلاب المحدث                     ║
echo ║                   نظام إدارة الطلاب المتطور                           ║
echo ║              نظام شامل لإدارة الطلاب                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 التحديثات الجديدة:
echo.
echo ✅ توليد QR Code تلقائياً عند إضافة طالب
echo ✅ حضور بالاسم بدلاً من الكود فقط
echo ✅ بحث ذكي بالاسم
echo ✅ نظام فيديوهات تعليمية محمية (منفصل)
echo.

echo 🚀 تشغيل النظام المحدث...
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود
    echo 🏪 فتح Microsoft Store لتثبيت Python...
    start ms-windows-store://pdp/?ProductId=9NRWMJP3717K
    goto end
)

echo ✅ Python موجود
python --version

echo.
echo 📦 تثبيت المكتبات المطلوبة...

REM تثبيت المكتبات الأساسية
python -m pip install --quiet PyQt5

REM تثبيت مكتبات QR Code (اختيارية)
python -m pip install --quiet qrcode[pil] Pillow

echo ✅ تم تثبيت المكتبات

echo.
echo 🎯 تشغيل النظام...
echo.

python main.py

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    التحديثات الجديدة                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🆕 المميزات الجديدة:
echo.
echo 1️⃣ توليد QR Code تلقائي:
echo    ✅ عند إضافة طالب جديد
echo    ✅ يتم حفظ QR Code في مجلد qr_codes/
echo    ✅ يحتوي على بيانات الطالب مشفرة
echo    ✅ جاهز للطباعة والتوزيع
echo.
echo 2️⃣ حضور بالاسم:
echo    ✅ بحث بالاسم في تسجيل الحضور
echo    ✅ اقتراحات تلقائية أثناء الكتابة
echo    ✅ اختيار من قائمة منسدلة
echo    ✅ تسجيل مباشر بالاسم
echo.
echo 3️⃣ نظام البحث الذكي:
echo    ✅ بحث فوري أثناء الكتابة
echo    ✅ عرض النتائج المطابقة
echo    ✅ اختيار سريع من القائمة
echo    ✅ ملء تلقائي للبيانات
echo.
echo 🎥 نظام الفيديوهات المحمي (منفصل):
echo    📁 ملف منفصل: video_exam_system.py
echo    🔒 حماية متقدمة من التسجيل
echo    📚 ربط الفيديو بالامتحان
echo    ⚡ تشغيل: RUN_VIDEO_SYSTEM.bat
echo.
echo 🧭 كيفية الاستخدام:
echo.
echo 📝 إضافة طالب جديد:
echo    1. اذهب لإدارة الطلاب
echo    2. اضغط "طالب جديد"
echo    3. املأ البيانات
echo    4. اضغط "حفظ"
echo    5. سيتم توليد QR Code تلقائياً
echo.
echo 📋 تسجيل الحضور:
echo    1. اذهب لتسجيل الحضور
echo    2. أدخل كود الطالب أو اسمه
echo    3. اختر من القائمة المنسدلة
echo    4. اضغط "تسجيل حضور"
echo.
echo 🔍 البحث بالاسم:
echo    1. ابدأ بكتابة اسم الطالب
echo    2. ستظهر اقتراحات تلقائية
echo    3. اختر الطالب المطلوب
echo    4. سيتم ملء البيانات تلقائياً
echo.
echo 📁 ملفات QR Code:
echo    📂 المجلد: qr_codes/
echo    🖼️ الصيغة: PNG
echo    📛 الاسم: qr_[كود_الطالب].png
echo    🖨️ جاهز للطباعة
echo.
echo 🎓 تم التطوير بواسطة: م/ حسام أسامة
echo    مهندس برمجيات - مطور تطبيقات
echo    مصمم خصيصاً لنظام إدارة الطلاب المتطور
echo.
echo 🏆 أقوى نظام إدارة طلاب عربي محدث!
echo.
pause
