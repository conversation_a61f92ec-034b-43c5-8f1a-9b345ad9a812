@echo off
title نظام إدارة الطلاب - نظام إدارة الطلاب المتطور

cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة الطلاب                        ║
echo ║                   نظام إدارة الطلاب المتطور                           ║
echo ║              نظام شامل لإدارة الطلاب                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 تشغيل النظام...
echo.

REM محاولة تشغيل البرنامج بطرق مختلفة

REM الطريقة الأولى: py launcher
py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python!
    py --version
    echo.
    echo 🎯 بدء تشغيل نظام إدارة الطلاب...
    echo.
    py main.py
    goto end
)

REM الطريقة الثانية: python command
python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python!
    python --version
    echo.
    echo 🎯 بدء تشغيل نظام إدارة الطلاب...
    echo.
    python main.py
    goto end
)

REM الطريقة الثالثة: python3 command
python3 --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python3!
    python3 --version
    echo.
    echo 🎯 بدء تشغيل نظام إدارة الطلاب...
    echo.
    python3 main.py
    goto end
)

echo ❌ لم يتم العثور على Python!
echo.
echo 💡 الحلول المتاحة:
echo.
echo 1️⃣ تثبيت Python من Microsoft Store:
echo    - افتح Microsoft Store
echo    - ابحث عن "Python"
echo    - ثبت Python 3.9 أو أحدث
echo.
echo 2️⃣ تثبيت Python من الموقع الرسمي:
echo    - اذهب إلى: https://python.org/downloads
echo    - حمل وثبت Python
echo    - تأكد من تحديد "Add Python to PATH"
echo.
echo 3️⃣ بناء ملف EXE:
echo    - شغل: build_simple.bat
echo    - احصل على ملف EXE يعمل بدون Python
echo.

echo 🌐 فتح صفحة تحميل Python...
start https://python.org/downloads

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      معلومات تسجيل الدخول                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.
echo 📚 المميزات المتاحة:
echo    ✅ إدارة الطلاب
echo    ✅ تسجيل الحضور
echo    ✅ إدارة الدرجات
echo    ✅ التقارير الاحترافية
echo    ✅ النسخ الاحتياطي
echo.
echo 🎓 تم التطوير خصيصاً لنظام إدارة الطلاب المتطور
echo    نظام شامل لإدارة الطلاب
echo.
pause
