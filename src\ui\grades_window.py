# -*- coding: utf-8 -*-
"""
نافذة إدارة الدرجات
Grades Management Window
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                            QComboBox, QMessageBox, QFrame, QGroupBox, QFormLayout,
                            QHeaderView, QAbstractItemView, QSplitter, QDateEdit,
                            QTabWidget, QSpinBox, QDoubleSpinBox)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

from datetime import date
from ..models.student import Student
from ..models.grades import Grades
from ..models.groups import Groups
from ..database.database_manager import DatabaseManager
from ..utils.messaging import messaging_system

class GradesWindow(QWidget):
    """نافذة إدارة الدرجات"""
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
        self.student_model = Student(db_manager)
        self.grades_model = Grades(db_manager)
        self.groups_model = Groups(db_manager)
        self.current_grade_id = None
        
        self.init_ui()
        self.setup_styles()
        self.load_students()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة الدرجات")
        self.setGeometry(100, 100, 1200, 800)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # التبويبات
        tabs = QTabWidget()
        
        # تبويب إدخال الدرجات
        input_tab = self.create_input_tab()
        tabs.addTab(input_tab, "إدخال الدرجات")
        
        # تبويب عرض الدرجات
        view_tab = self.create_view_tab()
        tabs.addTab(view_tab, "عرض الدرجات")
        
        # تبويب الإحصائيات
        stats_tab = self.create_stats_tab()
        tabs.addTab(stats_tab, "الإحصائيات")
        
        main_layout.addWidget(tabs)
        self.setLayout(main_layout)
    
    def create_input_tab(self):
        """إنشاء تبويب إدخال الدرجات"""
        widget = QWidget()
        layout = QHBoxLayout()
        
        # الجانب الأيسر - نموذج إدخال الدرجة
        input_group = QGroupBox("إدخال درجة جديدة")
        input_layout = QFormLayout()
        
        # اختيار الطالب
        self.student_combo = QComboBox()
        self.student_combo.currentTextChanged.connect(self.on_student_selected)
        input_layout.addRow("الطالب:", self.student_combo)
        
        # معلومات الطالب
        self.student_info_label = QLabel("اختر طالباً لعرض معلوماته")
        self.student_info_label.setObjectName("studentInfo")
        input_layout.addRow("المعلومات:", self.student_info_label)

        # البحث بالكود
        search_layout = QHBoxLayout()
        self.search_code_input = QLineEdit()
        self.search_code_input.setPlaceholderText("ابحث بكود الطالب...")
        search_button = QPushButton("بحث")
        search_button.clicked.connect(self.search_by_code)
        search_layout.addWidget(self.search_code_input)
        search_layout.addWidget(search_button)
        input_layout.addRow("البحث بالكود:", search_layout)
        
        # نوع الامتحان
        self.exam_type_input = QLineEdit()
        self.exam_type_input.setPlaceholderText("مثال: امتحان شهري، واجب، مشاركة")
        input_layout.addRow("نوع الامتحان:", self.exam_type_input)
        
        # الدرجة المحصلة
        self.score_input = QDoubleSpinBox()
        self.score_input.setRange(0, 100)
        self.score_input.setDecimals(1)
        self.score_input.setSuffix(" درجة")
        input_layout.addRow("الدرجة المحصلة:", self.score_input)
        
        # الدرجة الكاملة
        self.max_score_input = QDoubleSpinBox()
        self.max_score_input.setRange(1, 100)
        self.max_score_input.setDecimals(1)
        self.max_score_input.setValue(100)
        self.max_score_input.setSuffix(" درجة")
        input_layout.addRow("الدرجة الكاملة:", self.max_score_input)
        
        # تاريخ الامتحان
        self.exam_date_input = QDateEdit()
        self.exam_date_input.setDate(QDate.currentDate())
        input_layout.addRow("تاريخ الامتحان:", self.exam_date_input)
        
        # ملاحظات
        self.notes_input = QLineEdit()
        self.notes_input.setPlaceholderText("ملاحظات إضافية (اختياري)")
        input_layout.addRow("ملاحظات:", self.notes_input)
        
        # أزرار العمليات
        buttons_layout = QVBoxLayout()
        
        self.add_grade_button = QPushButton("إضافة الدرجة")
        self.add_grade_button.clicked.connect(self.add_grade)
        buttons_layout.addWidget(self.add_grade_button)
        
        self.update_grade_button = QPushButton("تحديث الدرجة")
        self.update_grade_button.clicked.connect(self.update_grade)
        self.update_grade_button.setEnabled(False)
        buttons_layout.addWidget(self.update_grade_button)
        
        self.delete_grade_button = QPushButton("حذف الدرجة")
        self.delete_grade_button.clicked.connect(self.delete_grade)
        self.delete_grade_button.setEnabled(False)
        buttons_layout.addWidget(self.delete_grade_button)
        
        self.clear_form_button = QPushButton("مسح النموذج")
        self.clear_form_button.clicked.connect(self.clear_form)
        buttons_layout.addWidget(self.clear_form_button)
        
        input_layout.addRow("", buttons_layout)
        input_group.setLayout(input_layout)
        
        # الجانب الأيمن - درجات الطالب المحدد
        student_grades_group = QGroupBox("درجات الطالب المحدد")
        student_grades_layout = QVBoxLayout()
        
        # جدول درجات الطالب
        self.student_grades_table = QTableWidget()
        self.student_grades_table.setColumnCount(6)
        self.student_grades_table.setHorizontalHeaderLabels([
            "المادة", "نوع الامتحان", "الدرجة", "من", "النسبة", "التاريخ"
        ])
        
        header = self.student_grades_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        self.student_grades_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.student_grades_table.itemSelectionChanged.connect(self.on_grade_selected)
        
        student_grades_layout.addWidget(self.student_grades_table)
        
        # إحصائيات الطالب
        self.student_stats_label = QLabel("اختر طالباً لعرض إحصائياته")
        student_grades_layout.addWidget(self.student_stats_label)
        
        student_grades_group.setLayout(student_grades_layout)
        
        # تقسيم المساحة
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(input_group)
        splitter.addWidget(student_grades_group)
        splitter.setSizes([400, 600])
        
        layout.addWidget(splitter)
        widget.setLayout(layout)
        return widget
    
    def create_view_tab(self):
        """إنشاء تبويب عرض الدرجات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # شريط التحكم
        control_layout = QHBoxLayout()
        
        # فلاتر
        self.filter_group_combo = QComboBox()
        self.filter_group_combo.addItem("جميع المجموعات")
        self.populate_group_filter()
        self.filter_group_combo.currentTextChanged.connect(self.filter_grades)

        self.filter_grade_combo = QComboBox()
        self.filter_grade_combo.addItem("الكل")
        self.filter_grade_combo.currentTextChanged.connect(self.filter_grades)

        self.filter_exam_type_combo = QComboBox()
        self.filter_exam_type_combo.addItem("الكل")
        self.filter_exam_type_combo.currentTextChanged.connect(self.filter_grades)

        self.refresh_button = QPushButton("تحديث")
        self.refresh_button.clicked.connect(self.load_all_grades)

        control_layout.addWidget(QLabel("المجموعة:"))
        control_layout.addWidget(self.filter_group_combo)
        control_layout.addWidget(QLabel("الصف:"))
        control_layout.addWidget(self.filter_grade_combo)
        control_layout.addWidget(QLabel("نوع الامتحان:"))
        control_layout.addWidget(self.filter_exam_type_combo)
        control_layout.addWidget(self.refresh_button)
        control_layout.addStretch()
        
        layout.addLayout(control_layout)
        
        # جدول جميع الدرجات
        self.all_grades_table = QTableWidget()
        self.all_grades_table.setColumnCount(9)
        self.all_grades_table.setHorizontalHeaderLabels([
            "الكود", "الاسم", "المجموعة", "الصف", "المادة", "نوع الامتحان", "الدرجة", "من", "التاريخ"
        ])
        
        header = self.all_grades_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        self.all_grades_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.all_grades_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.all_grades_table)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        self.export_grades_button = QPushButton("تصدير لـ Excel")
        self.export_grades_button.clicked.connect(self.export_grades)
        
        self.bulk_input_button = QPushButton("إدخال مجمع")
        self.bulk_input_button.clicked.connect(self.bulk_input_grades)
        
        buttons_layout.addWidget(self.export_grades_button)
        buttons_layout.addWidget(self.bulk_input_button)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        widget.setLayout(layout)
        return widget
    
    def create_stats_tab(self):
        """إنشاء تبويب الإحصائيات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إحصائيات عامة
        general_stats_group = QGroupBox("الإحصائيات العامة")
        general_stats_layout = QVBoxLayout()
        
        self.general_stats_label = QLabel("جاري تحميل الإحصائيات...")
        general_stats_layout.addWidget(self.general_stats_label)
        
        general_stats_group.setLayout(general_stats_layout)
        layout.addWidget(general_stats_group)
        
        # أفضل الطلاب
        top_students_group = QGroupBox("أفضل الطلاب")
        top_students_layout = QHBoxLayout()
        
        # أفضل في الجغرافيا
        geography_layout = QVBoxLayout()
        geography_layout.addWidget(QLabel("الجغرافيا:"))
        self.top_geography_table = QTableWidget()
        self.top_geography_table.setColumnCount(3)
        self.top_geography_table.setHorizontalHeaderLabels(["الاسم", "الصف", "الدرجة"])
        geography_layout.addWidget(self.top_geography_table)
        
        # أفضل في التاريخ
        history_layout = QVBoxLayout()
        history_layout.addWidget(QLabel("التاريخ:"))
        self.top_history_table = QTableWidget()
        self.top_history_table.setColumnCount(3)
        self.top_history_table.setHorizontalHeaderLabels(["الاسم", "الصف", "الدرجة"])
        history_layout.addWidget(self.top_history_table)
        
        top_students_layout.addLayout(geography_layout)
        top_students_layout.addLayout(history_layout)
        
        top_students_group.setLayout(top_students_layout)
        layout.addWidget(top_students_group)
        
        # زر تحديث الإحصائيات
        refresh_stats_button = QPushButton("تحديث الإحصائيات")
        refresh_stats_button.clicked.connect(self.load_statistics)
        layout.addWidget(refresh_stats_button)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def load_students(self):
        """تحميل قائمة الطلاب"""
        students = self.student_model.get_all_students()

        self.student_combo.clear()
        self.student_combo.addItem("اختر طالباً...")

        for student in students:
            display_text = f"{student['full_name']} ({student['student_code']}) - {student['grade']}"
            self.student_combo.addItem(display_text, student['id'])

        # تحميل فلاتر الصفوف
        grades = set(student['grade'] for student in students)
        self.filter_grade_combo.clear()
        self.filter_grade_combo.addItem("الكل")
        self.filter_grade_combo.addItems(sorted(grades))

        # تحميل أنواع الامتحانات
        exam_types = self.grades_model.get_exam_types()
        self.filter_exam_type_combo.clear()
        self.filter_exam_type_combo.addItem("الكل")
        self.filter_exam_type_combo.addItems(exam_types)

    def on_student_selected(self):
        """معالجة اختيار طالب"""
        current_index = self.student_combo.currentIndex()

        if current_index > 0:  # تجاهل "اختر طالباً..."
            student_id = self.student_combo.currentData()
            student = self.student_model.get_student_by_id(student_id)

            if student:
                # عرض معلومات الطالب
                info_text = f"{student['full_name']} | {student['grade']} | {student['gender']}"
                self.student_info_label.setText(info_text)

                # تحميل درجات الطالب
                self.load_student_grades(student_id)
                self.update_student_stats(student_id)
        else:
            self.student_info_label.setText("اختر طالباً لعرض معلوماته")
            self.student_grades_table.setRowCount(0)
            self.student_stats_label.setText("اختر طالباً لعرض إحصائياته")

    def load_student_grades(self, student_id):
        """تحميل درجات طالب معين"""
        grades = self.grades_model.get_student_grades(student_id)

        self.student_grades_table.setRowCount(len(grades))

        for row, grade in enumerate(grades):
            self.student_grades_table.setItem(row, 0, QTableWidgetItem(grade['subject']))
            self.student_grades_table.setItem(row, 1, QTableWidgetItem(grade['exam_type']))
            self.student_grades_table.setItem(row, 2, QTableWidgetItem(f"{grade['score']:.1f}"))
            self.student_grades_table.setItem(row, 3, QTableWidgetItem(f"{grade['max_score']:.1f}"))

            percentage = (grade['score'] / grade['max_score']) * 100
            self.student_grades_table.setItem(row, 4, QTableWidgetItem(f"{percentage:.1f}%"))

            exam_date = grade.get('exam_date', '')
            self.student_grades_table.setItem(row, 5, QTableWidgetItem(str(exam_date)))

            # حفظ معرف الدرجة
            self.student_grades_table.item(row, 0).setData(Qt.UserRole, grade['id'])

    def update_student_stats(self, student_id):
        """تحديث إحصائيات الطالب"""
        student = self.student_model.get_student_by_id(student_id)

        if student:
            geography_score = student.get('geography_score', 0)
            history_score = student.get('history_score', 0)
            average = (geography_score + history_score) / 2

            stats_text = f"""
إحصائيات الطالب:
- متوسط الجغرافيا: {geography_score:.1f}%
- متوسط التاريخ: {history_score:.1f}%
- المتوسط العام: {average:.1f}%
            """

            self.student_stats_label.setText(stats_text.strip())

    def on_grade_selected(self):
        """معالجة اختيار درجة من الجدول"""
        current_row = self.student_grades_table.currentRow()

        if current_row >= 0:
            grade_id_item = self.student_grades_table.item(current_row, 0)
            if grade_id_item:
                self.current_grade_id = grade_id_item.data(Qt.UserRole)

                # تحميل بيانات الدرجة في النموذج
                self.load_grade_data(self.current_grade_id)

                # تفعيل أزرار التحديث والحذف
                self.add_grade_button.setEnabled(False)
                self.update_grade_button.setEnabled(True)
                self.delete_grade_button.setEnabled(True)

    def load_grade_data(self, grade_id):
        """تحميل بيانات درجة معينة في النموذج"""
        # هذه دالة مساعدة - في التطبيق الحقيقي نحتاج دالة في النموذج لجلب درجة بالمعرف
        pass

    def clear_form(self):
        """مسح نموذج إدخال الدرجة"""
        self.student_combo.setCurrentIndex(0)
        self.exam_type_input.clear()
        self.score_input.setValue(0)
        self.max_score_input.setValue(100)
        self.exam_date_input.setDate(QDate.currentDate())
        self.notes_input.clear()
        self.search_code_input.clear()

        self.current_grade_id = None
        self.add_grade_button.setEnabled(True)
        self.update_grade_button.setEnabled(False)
        self.delete_grade_button.setEnabled(False)

    def validate_form(self):
        """التحقق من صحة البيانات"""
        if self.student_combo.currentIndex() == 0:
            QMessageBox.warning(self, "خطأ", "يرجى اختيار طالب")
            return False

        if not self.exam_type_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال نوع الامتحان")
            return False

        if self.score_input.value() > self.max_score_input.value():
            QMessageBox.warning(self, "خطأ", "الدرجة المحصلة لا يمكن أن تكون أكبر من الدرجة الكاملة")
            return False

        return True

    def add_grade(self):
        """إضافة درجة جديدة"""
        if not self.validate_form():
            return

        student_id = self.student_combo.currentData()
        # استخدام مادة افتراضية أو من إعدادات النظام
        subject = "عام"  # يمكن تخصيصها لاحقاً من الإعدادات
        exam_type = self.exam_type_input.text().strip()
        score = self.score_input.value()
        max_score = self.max_score_input.value()
        exam_date = self.exam_date_input.date().toPyDate()
        notes = self.notes_input.text().strip()

        grade_id = self.grades_model.add_grade(
            student_id, subject, exam_type, score, max_score, exam_date, notes
        )

        if grade_id:
            QMessageBox.information(self, "نجح", "تم إضافة الدرجة بنجاح")

            # إرسال رسائل تلقائية حسب الدرجة
            try:
                # الحصول على بيانات الطالب
                student_data = None
                for i in range(self.student_combo.count()):
                    if self.student_combo.itemData(i) == student_id:
                        student_text = self.student_combo.itemText(i)
                        # استخراج اسم الطالب من النص
                        student_name = student_text.split(' (')[0]

                        # البحث عن بيانات الطالب الكاملة
                        students = self.student_model.get_all_students()
                        for student in students:
                            if student['id'] == student_id:
                                student_data = student
                                break
                        break

                if student_data and student_data.get('parent_phone'):
                    percentage = (score / max_score) * 100

                    # رسالة للدرجات الممتازة (85% فأكثر)
                    if percentage >= 85:
                        messaging_system.send_excellent_grade_notification(
                            student_data['full_name'],
                            student_data['parent_phone'],
                            subject,
                            score,
                            max_score
                        )
                    # رسالة للدرجات المنخفضة (أقل من 50%)
                    elif percentage < 50:
                        messaging_system.send_low_grade_notification(
                            student_data['full_name'],
                            student_data['parent_phone'],
                            subject,
                            score,
                            max_score
                        )
            except Exception as e:
                print(f"خطأ في إرسال رسالة الدرجة: {e}")

            self.clear_form()
            self.load_student_grades(student_id)
            self.update_student_stats(student_id)
            self.load_all_grades()
        else:
            QMessageBox.critical(self, "خطأ", "فشل في إضافة الدرجة")

    def update_grade(self):
        """تحديث درجة موجودة"""
        if not self.current_grade_id or not self.validate_form():
            return

        score = self.score_input.value()
        max_score = self.max_score_input.value()
        notes = self.notes_input.text().strip()

        if self.grades_model.update_grade(self.current_grade_id, score, max_score, notes):
            QMessageBox.information(self, "نجح", "تم تحديث الدرجة بنجاح")

            student_id = self.student_combo.currentData()
            self.load_student_grades(student_id)
            self.update_student_stats(student_id)
            self.load_all_grades()
        else:
            QMessageBox.critical(self, "خطأ", "فشل في تحديث الدرجة")

    def delete_grade(self):
        """حذف درجة"""
        if not self.current_grade_id:
            return

        reply = QMessageBox.question(
            self, 'تأكيد الحذف',
            'هل تريد حذف هذه الدرجة؟',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            if self.grades_model.delete_grade(self.current_grade_id):
                QMessageBox.information(self, "نجح", "تم حذف الدرجة بنجاح")
                self.clear_form()

                student_id = self.student_combo.currentData()
                if student_id:
                    self.load_student_grades(student_id)
                    self.update_student_stats(student_id)

                self.load_all_grades()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حذف الدرجة")

    def load_all_grades(self):
        """تحميل جميع الدرجات"""
        # جلب جميع الدرجات من جميع المواد
        all_grades = self.grades_model.get_all_grades_with_student_info()

        self.all_grades_table.setRowCount(len(all_grades))

        for row, grade in enumerate(all_grades):
            self.all_grades_table.setItem(row, 0, QTableWidgetItem(grade['student_code']))
            self.all_grades_table.setItem(row, 1, QTableWidgetItem(grade['full_name']))
            self.all_grades_table.setItem(row, 2, QTableWidgetItem(grade.get('group_name', 'لا توجد مجموعة')))
            self.all_grades_table.setItem(row, 3, QTableWidgetItem(grade['grade']))
            self.all_grades_table.setItem(row, 4, QTableWidgetItem(grade['subject']))
            self.all_grades_table.setItem(row, 5, QTableWidgetItem(grade['exam_type']))
            self.all_grades_table.setItem(row, 6, QTableWidgetItem(f"{grade['score']:.1f}"))
            self.all_grades_table.setItem(row, 7, QTableWidgetItem(f"{grade['max_score']:.1f}"))

            exam_date = grade.get('exam_date', '')
            self.all_grades_table.setItem(row, 8, QTableWidgetItem(str(exam_date)))

    def filter_grades(self):
        """فلترة الدرجات"""
        # تطبيق الفلاتر على الجدول
        group_filter = self.filter_group_combo.currentText()
        grade_filter = self.filter_grade_combo.currentText()
        exam_type_filter = self.filter_exam_type_combo.currentText()

        for row in range(self.all_grades_table.rowCount()):
            show_row = True

            # فلتر المجموعة
            if group_filter != "جميع المجموعات":
                group_item = self.all_grades_table.item(row, 2)  # عمود المجموعة
                if group_item and group_item.text() != group_filter:
                    show_row = False

            # فلتر الصف
            if grade_filter != "الكل":
                grade_item = self.all_grades_table.item(row, 3)  # عمود الصف
                if grade_item and grade_item.text() != grade_filter:
                    show_row = False

            # فلتر نوع الامتحان
            if exam_type_filter != "الكل":
                exam_type_item = self.all_grades_table.item(row, 5)  # عمود نوع الامتحان
                if exam_type_item and exam_type_item.text() != exam_type_filter:
                    show_row = False

            self.all_grades_table.setRowHidden(row, not show_row)

    def export_grades(self):
        """تصدير الدرجات لملف Excel"""
        try:
            from openpyxl import Workbook
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الدرجات",
                f"grades_{date.today().strftime('%Y-%m-%d')}.xlsx",
                "Excel Files (*.xlsx)"
            )

            if file_path:
                wb = Workbook()
                ws = wb.active
                ws.title = "تقرير الدرجات"

                # العناوين
                headers = ["الكود", "الاسم", "الصف", "المادة", "نوع الامتحان", "الدرجة", "من", "التاريخ"]
                for col, header in enumerate(headers, 1):
                    ws.cell(row=1, column=col, value=header)

                # البيانات
                for row in range(self.all_grades_table.rowCount()):
                    if not self.all_grades_table.isRowHidden(row):
                        for col in range(8):
                            item = self.all_grades_table.item(row, col)
                            if item:
                                ws.cell(row=row+2, column=col+1, value=item.text())

                wb.save(file_path)
                QMessageBox.information(self, "تم", f"تم حفظ التقرير في:\n{file_path}")

        except ImportError:
            QMessageBox.warning(self, "خطأ", "مكتبة openpyxl غير مثبتة")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير: {str(e)}")

    def bulk_input_grades(self):
        """إدخال مجمع للدرجات"""
        QMessageBox.information(self, "قريباً", "ميزة الإدخال المجمع قيد التطوير")

    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            # إحصائيات عامة
            geography_stats = self.grades_model.get_grade_statistics("جغرافيا")
            history_stats = self.grades_model.get_grade_statistics("تاريخ")

            stats_text = "الإحصائيات العامة:\n\n"

            if geography_stats:
                for stat in geography_stats:
                    if stat['subject'] == 'جغرافيا':
                        stats_text += f"الجغرافيا:\n"
                        stats_text += f"- عدد الدرجات: {stat['total_grades']}\n"
                        stats_text += f"- المتوسط: {stat['average_percentage']:.1f}%\n"
                        stats_text += f"- أعلى درجة: {stat['max_percentage']:.1f}%\n"
                        stats_text += f"- أقل درجة: {stat['min_percentage']:.1f}%\n\n"

            if history_stats:
                for stat in history_stats:
                    if stat['subject'] == 'تاريخ':
                        stats_text += f"التاريخ:\n"
                        stats_text += f"- عدد الدرجات: {stat['total_grades']}\n"
                        stats_text += f"- المتوسط: {stat['average_percentage']:.1f}%\n"
                        stats_text += f"- أعلى درجة: {stat['max_percentage']:.1f}%\n"
                        stats_text += f"- أقل درجة: {stat['min_percentage']:.1f}%\n"

            self.general_stats_label.setText(stats_text)

            # أفضل الطلاب في الجغرافيا
            top_geography = self.grades_model.get_top_students("جغرافيا", 10)
            self.top_geography_table.setRowCount(len(top_geography))

            for row, student in enumerate(top_geography):
                self.top_geography_table.setItem(row, 0, QTableWidgetItem(student['full_name']))
                self.top_geography_table.setItem(row, 1, QTableWidgetItem(student['grade']))
                self.top_geography_table.setItem(row, 2, QTableWidgetItem(f"{student['score']:.1f}%"))

            # أفضل الطلاب في التاريخ
            top_history = self.grades_model.get_top_students("تاريخ", 10)
            self.top_history_table.setRowCount(len(top_history))

            for row, student in enumerate(top_history):
                self.top_history_table.setItem(row, 0, QTableWidgetItem(student['full_name']))
                self.top_history_table.setItem(row, 1, QTableWidgetItem(student['grade']))
                self.top_history_table.setItem(row, 2, QTableWidgetItem(f"{student['score']:.1f}%"))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الإحصائيات: {str(e)}")

    def setup_styles(self):
        """تطبيق الأنماط"""
        style = """
        #studentInfo {
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            padding: 8px;
            background-color: #ecf0f1;
            border-radius: 4px;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }

        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px;
            border-radius: 4px;
            font-weight: bold;
        }

        QPushButton:hover {
            background-color: #2980b9;
        }

        QPushButton:pressed {
            background-color: #21618c;
        }

        QPushButton:disabled {
            background-color: #bdc3c7;
        }

        QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
            padding: 5px;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
        }

        QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
            border-color: #3498db;
        }

        QTableWidget {
            gridline-color: #bdc3c7;
            background-color: white;
            alternate-background-color: #f8f9fa;
        }

        QTableWidget::item:selected {
            background-color: #3498db;
            color: white;
        }

        QHeaderView::section {
            background-color: #34495e;
            color: white;
            padding: 8px;
            border: none;
            font-weight: bold;
        }

        QTabWidget::pane {
            border: 1px solid #bdc3c7;
            background-color: white;
        }

        QTabBar::tab {
            background-color: #ecf0f1;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }

        QTabBar::tab:selected {
            background-color: white;
            border-bottom: 2px solid #3498db;
        }

        QTabBar::tab:hover {
            background-color: #d5dbdb;
        }
        """

        self.setStyleSheet(style)

    def populate_group_filter(self):
        """تعبئة قائمة فلتر المجموعات"""
        try:
            groups = self.groups_model.get_group_names()
            self.filter_group_combo.clear()
            self.filter_group_combo.addItem("جميع المجموعات")
            for group in groups:
                self.filter_group_combo.addItem(group)
        except Exception as e:
            print(f"خطأ في تحميل المجموعات: {e}")

    def search_by_code(self):
        """البحث عن طالب بالكود"""
        code = self.search_code_input.text().strip()
        if not code:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كود الطالب")
            return

        # البحث عن الطالب
        student = self.student_model.get_student_by_code(code)
        if student:
            # العثور على الطالب في القائمة وتحديده
            for i in range(self.student_combo.count()):
                if self.student_combo.itemData(i) == student['id']:
                    self.student_combo.setCurrentIndex(i)
                    self.load_student_grades()
                    QMessageBox.information(self, "تم العثور", f"تم العثور على الطالب: {student['full_name']}")
                    return

        QMessageBox.warning(self, "غير موجود", f"لم يتم العثور على طالب بالكود: {code}")

    def load_group_students(self, group_name):
        """تحميل طلاب مجموعة معينة"""
        if group_name == "جميع المجموعات":
            students = self.student_model.get_all_students()
        else:
            students = self.groups_model.get_students_in_group(group_name)

        # تحديث قائمة الطلاب
        self.student_combo.clear()
        self.student_combo.addItem("اختر طالباً", None)

        # ترتيب الطلاب حسب الكود
        students.sort(key=lambda x: int(x['student_code']) if x['student_code'].isdigit() else 0)

        for student in students:
            display_text = f"{student['student_code']} - {student['full_name']}"
            self.student_combo.addItem(display_text, student['id'])
