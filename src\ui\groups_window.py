# -*- coding: utf-8 -*-
"""
نافذة إدارة المجموعات
Groups Management Window
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                            QComboBox, QMessageBox, QFrame, QGroupBox, QFormLayout,
                            QHeaderView, QAbstractItemView, QSplitter, QListWidget)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from ..models.student import Student
from ..database.database_manager import DatabaseManager
from ..utils.styles import get_form_style, get_table_style, get_arabic_font_style
from ..utils.message_boxes import show_error_message, show_success_message, show_warning_message, show_confirmation_dialog
from .group_transfer_window import GroupTransferWindow

class GroupsWindow(QWidget):
    """نافذة إدارة المجموعات"""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.student_model = Student(db_manager)
        
        self.init_ui()
        self.load_groups()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة المجموعات - نظام إدارة الطلاب")
        self.setGeometry(100, 100, 1000, 700)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout()
        
        # الجانب الأيسر - قائمة المجموعات
        left_panel = self.create_groups_panel()
        main_layout.addWidget(left_panel, 1)
        
        # الجانب الأيمن - طلاب المجموعة
        right_panel = self.create_students_panel()
        main_layout.addWidget(right_panel, 2)
        
        self.setLayout(main_layout)
        
        # تطبيق الأنماط
        self.setStyleSheet(get_form_style() + get_arabic_font_style())
    
    def create_groups_panel(self):
        """إنشاء لوحة المجموعات"""
        group_box = QGroupBox("إدارة المجموعات")
        layout = QVBoxLayout()
        
        # نموذج إضافة مجموعة جديدة
        form_layout = QFormLayout()
        
        self.group_name_input = QLineEdit()
        self.group_name_input.setPlaceholderText("أدخل اسم المجموعة")
        form_layout.addRow("اسم المجموعة:", self.group_name_input)
        
        # زر إضافة مجموعة
        add_group_btn = QPushButton("إضافة مجموعة")
        add_group_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_group_btn.clicked.connect(self.add_group)
        
        layout.addLayout(form_layout)
        layout.addWidget(add_group_btn)
        
        # قائمة المجموعات
        self.groups_list = QListWidget()
        self.groups_list.itemClicked.connect(self.on_group_selected)
        layout.addWidget(QLabel("المجموعات الموجودة:"))
        layout.addWidget(self.groups_list)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        edit_btn = QPushButton("تعديل")
        edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        edit_btn.clicked.connect(self.edit_group)
        
        delete_btn = QPushButton("حذف")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_btn.clicked.connect(self.delete_group)
        
        buttons_layout.addWidget(edit_btn)
        buttons_layout.addWidget(delete_btn)
        layout.addLayout(buttons_layout)
        
        group_box.setLayout(layout)
        return group_box
    
    def create_students_panel(self):
        """إنشاء لوحة طلاب المجموعة"""
        group_box = QGroupBox("طلاب المجموعة")
        layout = QVBoxLayout()
        
        # معلومات المجموعة المحددة
        self.selected_group_label = QLabel("لم يتم تحديد مجموعة")
        self.selected_group_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin: 5px;
            }
        """)
        layout.addWidget(self.selected_group_label)
        
        # جدول الطلاب
        self.students_table = QTableWidget()
        self.students_table.setColumnCount(6)
        self.students_table.setHorizontalHeaderLabels([
            "كود الطالب", "الاسم", "المرحلة", "الصف", "الهاتف", "ولي الأمر"
        ])
        
        # تنسيق الجدول
        header = self.students_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.students_table.setAlternatingRowColors(True)
        self.students_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        
        layout.addWidget(self.students_table)
        
        # أزرار إدارة الطلاب
        buttons_layout = QHBoxLayout()
        
        add_student_btn = QPushButton("إضافة طالب للمجموعة")
        add_student_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_student_btn.clicked.connect(self.add_student_to_group)
        
        remove_student_btn = QPushButton("إزالة طالب من المجموعة")
        remove_student_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        remove_student_btn.clicked.connect(self.remove_student_from_group)

        # زر نقل الطلاب
        transfer_students_btn = QPushButton("نقل الطلاب بين المجموعات")
        transfer_students_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        transfer_students_btn.clicked.connect(self.open_transfer_window)

        buttons_layout.addWidget(add_student_btn)
        buttons_layout.addWidget(remove_student_btn)
        buttons_layout.addWidget(transfer_students_btn)
        layout.addLayout(buttons_layout)
        
        group_box.setLayout(layout)
        return group_box
    
    def load_groups(self):
        """تحميل قائمة المجموعات"""
        try:
            # الحصول على المجموعات من قاعدة البيانات
            query = "SELECT DISTINCT group_name FROM students WHERE group_name IS NOT NULL AND group_name != ''"
            result = self.db_manager.execute_query_with_fetch(query, fetch=True)
            
            self.groups_list.clear()
            
            if result:
                for row in result:
                    group_name = row[0]
                    if group_name and group_name != "لا توجد مجموعة":
                        self.groups_list.addItem(group_name)
            
            # إضافة المجموعات الافتراضية إذا لم تكن موجودة
            default_groups = ["مجموعة أ", "مجموعة ب", "مجموعة ج"]
            existing_groups = [self.groups_list.item(i).text() for i in range(self.groups_list.count())]
            
            for group in default_groups:
                if group not in existing_groups:
                    self.groups_list.addItem(group)
                    
        except Exception as e:
            show_error_message(self, "خطأ", f"فشل في تحميل المجموعات: {str(e)}")
    
    def on_group_selected(self, item):
        """عند تحديد مجموعة"""
        group_name = item.text()
        self.selected_group_label.setText(f"المجموعة المحددة: {group_name}")
        self.load_group_students(group_name)
    
    def load_group_students(self, group_name):
        """تحميل طلاب المجموعة"""
        try:
            query = """
            SELECT student_code, full_name, stage, grade, phone, parent_phone
            FROM students
            WHERE group_name = ?
            """
            result = self.db_manager.execute_query_with_fetch(query, (group_name,), fetch=True)
            
            self.students_table.setRowCount(0)
            
            if result:
                self.students_table.setRowCount(len(result))
                for row_idx, row_data in enumerate(result):
                    for col_idx, data in enumerate(row_data):
                        item = QTableWidgetItem(str(data) if data else "")
                        self.students_table.setItem(row_idx, col_idx, item)
                        
        except Exception as e:
            show_error_message(self, "خطأ", f"فشل في تحميل طلاب المجموعة: {str(e)}")
    
    def add_group(self):
        """إضافة مجموعة جديدة"""
        group_name = self.group_name_input.text().strip()
        
        if not group_name:
            show_warning_message(self, "تحذير", "يرجى إدخال اسم المجموعة")
            return
        
        # التحقق من عدم وجود المجموعة
        existing_groups = [self.groups_list.item(i).text() for i in range(self.groups_list.count())]
        if group_name in existing_groups:
            show_warning_message(self, "تحذير", "هذه المجموعة موجودة بالفعل")
            return
        
        # إضافة المجموعة للقائمة
        self.groups_list.addItem(group_name)
        self.group_name_input.clear()
        
        show_success_message(self, "نجح", f"تم إضافة المجموعة '{group_name}' بنجاح")
    
    def edit_group(self):
        """تعديل اسم المجموعة"""
        current_item = self.groups_list.currentItem()
        if not current_item:
            show_warning_message(self, "تحذير", "يرجى تحديد مجموعة للتعديل")
            return
        
        old_name = current_item.text()
        new_name = self.group_name_input.text().strip()
        
        if not new_name:
            show_warning_message(self, "تحذير", "يرجى إدخال الاسم الجديد للمجموعة")
            return
        
        if show_confirmation_dialog(self, "تأكيد", f"هل تريد تغيير اسم المجموعة من '{old_name}' إلى '{new_name}'؟"):
            try:
                # تحديث قاعدة البيانات
                query = "UPDATE students SET group_name = ? WHERE group_name = ?"
                self.db_manager.execute_query(query, (new_name, old_name))
                
                # تحديث القائمة
                current_item.setText(new_name)
                self.group_name_input.clear()
                
                show_success_message(self, "نجح", "تم تعديل اسم المجموعة بنجاح")
                
            except Exception as e:
                show_error_message(self, "خطأ", f"فشل في تعديل المجموعة: {str(e)}")
    
    def delete_group(self):
        """حذف المجموعة"""
        current_item = self.groups_list.currentItem()
        if not current_item:
            show_warning_message(self, "تحذير", "يرجى تحديد مجموعة للحذف")
            return
        
        group_name = current_item.text()
        
        if show_confirmation_dialog(self, "تأكيد الحذف", f"هل تريد حذف المجموعة '{group_name}'؟\nسيتم نقل جميع الطلاب إلى 'لا توجد مجموعة'"):
            try:
                # نقل الطلاب إلى "لا توجد مجموعة"
                query = "UPDATE students SET group_name = 'لا توجد مجموعة' WHERE group_name = ?"
                self.db_manager.execute_query(query, (group_name,))
                
                # حذف المجموعة من القائمة
                row = self.groups_list.row(current_item)
                self.groups_list.takeItem(row)
                
                # مسح جدول الطلاب
                self.students_table.setRowCount(0)
                self.selected_group_label.setText("لم يتم تحديد مجموعة")
                
                show_success_message(self, "نجح", "تم حذف المجموعة بنجاح")
                
            except Exception as e:
                show_error_message(self, "خطأ", f"فشل في حذف المجموعة: {str(e)}")
    
    def add_student_to_group(self):
        """إضافة طالب للمجموعة"""
        current_item = self.groups_list.currentItem()
        if not current_item:
            show_warning_message(self, "تحذير", "يرجى تحديد مجموعة أولاً")
            return
        
        # هنا يمكن إضافة نافذة لاختيار الطالب
        show_warning_message(self, "قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def remove_student_from_group(self):
        """إزالة طالب من المجموعة"""
        current_row = self.students_table.currentRow()
        if current_row < 0:
            show_warning_message(self, "تحذير", "يرجى تحديد طالب للإزالة")
            return
        
        student_code = self.students_table.item(current_row, 0).text()
        student_name = self.students_table.item(current_row, 1).text()
        
        if show_confirmation_dialog(self, "تأكيد", f"هل تريد إزالة الطالب '{student_name}' من المجموعة؟"):
            try:
                query = "UPDATE students SET group_name = 'لا توجد مجموعة' WHERE student_code = ?"
                self.db_manager.execute_query(query, (student_code,))
                
                # إعادة تحميل الطلاب
                current_item = self.groups_list.currentItem()
                if current_item:
                    self.load_group_students(current_item.text())
                
                show_success_message(self, "نجح", "تم إزالة الطالب من المجموعة بنجاح")
                
            except Exception as e:
                show_error_message(self, "خطأ", f"فشل في إزالة الطالب: {str(e)}")

    def open_transfer_window(self):
        """فتح نافذة نقل الطلاب بين المجموعات"""
        try:
            self.transfer_window = GroupTransferWindow(self.db_manager)
            self.transfer_window.show()
        except Exception as e:
            show_error_message(self, "خطأ", f"فشل في فتح نافذة نقل الطلاب: {str(e)}")
