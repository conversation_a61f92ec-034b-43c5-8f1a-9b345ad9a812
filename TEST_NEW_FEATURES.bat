@echo off
chcp 65001 > nul
title اختبار المميزات الجديدة

echo.
echo ===============================================
echo    🧪 اختبار المميزات الجديدة 🧪
echo    نظام إدارة الطلاب المتطور
echo ===============================================
echo.
echo 📝 الاختبارات المطلوبة:
echo    • إزالة اسم نظام إدارة الطلاب المتطور من جميع الواجهات
echo    • إضافة معلومات المطور في الإعدادات
echo    • إضافة إعدادات المواد المتعددة
echo    • تحديث النصوص في جميع الملفات
echo.
echo ===============================================

REM فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo 📥 يرجى تثبيت Python أولاً من: https://python.org
    echo.
    pause
    exit /b 1
)

echo 🔍 فحص Python... ✅
echo.

echo ===============================================
echo              🧪 تشغيل الاختبارات
echo ===============================================
echo.

echo 🚀 تشغيل اختبار المميزات الجديدة...
python test_new_features.py

echo.
echo ===============================================
echo              📊 نتائج الاختبارات
echo ===============================================
echo.

if errorlevel 1 (
    echo ❌ فشل في بعض الاختبارات
    echo 🔧 يرجى مراجعة الأخطاء أعلاه وإصلاحها
) else (
    echo ✅ نجحت جميع الاختبارات!
    echo 🎉 المميزات الجديدة تعمل بشكل صحيح
)

echo.
echo ===============================================
echo                تم الانتهاء
echo ===============================================
echo.
echo 📞 للدعم الفني:
echo    • راجع ملف ENHANCED_USER_GUIDE.md
echo    • تواصل مع المطور: 01225396729
echo    • صفحة فيسبوك: H - TECH
echo.
pause
