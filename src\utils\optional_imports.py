# -*- coding: utf-8 -*-
"""
إدارة الاستيرادات الاختيارية
Optional Imports Management
"""

import importlib
import logging
import sys

# تهيئة السجل
logger = logging.getLogger(__name__)

# قائمة بالوحدات الاختيارية
OPTIONAL_MODULES = {
    'qrcode': False,
    'pyzbar': False,
    'cv2': False,
    'cryptography': False,
    'telegram': False,
    'google.cloud.storage': False,
    'boto3': False,
    'dropbox': False
}

# محاولة استيراد الوحدات
for module_name in OPTIONAL_MODULES:
    try:
        importlib.import_module(module_name)
        OPTIONAL_MODULES[module_name] = True
        logger.info(f"✅ تم استيراد {module_name} بنجاح")
    except ImportError:
        logger.warning(f"⚠️ الوحدة {module_name} غير متوفرة")

# دوال مساعدة للتحقق من توفر الوحدات
def is_qrcode_available():
    """التحقق من توفر وحدة QR Code"""
    return OPTIONAL_MODULES['qrcode']

def is_pyzbar_available():
    """التحقق من توفر وحدة PyZBar"""
    return OPTIONAL_MODULES['pyzbar']

def is_opencv_available():
    """التحقق من توفر وحدة OpenCV"""
    return OPTIONAL_MODULES['cv2']

def is_cryptography_available():
    """التحقق من توفر وحدة Cryptography"""
    return OPTIONAL_MODULES['cryptography']

def is_telegram_available():
    """التحقق من توفر وحدة Telegram"""
    return OPTIONAL_MODULES['telegram']

def is_google_cloud_available():
    """التحقق من توفر وحدة Google Cloud Storage"""
    return OPTIONAL_MODULES['google.cloud.storage']

def is_aws_available():
    """التحقق من توفر وحدة AWS S3"""
    return OPTIONAL_MODULES['boto3']

def is_dropbox_available():
    """التحقق من توفر وحدة Dropbox"""
    return OPTIONAL_MODULES['dropbox']

def is_pil_available():
    """التحقق من توفر وحدة PIL"""
    try:
        importlib.import_module('PIL')
        return True
    except ImportError:
        return False

# دوال للتحقق من توفر أنظمة كاملة
def is_qr_system_available():
    """التحقق من توفر جميع وحدات نظام QR"""
    return all([
        OPTIONAL_MODULES['qrcode'],
        OPTIONAL_MODULES['pyzbar'],
        OPTIONAL_MODULES['cv2'],
        OPTIONAL_MODULES['cryptography']
    ])

def is_cloud_sync_available():
    """التحقق من توفر وحدات المزامنة السحابية"""
    return any([
        OPTIONAL_MODULES['google.cloud.storage'],
        OPTIONAL_MODULES['boto3'],
        OPTIONAL_MODULES['dropbox']
    ])

# استيراد الوحدات بشكل آمن
def safe_import(module_name):
    """
    استيراد وحدة بشكل آمن
    
    Args:
        module_name: اسم الوحدة المراد استيرادها
        
    Returns:
        الوحدة المستوردة أو None إذا كانت غير متوفرة
    """
    if module_name in OPTIONAL_MODULES and OPTIONAL_MODULES[module_name]:
        return importlib.import_module(module_name)
    return None

def get_missing_modules(required_modules):
    """
    الحصول على قائمة بالوحدات المفقودة
    
    Args:
        required_modules: قائمة بأسماء الوحدات المطلوبة
        
    Returns:
        قائمة بأسماء الوحدات المفقودة
    """
    missing = []
    for module in required_modules:
        if module in OPTIONAL_MODULES and not OPTIONAL_MODULES[module]:
            missing.append(module)
    return missing

def print_module_status():
    """طباعة حالة جميع الوحدات الاختيارية"""
    print("\n=== حالة الوحدات الاختيارية ===")
    for module, available in OPTIONAL_MODULES.items():
        status = "✅ متوفر" if available else "❌ غير متوفر"
        print(f"{module}: {status}")
    print("===============================\n")