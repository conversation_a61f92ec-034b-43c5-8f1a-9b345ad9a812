@echo off
chcp 65001 >nul
title نظام QR Code للحضور الذكي - الإصدار المتكامل

cls
color 0F
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                نظام QR Code للحضور الذكي                   ║
echo ║              الإصدار المتكامل v7.0                          ║
echo ║                   نظام إدارة الطلاب المتطور                           ║
echo ║              نظام شامل لإدارة الطلاب                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 المميزات الجديدة في نظام QR Code:
echo.
echo ✅ إنشاء QR Code مشفر لكل طالب
echo ✅ مسح QR Code بالكاميرا مباشرة
echo ✅ تحديد حالة الحضور حسب التوقيت
echo ✅ تنبيهات فورية للمسح خارج التوقيت
echo ✅ إشعارات تلقائية لأولياء الأمور
echo ✅ تقارير يومية آلية
echo ✅ حماية من التلاعب والتزوير
echo ✅ إدارة جداول الحصص
echo.

echo 🔧 تثبيت المتطلبات...
echo.

REM التحقق من وجود Python
py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ Python موجود!
    goto install_requirements
)

python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ Python موجود!
    goto install_requirements
)

python3 --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ Python موجود!
    goto install_requirements
)

echo ❌ Python غير موجود!
echo يرجى تثبيت Python من: https://python.org/downloads
start https://python.org/downloads
goto end

:install_requirements
echo 📦 تثبيت مكتبات QR Code...
echo.

REM تثبيت المتطلبات
py -m pip install --upgrade pip >nul 2>&1
py -m pip install -r requirements_qr.txt

if errorlevel 1 (
    echo ⚠️ تحذير: قد تكون هناك مشاكل في تثبيت بعض المكتبات
    echo سيتم المحاولة بطريقة بديلة...
    
    REM تثبيت المكتبات الأساسية واحدة تلو الأخرى
    py -m pip install PyQt5
    py -m pip install qrcode[pil]
    py -m pip install pyzbar
    py -m pip install cryptography
    py -m pip install Pillow
    py -m pip install opencv-python
)

echo.
echo 🚀 تشغيل نظام QR Code للحضور الذكي...
echo.

py main.py

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    دليل نظام QR Code                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 📱 نظام QR Code الذكي:
echo    🎯 الوصول: شريط التنقل ← "📱 حضور QR Code"
echo.
echo 🔗 إنشاء QR Codes:
echo    1️⃣ اذهب لتبويب "🔗 إنشاء QR Codes"
echo    2️⃣ اختر الطلاب المطلوبين
echo    3️⃣ حدد مدة الصلاحية (افتراضي: 24 ساعة)
echo    4️⃣ اضغط "🔗 إنشاء للمحددين"
echo    5️⃣ سيتم حفظ الصور في مجلد المشروع
echo.
echo 📅 إدارة الحصص:
echo    1️⃣ اذهب لتبويب "📅 إدارة الحصص"
echo    2️⃣ أضف حصة جديدة مع التوقيتات
echo    3️⃣ حدد أيام الأسبوع
echo    4️⃣ اضبط نوافذ الحضور والتأخير
echo.
echo 📱 مسح QR Code:
echo    1️⃣ اذهب لتبويب "📱 مسح QR Code"
echo    2️⃣ اضغط "📷 بدء المسح"
echo    3️⃣ وجه الكاميرا نحو QR Code
echo    4️⃣ سيتم التسجيل تلقائياً
echo.
echo 🚨 التنبيهات:
echo    • مسح خارج توقيت الحصة = تنبيه فوري
echo    • إشعار للمستر عبر النظام
echo    • تسجيل في جدول التنبيهات
echo.
echo 📱 إشعارات أولياء الأمور:
echo    • رسالة تلقائية عند تسجيل الحضور
echo    • تفاصيل الحصة والتوقيت
echo    • إرسال عبر WhatsApp و Telegram
echo.
echo 🔒 الأمان والحماية:
echo    • QR Code مشفر بتوقيع رقمي
echo    • مدة صلاحية محددة
echo    • حماية من التلاعب والنسخ
echo    • تشفير بيانات الطلاب
echo.
echo 📊 التقارير:
echo    • تقرير حضور يومي تلقائي
echo    • تصدير PDF و Excel
echo    • إحصائيات مفصلة
echo    • تحليل أنماط الحضور
echo.
echo 🎯 المتطلبات التقنية:
echo    📷 كاميرا ويب (مدمجة أو خارجية)
echo    💻 Windows 10/11
echo    🐍 Python 3.8+
echo    📱 QR Code scanner على الهاتف (للاختبار)
echo.
echo 🔧 حل المشاكل الشائعة:
echo.
echo ❌ مشكلة الكاميرا:
echo    • تأكد من توصيل الكاميرا
echo    • أغلق التطبيقات الأخرى التي تستخدم الكاميرا
echo    • جرب كاميرا أخرى
echo.
echo ❌ مشكلة مسح QR Code:
echo    • تأكد من وضوح الصورة
echo    • تأكد من الإضاءة الجيدة
echo    • تأكد من صلاحية QR Code
echo.
echo ❌ مشكلة التثبيت:
echo    • شغل Command Prompt كمدير
echo    • pip install --upgrade pip
echo    • pip install opencv-python-headless
echo.
echo 🎓 تم التطوير بواسطة: م/ حسام أسامة
echo    مهندس برمجيات - مطور تطبيقات
echo    مصمم خصيصاً لنظام إدارة الطلاب المتطور
echo.
echo 🏆 أول نظام QR Code ذكي للحضور باللغة العربية!
echo    نظام متكامل مع حماية متقدمة وتنبيهات فورية
echo.
pause
