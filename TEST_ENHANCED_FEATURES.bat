@echo off
chcp 65001 > nul
title اختبار المميزات المحسنة - نظام إدارة الطلاب

echo.
echo ===============================================
echo    🧪 اختبار المميزات المحسنة 🧪
echo    نظام إدارة الطلاب - نظام إدارة الطلاب المتطور
echo ===============================================
echo.
echo 📚 قناة نظام طلاب نظام إدارة الطلاب المتطور – دروس، امتحانات، ومتابعة مستمرة.
echo.
echo ===============================================
echo           اختبار الإصدار 2.0 المحسن
echo ===============================================
echo.

REM فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo 📥 يرجى تثبيت Python أولاً من: https://python.org
    echo.
    pause
    exit /b 1
)

echo 🔍 فحص Python... ✅
echo.

REM فحص وجود ملف الاختبار
if not exist "test_enhanced_features.py" (
    echo ❌ ملف test_enhanced_features.py غير موجود
    echo 📁 يرجى التأكد من وجود جميع ملفات النظام
    echo.
    pause
    exit /b 1
)

echo 🔍 فحص ملف الاختبار... ✅
echo.

echo ===============================================
echo              🚀 بدء الاختبارات
echo ===============================================
echo.

echo 📦 تثبيت المتطلبات الأساسية للاختبار...
pip install PyQt5 reportlab openpyxl Pillow requests --quiet --disable-pip-version-check

echo.
echo 🧪 تشغيل اختبارات المميزات المحسنة...
echo.

REM تشغيل الاختبارات
python test_enhanced_features.py

echo.
echo ===============================================
echo              📊 نتائج الاختبارات
echo ===============================================
echo.

if errorlevel 1 (
    echo ❌ فشل في بعض الاختبارات
    echo 💡 يرجى مراجعة الرسائل أعلاه لمعرفة التفاصيل
    echo.
    echo 🔧 نصائح لحل المشاكل:
    echo    • تأكد من تثبيت جميع المتطلبات
    echo    • تأكد من وجود جميع ملفات النظام
    echo    • تأكد من صحة إعدادات النظام
    echo.
) else (
    echo ✅ تم اجتياز جميع الاختبارات بنجاح!
    echo 🎉 النظام المحسن جاهز للاستخدام
    echo.
    echo 🚀 يمكنك الآن تشغيل النظام باستخدام:
    echo    • RUN_ENHANCED_SYSTEM.bat
    echo    • python enhanced_main.py
    echo.
)

echo ===============================================
echo                تم إنهاء الاختبارات
echo ===============================================
echo.
pause
