@echo off
chcp 65001 >nul
title نظام إدارة الطلاب - واجهة محسنة

cls
color 0B
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                نظام إدارة الطلاب                            ║
echo ║                واجهة محسنة وواضحة                          ║
echo ║                نظام إدارة الطلاب المتطور                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎨 تشغيل النظام بواجهة محسنة...
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود
    goto end
)

echo ✅ Python موجود
python --version

echo.
echo 📦 تثبيت المكتبات...
python -m pip install --quiet PyQt5 qrcode[pil] Pillow python-telegram-bot

echo.
echo 🎯 تشغيل النظام بواجهة محسنة...
echo.

REM تشغيل النظام مع واجهة محسنة
python -c "
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QPalette, QColor

class ImprovedMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle('نظام إدارة الطلاب - نظام إدارة الطلاب المتطور')
        self.setGeometry(100, 100, 1200, 800)
        
        # تعيين خط كبير
        font = QFont('Arial', 16)
        self.setFont(font)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout()
        
        # العنوان الرئيسي
        header_frame = QFrame()
        header_frame.setStyleSheet('''
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 15px;
                padding: 25px;
                margin: 10px;
            }
            QLabel {
                color: white;
                font-weight: bold;
            }
        ''')
        
        header_layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel('🎓 نظام إدارة الطلاب')
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet('font-size: 32px; font-weight: bold; margin-bottom: 10px;')
        header_layout.addWidget(title_label)
        
        # اسم المعلم
        teacher_label = QLabel('نظام إدارة الطلاب المتطور')
        teacher_label.setAlignment(Qt.AlignCenter)
        teacher_label.setStyleSheet('font-size: 24px; margin-bottom: 5px;')
        header_layout.addWidget(teacher_label)
        
        # التخصص
        subject_label = QLabel('نظام شامل لإدارة الطلاب')
        subject_label.setAlignment(Qt.AlignCenter)
        subject_label.setStyleSheet('font-size: 18px; margin-bottom: 15px;')
        header_layout.addWidget(subject_label)
        

        
        header_frame.setLayout(header_layout)
        layout.addWidget(header_frame)
        
        # الأزرار الرئيسية
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet('''
            QFrame {
                background-color: white;
                border-radius: 15px;
                padding: 20px;
                margin: 10px;
            }
        ''')
        
        buttons_layout = QVBoxLayout()
        
        # الصف الأول من الأزرار
        row1_layout = QHBoxLayout()
        
        students_btn = self.create_large_button('👥 إدارة الطلاب', '#e74c3c')
        groups_btn = self.create_large_button('👨‍👩‍👧‍👦 إدارة المجموعات', '#9b59b6')
        payments_btn = self.create_large_button('💰 إدارة المدفوعات', '#f39c12')
        videos_btn = self.create_large_button('🎥 إدارة الفيديوهات', '#e67e22')
        
        row1_layout.addWidget(students_btn)
        row1_layout.addWidget(groups_btn)
        row1_layout.addWidget(payments_btn)
        row1_layout.addWidget(videos_btn)
        
        buttons_layout.addLayout(row1_layout)
        
        # الصف الثاني من الأزرار
        row2_layout = QHBoxLayout()
        
        attendance_btn = self.create_large_button('📋 تسجيل الحضور', '#27ae60')
        grades_btn = self.create_large_button('📊 إدارة الدرجات', '#3498db')
        reports_btn = self.create_large_button('📈 التقارير', '#34495e')
        settings_btn = self.create_large_button('⚙️ الإعدادات', '#95a5a6')
        
        row2_layout.addWidget(attendance_btn)
        row2_layout.addWidget(grades_btn)
        row2_layout.addWidget(reports_btn)
        row2_layout.addWidget(settings_btn)
        
        buttons_layout.addLayout(row2_layout)
        
        buttons_frame.setLayout(buttons_layout)
        layout.addWidget(buttons_frame)
        
        # معلومات النظام
        info_frame = QFrame()
        info_frame.setStyleSheet('''
            QFrame {
                background-color: #ecf0f1;
                border-radius: 10px;
                padding: 15px;
                margin: 10px;
            }
            QLabel {
                color: #2c3e50;
                font-size: 14px;
            }
        ''')
        
        info_layout = QVBoxLayout()
        
        features_label = QLabel(
            '🌟 المميزات: إدارة شاملة للطلاب • حضور ذكي بـ QR Code • فيديوهات محمية مع امتحانات • ربط تليجرام • تقارير احترافية'
        )
        features_label.setAlignment(Qt.AlignCenter)
        features_label.setWordWrap(True)
        info_layout.addWidget(features_label)
        
        developer_label = QLabel('🎓 تم التطوير بواسطة: م/ حسام أسامة - مهندس برمجيات')
        developer_label.setAlignment(Qt.AlignCenter)
        developer_label.setStyleSheet('font-style: italic; margin-top: 10px;')
        info_layout.addWidget(developer_label)
        
        info_frame.setLayout(info_layout)
        layout.addWidget(info_frame)
        
        central_widget.setLayout(layout)
        
    def create_large_button(self, text, color):
        button = QPushButton(text)
        button.setStyleSheet(f'''
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 20px;
                border-radius: 12px;
                font-size: 16px;
                font-weight: bold;
                min-height: 60px;
                margin: 5px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
                transform: scale(1.05);
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.3)};
            }}
        ''')
        button.clicked.connect(lambda: self.show_feature_info(text))
        return button
        
    def darken_color(self, color, factor=0.2):
        # تغميق اللون للتأثيرات
        color_map = {
            '#e74c3c': '#c0392b',
            '#9b59b6': '#8e44ad',
            '#f39c12': '#e67e22',
            '#e67e22': '#d35400',
            '#27ae60': '#229954',
            '#3498db': '#2980b9',
            '#34495e': '#2c3e50',
            '#95a5a6': '#7f8c8d'
        }
        return color_map.get(color, color)
        
    def show_feature_info(self, feature_name):
        from PyQt5.QtWidgets import QMessageBox
        
        info_map = {
            '👥 إدارة الطلاب': 'إدارة شاملة للطلاب مع توليد QR Code تلقائي',
            '👨‍👩‍👧‍👦 إدارة المجموعات': 'تنظيم الطلاب في مجموعات دراسية',
            '💰 إدارة المدفوعات': 'متابعة المدفوعات الشهرية للطلاب',
            '🎥 إدارة الفيديوهات': 'فيديوهات محمية مع امتحانات مربوطة وربط تليجرام',
            '📋 تسجيل الحضور': 'حضور بالاسم والكود مع رسائل تلقائية',
            '📊 إدارة الدرجات': 'إدخال ومتابعة درجات الطلاب',
            '📈 التقارير': 'تقارير PDF وExcel احترافية',
            '⚙️ الإعدادات': 'إعدادات النظام والتخصيص'
        }
        
        info_text = info_map.get(feature_name, 'ميزة قيد التطوير')
        QMessageBox.information(self, feature_name, f'{feature_name}\n\n{info_text}\n\nسيتم تفعيل هذه الميزة قريباً!')

try:
    app = QApplication(sys.argv)
    app.setApplicationName('نظام إدارة الطلاب')
    
    # تعيين خط كبير للتطبيق
    font = QFont('Arial', 14)
    app.setFont(font)
    
    # دعم الشاشات عالية الدقة
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة المحسنة
    window = ImprovedMainWindow()
    window.show()
    
    print('✅ تم تشغيل النظام بواجهة محسنة!')
    sys.exit(app.exec_())
    
except Exception as e:
    print(f'❌ خطأ: {e}')
    import traceback
    traceback.print_exc()
"

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    الواجهة المحسنة                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🎨 مميزات الواجهة الجديدة:
echo    ✅ خط كبير وواضح (حجم 16-32)
echo    ✅ ألوان جذابة ومتدرجة
echo    ✅ أزرار كبيرة وسهلة الضغط
echo    ✅ تأثيرات بصرية جميلة
echo    ✅ تخطيط منظم ومرتب
echo    ✅ دعم الشاشات عالية الدقة
echo.
echo 📝 النص المضاف:
echo    "قناة نظام طلاب نظام إدارة الطلاب المتطور – دروس، امتحانات، ومتابعة مستمرة."
echo.
echo 🎓 تم التطوير بواسطة: م/ حسام أسامة
echo.
pause
