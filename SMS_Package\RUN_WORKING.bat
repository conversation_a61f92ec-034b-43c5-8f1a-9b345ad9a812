@echo off
chcp 65001 >nul
title نظام إدارة الطلاب - نظام إدارة الطلاب المتطور

cls
color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة الطلاب                        ║
echo ║                   نظام إدارة الطلاب المتطور                           ║
echo ║              نظام شامل لإدارة الطلاب                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 تشغيل النظام...
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود
    goto error
)

echo ✅ Python موجود
python --version

REM التحقق من PyQt5
python -c "import PyQt5" >nul 2>&1
if errorlevel 1 (
    echo ❌ PyQt5 غير موجود، جاري التثبيت...
    python -m pip install PyQt5
) else (
    echo ✅ PyQt5 موجود
)

echo.
echo 🎯 تشغيل النظام...
echo.

REM تشغيل من المجلد الحالي
if exist "main.py" (
    echo 📂 تشغيل من المجلد الحالي...
    python main.py
    if not errorlevel 1 goto success
)

REM تشغيل من مجلد SMS_Package
if exist "SMS_Package\main.py" (
    echo 📂 تشغيل من مجلد SMS_Package...
    cd SMS_Package
    python main.py
    if not errorlevel 1 goto success
)

REM البحث في جميع المجلدات
echo 🔍 البحث عن النظام...
for /r . %%i in (main.py) do (
    echo وُجد: %%i
    cd /d "%%~dpi"
    python main.py
    if not errorlevel 1 goto success
)

goto error

:success
echo.
echo ✅ تم تشغيل النظام بنجاح!
goto end

:error
echo.
echo ❌ فشل في تشغيل النظام
echo.
echo 🔧 جرب هذه الملفات:
echo - RUN_COMPACT_FINAL.bat
echo - START_EVERYTHING.bat
echo - RUN_COMPLETE.bat
echo.

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      معلومات النظام                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🏠 بعد تسجيل الدخول ستجد:
echo    📊 الصفحة الرئيسية مع الإحصائيات
echo    👥 إدارة الطلاب
echo    📋 تسجيل الحضور
echo    📊 إدارة الدرجات
echo    📈 التقارير
echo    📱 نظام QR Code
echo    ⚙️ الإعدادات
echo.
echo 🎓 تم التطوير بواسطة: م/ حسام أسامة
echo    مهندس برمجيات - مطور تطبيقات
echo.
pause
