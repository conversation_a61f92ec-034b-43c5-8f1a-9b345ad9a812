#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عميل التحكم عن بُعد - للاختبار
Remote Control Client - For Testing
"""

import socket
import json
import time
import sys

class RemoteControlClient:
    """عميل التحكم عن بُعد"""
    
    def __init__(self, host='localhost', port=8080):
        self.host = host
        self.port = port
        self.socket = None
    
    def connect(self):
        """الاتصال بالخادم"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            print(f"✅ تم الاتصال بالخادم {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"❌ فشل في الاتصال: {e}")
            return False
    
    def send_command(self, command):
        """إرسال أمر للخادم"""
        try:
            if not self.socket:
                print("❌ غير متصل بالخادم")
                return None
            
            # إرسال الأمر
            self.socket.send(json.dumps(command).encode('utf-8'))
            
            # استقبال الرد
            response = self.socket.recv(4096).decode('utf-8')
            return json.loads(response)
            
        except Exception as e:
            print(f"❌ خطأ في إرسال الأمر: {e}")
            return None
    
    def get_students(self):
        """الحصول على قائمة الطلاب"""
        command = {
            "type": "get_students",
            "data": {}
        }
        return self.send_command(command)
    
    def add_student(self, name, grade, class_name):
        """إضافة طالب جديد"""
        command = {
            "type": "add_student",
            "data": {
                "name": name,
                "grade": grade,
                "class_name": class_name
            }
        }
        return self.send_command(command)
    
    def mark_attendance(self, student_id, status, date=None):
        """تسجيل الحضور"""
        if not date:
            date = time.strftime("%Y-%m-%d")
        
        command = {
            "type": "mark_attendance",
            "data": {
                "student_id": student_id,
                "status": status,
                "date": date
            }
        }
        return self.send_command(command)
    
    def get_stats(self):
        """الحصول على إحصائيات النظام"""
        command = {
            "type": "get_stats",
            "data": {}
        }
        return self.send_command(command)
    
    def close(self):
        """إغلاق الاتصال"""
        if self.socket:
            self.socket.close()
            print("🔌 تم إغلاق الاتصال")

def main():
    """الدالة الرئيسية للاختبار"""
    print("🌐 عميل التحكم عن بُعد - للاختبار")
    print("=" * 50)
    
    # إنشاء العميل
    client = RemoteControlClient()
    
    # محاولة الاتصال
    if not client.connect():
        print("❌ فشل في الاتصال بالخادم")
        print("💡 تأكد من تشغيل النظام المثالي أولاً")
        return
    
    print("\n🎯 اختبار الأوامر:")
    print("1. الحصول على قائمة الطلاب")
    print("2. إضافة طالب جديد")
    print("3. تسجيل الحضور")
    print("4. الحصول على الإحصائيات")
    print("5. خروج")
    
    while True:
        try:
            choice = input("\nأدخل اختيارك (1-5): ").strip()
            
            if choice == "1":
                print("\n📋 جاري الحصول على قائمة الطلاب...")
                response = client.get_students()
                if response:
                    print("✅ تم استلام البيانات:")
                    print(json.dumps(response, indent=2, ensure_ascii=False))
                else:
                    print("❌ فشل في الحصول على البيانات")
            
            elif choice == "2":
                print("\n➕ إضافة طالب جديد:")
                name = input("اسم الطالب: ").strip()
                grade = input("المرحلة (مثل: أولى ثانوي): ").strip()
                class_name = input("الصف (مثل: أ): ").strip()
                
                if name and grade and class_name:
                    response = client.add_student(name, grade, class_name)
                    if response and response.get('success'):
                        print("✅ تم إضافة الطالب بنجاح")
                    else:
                        print("❌ فشل في إضافة الطالب")
                else:
                    print("❌ يرجى إدخال جميع البيانات")
            
            elif choice == "3":
                print("\n📝 تسجيل الحضور:")
                student_id = input("رقم الطالب: ").strip()
                status = input("الحالة (حاضر/غائب/متأخر): ").strip()
                
                if student_id and status:
                    try:
                        student_id = int(student_id)
                        response = client.mark_attendance(student_id, status)
                        if response and response.get('success'):
                            print("✅ تم تسجيل الحضور بنجاح")
                        else:
                            print("❌ فشل في تسجيل الحضور")
                    except ValueError:
                        print("❌ رقم الطالب يجب أن يكون رقماً")
                else:
                    print("❌ يرجى إدخال جميع البيانات")
            
            elif choice == "4":
                print("\n📊 جاري الحصول على الإحصائيات...")
                response = client.get_stats()
                if response:
                    stats = response.get('stats', {})
                    print("✅ إحصائيات النظام:")
                    print(f"   إجمالي الطلاب: {stats.get('total_students', 0)}")
                    print(f"   الحضور اليوم: {stats.get('present_today', 0)}")
                    print(f"   الغياب اليوم: {stats.get('absent_today', 0)}")
                    print(f"   حالة النظام: {stats.get('system_status', 'غير معروف')}")
                else:
                    print("❌ فشل في الحصول على الإحصائيات")
            
            elif choice == "5":
                print("👋 شكراً لاستخدام عميل التحكم عن بُعد")
                break
            
            else:
                print("❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى")
        
        except KeyboardInterrupt:
            print("\n👋 تم إيقاف العميل")
            break
        except Exception as e:
            print(f"❌ خطأ غير متوقع: {e}")
    
    # إغلاق الاتصال
    client.close()

if __name__ == "__main__":
    main() 