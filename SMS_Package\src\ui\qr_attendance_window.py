# -*- coding: utf-8 -*-
"""
نافذة حضور QR Code الذكي
Smart QR Code Attendance Window
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QComboBox, QMessageBox, QFrame, QGroupBox, QFormLayout,
                            QHeaderView, QAbstractItemView, QSplitter, QDateEdit,
                            QTextEdit, QTabWidget, QTimeEdit, QSpinBox, QCheckBox, QLineEdit)
from PyQt5.QtCore import Qt, QDate, QTime, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap
from datetime import datetime, date, time
import json

from ..utils.qr_generator import QRCodeGenerator
from ..utils.qr_scanner import QRScannerWidget
from ..models.class_schedule import ClassSchedule
from ..models.student import Student
from ..utils.styles import get_form_style, get_table_style, get_arabic_font_style
from ..utils.message_boxes import show_error_message, show_success_message, show_warning_message
from ..utils.messaging import messaging_system

class QRAttendanceWindow(QWidget):
    """نافذة حضور QR Code الذكي"""
    
    attendance_recorded = pyqtSignal(dict)  # إشارة عند تسجيل الحضور
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.student_model = Student(db_manager)
        self.schedule_model = ClassSchedule(db_manager)
        self.qr_generator = QRCodeGenerator()
        
        # متغيرات الحالة
        self.current_class = None
        self.attendance_list = []
        
        self.init_ui()
        self.setup_timer()
        self.load_initial_data()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("حضور QR Code الذكي - نظام إدارة الطلاب")
        self.setGeometry(100, 100, 1400, 900)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # شريط المعلومات العلوي
        info_frame = self.create_info_frame()
        main_layout.addWidget(info_frame)
        
        # التبويبات
        tabs = QTabWidget()
        
        # تبويب المسح
        scan_tab = self.create_scan_tab()
        tabs.addTab(scan_tab, "📱 مسح QR Code")
        
        # تبويب إدارة الحصص
        schedule_tab = self.create_schedule_tab()
        tabs.addTab(schedule_tab, "📅 إدارة الحصص")
        
        # تبويب إنشاء QR Codes
        generate_tab = self.create_generate_tab()
        tabs.addTab(generate_tab, "🔗 إنشاء QR Codes")
        
        # تبويب التقارير
        reports_tab = self.create_reports_tab()
        tabs.addTab(reports_tab, "📊 التقارير")
        
        # تبويب التنبيهات
        alerts_tab = self.create_alerts_tab()
        tabs.addTab(alerts_tab, "🚨 التنبيهات")
        
        main_layout.addWidget(tabs)
        
        self.setLayout(main_layout)
        
        # تطبيق الأنماط
        self.setStyleSheet(get_form_style() + get_arabic_font_style())
    
    def create_info_frame(self):
        """إنشاء إطار المعلومات العلوي"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #3498db, stop:1 #2980b9);
                border-radius: 10px;
                margin: 5px;
                padding: 15px;
            }
            QLabel {
                color: white;
                font-weight: bold;
                background: transparent;
            }
        """)
        
        layout = QHBoxLayout()
        
        # الوقت الحالي
        self.current_time_label = QLabel()
        self.current_time_label.setStyleSheet("font-size: 18px;")
        layout.addWidget(self.current_time_label)
        
        # معلومات الحصة الحالية
        self.current_class_label = QLabel("لا توجد حصة حالية")
        self.current_class_label.setStyleSheet("font-size: 16px;")
        layout.addWidget(self.current_class_label)
        
        # عداد الحضور
        self.attendance_count_label = QLabel("الحضور: 0")
        self.attendance_count_label.setStyleSheet("font-size: 16px;")
        layout.addWidget(self.attendance_count_label)
        
        frame.setLayout(layout)
        return frame
    
    def create_scan_tab(self):
        """إنشاء تبويب المسح"""
        widget = QWidget()
        layout = QHBoxLayout()
        
        # الجانب الأيسر - ماسح QR Code
        self.qr_scanner = QRScannerWidget(self.qr_generator)
        self.qr_scanner.qr_scanned.connect(self.on_qr_scanned)
        layout.addWidget(self.qr_scanner, 1)
        
        # الجانب الأيمن - قائمة الحضور
        attendance_frame = self.create_attendance_frame()
        layout.addWidget(attendance_frame, 1)
        
        widget.setLayout(layout)
        return widget
    
    def create_attendance_frame(self):
        """إنشاء إطار قائمة الحضور"""
        group_box = QGroupBox("📋 قائمة الحضور")
        layout = QVBoxLayout()
        
        # جدول الحضور
        self.attendance_table = QTableWidget()
        self.attendance_table.setColumnCount(6)
        self.attendance_table.setHorizontalHeaderLabels([
            "الكود", "الاسم", "الحالة", "وقت المسح", "الحصة", "ملاحظات"
        ])
        
        # تنسيق الجدول
        header = self.attendance_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.attendance_table.setAlternatingRowColors(True)
        self.attendance_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        
        layout.addWidget(self.attendance_table)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.refresh_attendance)
        buttons_layout.addWidget(refresh_btn)
        
        export_btn = QPushButton("📤 تصدير")
        export_btn.clicked.connect(self.export_attendance)
        buttons_layout.addWidget(export_btn)
        
        clear_btn = QPushButton("🗑️ مسح")
        clear_btn.clicked.connect(self.clear_attendance)
        buttons_layout.addWidget(clear_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        group_box.setLayout(layout)
        return group_box
    
    def create_schedule_tab(self):
        """إنشاء تبويب إدارة الحصص"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # نموذج إضافة حصة
        form_group = QGroupBox("➕ إضافة حصة جديدة")
        form_layout = QFormLayout()
        
        self.class_name_input = QLineEdit()
        self.class_name_input.setPlaceholderText("مثال: حصة الجغرافيا - الصف الأول")
        form_layout.addRow("اسم الحصة:", self.class_name_input)
        
        self.subject_combo = QComboBox()
        self.subject_combo.addItems(["جغرافيا", "تاريخ"])
        form_layout.addRow("المادة:", self.subject_combo)
        
        self.start_time_input = QTimeEdit()
        self.start_time_input.setTime(QTime(8, 0))
        form_layout.addRow("وقت البداية:", self.start_time_input)
        
        self.end_time_input = QTimeEdit()
        self.end_time_input.setTime(QTime(9, 0))
        form_layout.addRow("وقت النهاية:", self.end_time_input)
        
        # أيام الأسبوع
        days_layout = QHBoxLayout()
        self.days_checkboxes = {}
        days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
        day_names = ["الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت", "الأحد"]
        
        for day, day_name in zip(days, day_names):
            checkbox = QCheckBox(day_name)
            self.days_checkboxes[day] = checkbox
            days_layout.addWidget(checkbox)
        
        form_layout.addRow("أيام الأسبوع:", days_layout)
        
        self.group_name_input = QLineEdit()
        self.group_name_input.setPlaceholderText("اختياري - اسم المجموعة")
        form_layout.addRow("المجموعة:", self.group_name_input)
        
        # إعدادات التوقيت
        timing_layout = QHBoxLayout()
        
        self.before_minutes = QSpinBox()
        self.before_minutes.setRange(0, 60)
        self.before_minutes.setValue(15)
        self.before_minutes.setSuffix(" دقيقة")
        timing_layout.addWidget(QLabel("السماح قبل:"))
        timing_layout.addWidget(self.before_minutes)
        
        self.after_minutes = QSpinBox()
        self.after_minutes.setRange(0, 120)
        self.after_minutes.setValue(30)
        self.after_minutes.setSuffix(" دقيقة")
        timing_layout.addWidget(QLabel("السماح بعد:"))
        timing_layout.addWidget(self.after_minutes)
        
        self.late_threshold = QSpinBox()
        self.late_threshold.setRange(0, 60)
        self.late_threshold.setValue(10)
        self.late_threshold.setSuffix(" دقيقة")
        timing_layout.addWidget(QLabel("حد التأخير:"))
        timing_layout.addWidget(self.late_threshold)
        
        form_layout.addRow("إعدادات التوقيت:", timing_layout)
        
        # زر الإضافة
        add_class_btn = QPushButton("➕ إضافة الحصة")
        add_class_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_class_btn.clicked.connect(self.add_class_session)
        form_layout.addRow(add_class_btn)
        
        form_group.setLayout(form_layout)
        layout.addWidget(form_group)
        
        # جدول الحصص الموجودة
        classes_group = QGroupBox("📅 الحصص المجدولة")
        classes_layout = QVBoxLayout()
        
        self.classes_table = QTableWidget()
        self.classes_table.setColumnCount(7)
        self.classes_table.setHorizontalHeaderLabels([
            "اسم الحصة", "المادة", "وقت البداية", "وقت النهاية", 
            "الأيام", "المجموعة", "إجراءات"
        ])
        
        header = self.classes_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        classes_layout.addWidget(self.classes_table)
        classes_group.setLayout(classes_layout)
        layout.addWidget(classes_group)
        
        widget.setLayout(layout)
        return widget
    
    def create_generate_tab(self):
        """إنشاء تبويب إنشاء QR Codes"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات الإنشاء
        settings_group = QGroupBox("⚙️ إعدادات QR Code")
        settings_layout = QFormLayout()
        
        self.validity_hours = QSpinBox()
        self.validity_hours.setRange(1, 168)  # من ساعة إلى أسبوع
        self.validity_hours.setValue(24)
        self.validity_hours.setSuffix(" ساعة")
        settings_layout.addRow("مدة الصلاحية:", self.validity_hours)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # اختيار الطلاب
        students_group = QGroupBox("👥 اختيار الطلاب")
        students_layout = QVBoxLayout()
        
        # أزرار الاختيار
        selection_layout = QHBoxLayout()
        
        select_all_btn = QPushButton("✅ اختيار الكل")
        select_all_btn.clicked.connect(self.select_all_students)
        selection_layout.addWidget(select_all_btn)
        
        deselect_all_btn = QPushButton("❌ إلغاء الكل")
        deselect_all_btn.clicked.connect(self.deselect_all_students)
        selection_layout.addWidget(deselect_all_btn)
        
        generate_selected_btn = QPushButton("🔗 إنشاء للمحددين")
        generate_selected_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        generate_selected_btn.clicked.connect(self.generate_selected_qr_codes)
        selection_layout.addWidget(generate_selected_btn)
        
        selection_layout.addStretch()
        students_layout.addLayout(selection_layout)
        
        # جدول الطلاب
        self.students_table = QTableWidget()
        self.students_table.setColumnCount(5)
        self.students_table.setHorizontalHeaderLabels([
            "اختيار", "الكود", "الاسم", "المجموعة", "QR Code"
        ])
        
        header = self.students_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        students_layout.addWidget(self.students_table)
        students_group.setLayout(students_layout)
        layout.addWidget(students_group)
        
        widget.setLayout(layout)
        return widget
    
    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # فلاتر التقرير
        filters_group = QGroupBox("🔍 فلاتر التقرير")
        filters_layout = QFormLayout()
        
        self.report_date = QDateEdit()
        self.report_date.setDate(QDate.currentDate())
        self.report_date.setCalendarPopup(True)
        filters_layout.addRow("التاريخ:", self.report_date)
        
        self.report_class_combo = QComboBox()
        self.report_class_combo.addItem("جميع الحصص")
        filters_layout.addRow("الحصة:", self.report_class_combo)
        
        generate_report_btn = QPushButton("📊 إنشاء التقرير")
        generate_report_btn.clicked.connect(self.generate_attendance_report)
        filters_layout.addRow(generate_report_btn)
        
        filters_group.setLayout(filters_layout)
        layout.addWidget(filters_group)
        
        # جدول التقرير
        report_group = QGroupBox("📋 تقرير الحضور")
        report_layout = QVBoxLayout()
        
        self.report_table = QTableWidget()
        self.report_table.setColumnCount(6)
        self.report_table.setHorizontalHeaderLabels([
            "الكود", "الاسم", "الحالة", "وقت المسح", "الحصة", "المادة"
        ])
        
        header = self.report_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        report_layout.addWidget(self.report_table)
        
        # أزرار التصدير
        export_layout = QHBoxLayout()
        
        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.clicked.connect(self.export_report_pdf)
        export_layout.addWidget(export_pdf_btn)
        
        export_excel_btn = QPushButton("📊 تصدير Excel")
        export_excel_btn.clicked.connect(self.export_report_excel)
        export_layout.addWidget(export_excel_btn)
        
        export_layout.addStretch()
        report_layout.addLayout(export_layout)
        
        report_group.setLayout(report_layout)
        layout.addWidget(report_group)
        
        widget.setLayout(layout)
        return widget
    
    def create_alerts_tab(self):
        """إنشاء تبويب التنبيهات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # جدول التنبيهات
        alerts_group = QGroupBox("🚨 تنبيهات المسح خارج التوقيت")
        alerts_layout = QVBoxLayout()
        
        self.alerts_table = QTableWidget()
        self.alerts_table.setColumnCount(6)
        self.alerts_table.setHorizontalHeaderLabels([
            "الطالب", "الكود", "وقت المسح", "نوع التنبيه", "الحالة", "إجراءات"
        ])
        
        header = self.alerts_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        alerts_layout.addWidget(self.alerts_table)
        
        # أزرار إدارة التنبيهات
        alerts_buttons_layout = QHBoxLayout()
        
        refresh_alerts_btn = QPushButton("🔄 تحديث التنبيهات")
        refresh_alerts_btn.clicked.connect(self.load_alerts)
        alerts_buttons_layout.addWidget(refresh_alerts_btn)
        
        mark_all_handled_btn = QPushButton("✅ تمييز الكل كمعالج")
        mark_all_handled_btn.clicked.connect(self.mark_all_alerts_handled)
        alerts_buttons_layout.addWidget(mark_all_handled_btn)
        
        alerts_buttons_layout.addStretch()
        alerts_layout.addLayout(alerts_buttons_layout)
        
        alerts_group.setLayout(alerts_layout)
        layout.addWidget(alerts_group)
        
        widget.setLayout(layout)
        return widget

    def setup_timer(self):
        """إعداد مؤقت تحديث الوقت والحصة"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_current_info)
        self.timer.start(1000)  # تحديث كل ثانية

    def update_current_info(self):
        """تحديث معلومات الوقت والحصة الحالية"""
        try:
            current_time = datetime.now()
            time_str = current_time.strftime("%Y-%m-%d %H:%M:%S")
            self.current_time_label.setText(f"الوقت الحالي: {time_str}")

            # تحديث معلومات الحصة الحالية
            current_class = self.schedule_model.get_current_class(current_time)

            if current_class:
                class_info = f"الحصة: {current_class['class_name']} ({current_class['subject']})"
                self.current_class_label.setText(class_info)
                self.current_class = current_class
            else:
                self.current_class_label.setText("لا توجد حصة حالية")
                self.current_class = None

            # تحديث عداد الحضور
            attendance_count = len(self.attendance_list)
            self.attendance_count_label.setText(f"الحضور: {attendance_count}")

        except Exception as e:
            print(f"خطأ في تحديث المعلومات: {e}")

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        self.load_students_for_qr()
        self.load_alerts()

    def on_qr_scanned(self, qr_data):
        """معالجة مسح QR Code"""
        try:
            # معالجة المسح عبر نموذج الجدولة
            result = self.schedule_model.process_qr_scan(qr_data)

            if result['success']:
                # تسجيل ناجح
                self.add_attendance_record(qr_data, result)

                # إرسال رسالة لولي الأمر
                self.send_parent_notification(qr_data, result)

                # إشعار نجاح
                show_success_message(
                    self,
                    "تم التسجيل",
                    f"تم تسجيل {result['status']} للطالب {qr_data['student_name']}"
                )

                # إرسال إشارة
                self.attendance_recorded.emit(result)

            else:
                # فشل في التسجيل أو مسح خارج التوقيت
                if result.get('alert_created'):
                    show_warning_message(
                        self,
                        "مسح خارج التوقيت",
                        f"تم مسح QR Code للطالب {qr_data['student_name']} خارج توقيت الحصة"
                    )
                    # تحديث التنبيهات
                    self.load_alerts()
                else:
                    show_error_message(self, "خطأ", result['message'])

        except Exception as e:
            show_error_message(self, "خطأ", f"خطأ في معالجة QR Code: {str(e)}")

    def add_attendance_record(self, student_data, result):
        """إضافة سجل حضور للجدول"""
        try:
            row_position = self.attendance_table.rowCount()
            self.attendance_table.insertRow(row_position)

            # البيانات
            scan_time = datetime.now().strftime("%H:%M:%S")
            class_name = result.get('class_info', {}).get('class_name', 'غير محدد')

            # إضافة البيانات للجدول
            self.attendance_table.setItem(row_position, 0, QTableWidgetItem(student_data['student_code']))
            self.attendance_table.setItem(row_position, 1, QTableWidgetItem(student_data['student_name']))
            self.attendance_table.setItem(row_position, 2, QTableWidgetItem(result['status']))
            self.attendance_table.setItem(row_position, 3, QTableWidgetItem(scan_time))
            self.attendance_table.setItem(row_position, 4, QTableWidgetItem(class_name))
            self.attendance_table.setItem(row_position, 5, QTableWidgetItem(""))

            # إضافة للقائمة المحلية
            self.attendance_list.append({
                'student_data': student_data,
                'result': result,
                'scan_time': scan_time
            })

        except Exception as e:
            print(f"خطأ في إضافة سجل الحضور: {e}")

    def send_parent_notification(self, student_data, result):
        """إرسال إشعار لولي الأمر"""
        try:
            # البحث عن رقم ولي الأمر
            student_id = student_data['student_id']
            student_info = self.student_model.get_student_by_id(student_id)

            if student_info and student_info.get('parent_phone'):
                # إنشاء الرسالة
                class_info = result.get('class_info', {})
                class_name = class_info.get('class_name', 'الحصة')
                subject = class_info.get('subject', '')
                status = result['status']
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M")

                # إرسال الرسالة
                messaging_system.send_attendance_notification(
                    student_data['student_name'],
                    student_info['parent_phone'],
                    status,
                    class_name,
                    current_time
                )

        except Exception as e:
            print(f"خطأ في إرسال إشعار ولي الأمر: {e}")

    def add_class_session(self):
        """إضافة حصة جديدة"""
        try:
            # جمع البيانات
            class_name = self.class_name_input.text().strip()
            if not class_name:
                show_warning_message(self, "تحذير", "يرجى إدخال اسم الحصة")
                return

            subject = self.subject_combo.currentText()
            start_time = self.start_time_input.time().toString("HH:mm:ss")
            end_time = self.end_time_input.time().toString("HH:mm:ss")
            group_name = self.group_name_input.text().strip()

            # أيام الأسبوع المحددة
            selected_days = []
            for day, checkbox in self.days_checkboxes.items():
                if checkbox.isChecked():
                    selected_days.append(day)

            if not selected_days:
                show_warning_message(self, "تحذير", "يرجى اختيار يوم واحد على الأقل")
                return

            # بيانات الحصة
            class_data = {
                'class_name': class_name,
                'subject': subject,
                'start_time': start_time,
                'end_time': end_time,
                'days_of_week': selected_days,
                'group_name': group_name,
                'attendance_window_before': self.before_minutes.value(),
                'attendance_window_after': self.after_minutes.value(),
                'late_threshold': self.late_threshold.value()
            }

            # إضافة الحصة
            class_id = self.schedule_model.add_class_session(class_data)

            if class_id:
                show_success_message(self, "نجح", "تم إضافة الحصة بنجاح")
                self.clear_class_form()
            else:
                show_error_message(self, "خطأ", "فشل في إضافة الحصة")

        except Exception as e:
            show_error_message(self, "خطأ", f"خطأ في إضافة الحصة: {str(e)}")

    def clear_class_form(self):
        """مسح نموذج الحصة"""
        self.class_name_input.clear()
        self.group_name_input.clear()
        for checkbox in self.days_checkboxes.values():
            checkbox.setChecked(False)

    def load_students_for_qr(self):
        """تحميل الطلاب لإنشاء QR Codes"""
        try:
            students = self.student_model.get_all_students()

            self.students_table.setRowCount(len(students))

            for row, student in enumerate(students):
                # checkbox للاختيار
                checkbox = QCheckBox()
                self.students_table.setCellWidget(row, 0, checkbox)

                # بيانات الطالب
                self.students_table.setItem(row, 1, QTableWidgetItem(student['student_code']))
                self.students_table.setItem(row, 2, QTableWidgetItem(student['full_name']))
                self.students_table.setItem(row, 3, QTableWidgetItem(student.get('group_name', '')))

                # زر إنشاء QR Code
                generate_btn = QPushButton("🔗 إنشاء")
                generate_btn.clicked.connect(lambda checked, s=student: self.generate_single_qr(s))
                self.students_table.setCellWidget(row, 4, generate_btn)

        except Exception as e:
            show_error_message(self, "خطأ", f"فشل في تحميل الطلاب: {str(e)}")

    def generate_single_qr(self, student):
        """إنشاء QR Code لطالب واحد"""
        try:
            validity_hours = self.validity_hours.value()
            qr_image, qr_string = self.qr_generator.generate_student_qr(student, validity_hours)

            if qr_image:
                # حفظ الصورة
                filename = f"qr_{student['student_code']}.png"
                if self.qr_generator.save_qr_image(qr_image, filename):
                    show_success_message(
                        self,
                        "نجح",
                        f"تم إنشاء QR Code للطالب {student['full_name']}\nتم الحفظ في: {filename}"
                    )
                else:
                    show_error_message(self, "خطأ", "فشل في حفظ QR Code")
            else:
                show_error_message(self, "خطأ", "فشل في إنشاء QR Code")

        except Exception as e:
            show_error_message(self, "خطأ", f"خطأ في إنشاء QR Code: {str(e)}")

    def select_all_students(self):
        """اختيار جميع الطلاب"""
        for row in range(self.students_table.rowCount()):
            checkbox = self.students_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)

    def deselect_all_students(self):
        """إلغاء اختيار جميع الطلاب"""
        for row in range(self.students_table.rowCount()):
            checkbox = self.students_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)

    def generate_selected_qr_codes(self):
        """إنشاء QR Codes للطلاب المحددين"""
        try:
            selected_students = []

            # جمع الطلاب المحددين
            for row in range(self.students_table.rowCount()):
                checkbox = self.students_table.cellWidget(row, 0)
                if checkbox and checkbox.isChecked():
                    student_code = self.students_table.item(row, 1).text()

                    # البحث عن بيانات الطالب الكاملة
                    students = self.student_model.get_all_students()
                    for student in students:
                        if student['student_code'] == student_code:
                            selected_students.append(student)
                            break

            if not selected_students:
                show_warning_message(self, "تحذير", "يرجى اختيار طالب واحد على الأقل")
                return

            # إنشاء QR Codes
            validity_hours = self.validity_hours.value()
            generated_codes = []

            for student in selected_students:
                qr_image, qr_string = self.qr_generator.generate_student_qr(student, validity_hours)
                if qr_image:
                    filename = f"qr_{student['student_code']}.png"
                    if self.qr_generator.save_qr_image(qr_image, filename):
                        generated_codes.append(filename)

            if generated_codes:
                show_success_message(
                    self,
                    "نجح",
                    f"تم إنشاء {len(generated_codes)} QR Code بنجاح"
                )
            else:
                show_error_message(self, "خطأ", "فشل في إنشاء QR Codes")

        except Exception as e:
            show_error_message(self, "خطأ", f"خطأ في إنشاء QR Codes: {str(e)}")

    def load_alerts(self):
        """تحميل التنبيهات"""
        try:
            alerts = self.schedule_model.get_unhandled_alerts()

            self.alerts_table.setRowCount(len(alerts))

            for row, alert in enumerate(alerts):
                self.alerts_table.setItem(row, 0, QTableWidgetItem(alert['student_name']))
                self.alerts_table.setItem(row, 1, QTableWidgetItem(alert['student_code']))

                scan_time = datetime.fromisoformat(alert['scan_time']).strftime("%Y-%m-%d %H:%M:%S")
                self.alerts_table.setItem(row, 2, QTableWidgetItem(scan_time))

                alert_type_map = {
                    'قبل_الحصة': 'مسح قبل الحصة',
                    'بعد_الحصة': 'مسح بعد الحصة',
                    'لا_توجد_حصة': 'لا توجد حصة'
                }
                alert_type = alert_type_map.get(alert['alert_type'], alert['alert_type'])
                self.alerts_table.setItem(row, 3, QTableWidgetItem(alert_type))

                self.alerts_table.setItem(row, 4, QTableWidgetItem("غير معالج"))

                # زر معالجة
                handle_btn = QPushButton("✅ معالج")
                handle_btn.clicked.connect(lambda checked, alert_id=alert['id']: self.handle_alert(alert_id))
                self.alerts_table.setCellWidget(row, 5, handle_btn)

        except Exception as e:
            show_error_message(self, "خطأ", f"فشل في تحميل التنبيهات: {str(e)}")

    def handle_alert(self, alert_id):
        """معالجة تنبيه"""
        try:
            if self.schedule_model.mark_alert_handled(alert_id):
                show_success_message(self, "نجح", "تم تمييز التنبيه كمعالج")
                self.load_alerts()
            else:
                show_error_message(self, "خطأ", "فشل في معالجة التنبيه")
        except Exception as e:
            show_error_message(self, "خطأ", f"خطأ في معالجة التنبيه: {str(e)}")

    def mark_all_alerts_handled(self):
        """تمييز جميع التنبيهات كمعالجة"""
        try:
            alerts = self.schedule_model.get_unhandled_alerts()
            handled_count = 0

            for alert in alerts:
                if self.schedule_model.mark_alert_handled(alert['id']):
                    handled_count += 1

            if handled_count > 0:
                show_success_message(self, "نجح", f"تم معالجة {handled_count} تنبيه")
                self.load_alerts()
            else:
                show_warning_message(self, "تحذير", "لا توجد تنبيهات للمعالجة")

        except Exception as e:
            show_error_message(self, "خطأ", f"خطأ في معالجة التنبيهات: {str(e)}")

    def refresh_attendance(self):
        """تحديث قائمة الحضور"""
        pass

    def export_attendance(self):
        """تصدير قائمة الحضور"""
        show_success_message(self, "تصدير", "سيتم تطوير ميزة التصدير قريباً")

    def clear_attendance(self):
        """مسح قائمة الحضور"""
        self.attendance_table.setRowCount(0)
        self.attendance_list.clear()

    def generate_attendance_report(self):
        """إنشاء تقرير الحضور"""
        show_success_message(self, "تقرير", "سيتم تطوير ميزة التقارير قريباً")

    def export_report_pdf(self):
        """تصدير التقرير PDF"""
        show_success_message(self, "تصدير", "سيتم تطوير ميزة تصدير PDF قريباً")

    def export_report_excel(self):
        """تصدير التقرير Excel"""
        show_success_message(self, "تصدير", "سيتم تطوير ميزة تصدير Excel قريباً")
