Set WshShell = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' الحصول على مسار المجلد الحالي
currentDir = fso.GetParentFolderName(WScript.ScriptFullName)

' تغيير المجلد الحالي
WshShell.CurrentDirectory = currentDir

' تشغيل البرنامج بدون إظهار نافذة
On Error Resume Next

' محاولة التشغيل بطرق مختلفة
WshShell.Run "pythonw main.py", 0, False
If Err.Number = 0 Then WScript.Quit

Err.Clear
WshShell.Run "py main.py", 0, False  
If Err.Number = 0 Then WScript.Quit

Err.Clear
WshShell.Run "python main.py", 0, False
If Err.Number = 0 Then WScript.Quit

' إذا فشل التشغيل
MsgBox "خطأ: فشل في تشغيل نظام إدارة الطلاب" & vbCrLf & vbCrLf & "تأكد من تثبيت Python" & vbCrLf & vbCrLf & "للدعم الفني: 01225396729", vbCritical, "خطأ في التشغيل"
