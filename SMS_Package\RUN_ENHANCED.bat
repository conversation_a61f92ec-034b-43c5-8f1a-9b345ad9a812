@echo off
chcp 65001 >nul
title نظام إدارة الطلاب - الإصدار المحسن النهائي

cls
color 0F
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة الطلاب                        ║
echo ║                الإصدار المحسن النهائي v3.0                  ║
echo ║                   نظام إدارة الطلاب المتطور                           ║
echo ║              نظام شامل لإدارة الطلاب                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎉 التحسينات الجديدة في هذا الإصدار:
echo.
echo ✅ نافذة تسجيل دخول أكبر ووضوح كامل
echo ✅ زر "طالب جديد" في إدارة الطلاب
echo ✅ حقول جديدة: رقم الهاتف ورقم ولي الأمر
echo ✅ رسائل حوار بخلفية بيضاء ونص أسود
echo ✅ تحسين قاعدة البيانات لدعم الحقول الجديدة
echo ✅ واجهة أكثر وضوحاً وسهولة في الاستخدام
echo.

echo 🚀 بدء تشغيل النظام المحسن...
echo.

REM محاولة تشغيل البرنامج بطرق مختلفة

REM الطريقة الأولى: py launcher
py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python!
    py --version
    echo.
    echo 🎯 تشغيل نظام إدارة الطلاب المحسن النهائي...
    echo.
    py main.py
    goto end
)

REM الطريقة الثانية: python command
python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python!
    python --version
    echo.
    echo 🎯 تشغيل نظام إدارة الطلاب المحسن النهائي...
    echo.
    python main.py
    goto end
)

REM الطريقة الثالثة: python3 command
python3 --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python3!
    python3 --version
    echo.
    echo 🎯 تشغيل نظام إدارة الطلاب المحسن النهائي...
    echo.
    python3 main.py
    goto end
)

echo ❌ لم يتم العثور على Python!
echo.
echo 💡 الحلول المتاحة:
echo.
echo 1️⃣ تثبيت Python من Microsoft Store:
echo    - افتح Microsoft Store
echo    - ابحث عن "Python"
echo    - ثبت Python 3.9 أو أحدث
echo.
echo 2️⃣ تثبيت Python من الموقع الرسمي:
echo    - اذهب إلى: https://python.org/downloads
echo    - حمل وثبت Python
echo    - تأكد من تحديد "Add Python to PATH"
echo.
echo 3️⃣ بناء ملف EXE:
echo    - شغل: build_simple.bat
echo    - احصل على ملف EXE يعمل بدون Python
echo.

echo 🌐 فتح صفحة تحميل Python...
start https://python.org/downloads

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      دليل الاستخدام                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 📝 كيفية إضافة طالب جديد:
echo    1. اذهب إلى "إدارة الطلاب"
echo    2. اضغط على زر "+ طالب جديد"
echo    3. املأ البيانات المطلوبة:
echo       • الاسم
echo       • المرحلة الدراسية
echo       • الصف الدراسي
echo       • رقم الهاتف
echo       • رقم ولي الأمر
echo    4. اضغط "إضافة طالب"
echo.
echo 📚 المميزات المتاحة:
echo    ✅ إدارة الطلاب مع الحقول الجديدة
echo    ✅ تسجيل الحضور السريع
echo    ✅ إدارة درجات الجغرافيا والتاريخ
echo    ✅ تقارير احترافية PDF/Excel
echo    ✅ نظام نسخ احتياطي
echo    ✅ واجهة عربية محسنة
echo    ✅ رسائل واضحة بخلفية بيضاء
echo.
echo 🎓 تم التطوير والتحسين خصيصاً لنظام إدارة الطلاب المتطور
echo    نظام شامل لإدارة الطلاب
echo.
echo 📞 للدعم الفني: راجع ملف USER_GUIDE.md
echo.
pause
