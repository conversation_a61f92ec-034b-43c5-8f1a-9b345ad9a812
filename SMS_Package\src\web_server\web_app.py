# -*- coding: utf-8 -*-
"""
تطبيق الويب الرئيسي
Main Web Application
"""

from flask import Flask, render_template, request, jsonify, session, redirect, url_for, flash
from flask_socketio import SocketIO, emit
from werkzeug.security import check_password_hash, generate_password_hash
import os
import json
from datetime import datetime, timedelta
import threading
import logging

# استيراد النماذج
try:
    from ..database.database_manager import DatabaseManager
    from ..models.student import Student
    from ..models.attendance import Attendance
    from ..models.grades import Grades
    from ..utils.auth import AuthManager
except ImportError:
    # استيراد مطلق كبديل
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from database.database_manager import DatabaseManager
    from models.student import Student
    from models.attendance import Attendance
    from models.grades import Grades
    from utils.auth import AuthManager

class WebApp:
    """تطبيق الويب الرئيسي"""
    
    def __init__(self, db_manager: DatabaseManager, port=5000):
        self.db_manager = db_manager
        self.port = port
        self.app = Flask(__name__, 
                        template_folder='templates',
                        static_folder='static')
        
        # إعداد المفتاح السري
        self.app.secret_key = 'sms_secret_key_2025'
        
        # إعداد SocketIO للتحديثات المباشرة
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # تهيئة النماذج
        self.student_model = Student(db_manager)
        self.attendance_model = Attendance(db_manager)
        self.grades_model = Grades(db_manager)
        self.auth_manager = AuthManager(db_manager)
        
        # إعداد المسارات
        self.setup_routes()
        self.setup_socketio_events()
        
        # إعداد السجلات
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def setup_routes(self):
        """إعداد مسارات التطبيق"""
        
        @self.app.route('/')
        def index():
            """الصفحة الرئيسية"""
            if 'user_id' not in session:
                return redirect(url_for('login'))
            
            # إحصائيات سريعة
            stats = {
                'total_students': len(self.student_model.get_all_students()),
                'present_today': self.attendance_model.get_today_attendance_count('حاضر'),
                'absent_today': self.attendance_model.get_today_attendance_count('غائب'),
                'late_today': self.attendance_model.get_today_attendance_count('متأخر')
            }
            
            return render_template('dashboard.html', stats=stats)
        
        @self.app.route('/login', methods=['GET', 'POST'])
        def login():
            """صفحة تسجيل الدخول"""
            if request.method == 'POST':
                username = request.form['username']
                password = request.form['password']
                
                user = self.auth_manager.authenticate(username, password)
                if user:
                    session['user_id'] = user['id']
                    session['username'] = user['username']
                    session['full_name'] = user['full_name']
                    flash('تم تسجيل الدخول بنجاح', 'success')
                    return redirect(url_for('index'))
                else:
                    flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
            
            return render_template('login.html')
        
        @self.app.route('/logout')
        def logout():
            """تسجيل الخروج"""
            session.clear()
            flash('تم تسجيل الخروج بنجاح', 'info')
            return redirect(url_for('login'))
        
        @self.app.route('/students')
        def students():
            """صفحة إدارة الطلاب"""
            if 'user_id' not in session:
                return redirect(url_for('login'))
            
            students = self.student_model.get_all_students()
            return render_template('students.html', students=students)
        
        @self.app.route('/api/students', methods=['GET', 'POST'])
        def api_students():
            """API إدارة الطلاب"""
            if 'user_id' not in session:
                return jsonify({'error': 'غير مصرح'}), 401
            
            if request.method == 'GET':
                students = self.student_model.get_all_students()
                return jsonify(students)
            
            elif request.method == 'POST':
                data = request.json
                student_id = self.student_model.add_student(
                    data['full_name'],
                    data['gender'],
                    data['stage'],
                    data['grade'],
                    data.get('phone', ''),
                    data.get('guardian_phone', '')
                )
                
                if student_id:
                    # إرسال تحديث مباشر
                    self.socketio.emit('student_added', {
                        'id': student_id,
                        'name': data['full_name']
                    })
                    return jsonify({'success': True, 'id': student_id})
                else:
                    return jsonify({'error': 'فشل في إضافة الطالب'}), 400
        
        @self.app.route('/attendance')
        def attendance():
            """صفحة تسجيل الحضور"""
            if 'user_id' not in session:
                return redirect(url_for('login'))
            
            today = datetime.now().date()
            attendance_records = self.attendance_model.get_attendance_by_date(today)
            return render_template('attendance.html', records=attendance_records, date=today)
        
        @self.app.route('/api/attendance', methods=['POST'])
        def api_mark_attendance():
            """API تسجيل الحضور"""
            if 'user_id' not in session:
                return jsonify({'error': 'غير مصرح'}), 401
            
            data = request.json
            success = self.attendance_model.mark_attendance_by_code(
                data['student_code'],
                datetime.now().date(),
                data['status'],
                data.get('notes', '')
            )
            
            if success:
                # إرسال تحديث مباشر
                self.socketio.emit('attendance_marked', {
                    'student_code': data['student_code'],
                    'status': data['status']
                })
                return jsonify({'success': True})
            else:
                return jsonify({'error': 'فشل في تسجيل الحضور'}), 400
        
        @self.app.route('/grades')
        def grades():
            """صفحة إدارة الدرجات"""
            if 'user_id' not in session:
                return redirect(url_for('login'))
            
            students = self.student_model.get_all_students()
            return render_template('grades.html', students=students)
        
        @self.app.route('/api/grades', methods=['POST'])
        def api_add_grade():
            """API إضافة درجة"""
            if 'user_id' not in session:
                return jsonify({'error': 'غير مصرح'}), 401
            
            data = request.json
            grade_id = self.grades_model.add_grade(
                data['student_id'],
                data['subject'],
                data['exam_type'],
                data['score'],
                data['max_score'],
                datetime.strptime(data['exam_date'], '%Y-%m-%d').date(),
                data.get('notes', '')
            )
            
            if grade_id:
                # إرسال تحديث مباشر
                self.socketio.emit('grade_added', {
                    'student_id': data['student_id'],
                    'subject': data['subject'],
                    'score': data['score']
                })
                return jsonify({'success': True, 'id': grade_id})
            else:
                return jsonify({'error': 'فشل في إضافة الدرجة'}), 400
        
        @self.app.route('/reports')
        def reports():
            """صفحة التقارير"""
            if 'user_id' not in session:
                return redirect(url_for('login'))
            
            return render_template('reports.html')
        
        @self.app.route('/api/reports/<report_type>')
        def api_generate_report(report_type):
            """API إنشاء التقارير"""
            if 'user_id' not in session:
                return jsonify({'error': 'غير مصرح'}), 401
            
            # هنا يمكن إضافة منطق إنشاء التقارير
            return jsonify({'message': f'تقرير {report_type} قيد الإنشاء'})
    
    def setup_socketio_events(self):
        """إعداد أحداث SocketIO للتحديثات المباشرة"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """عند الاتصال"""
            if 'user_id' in session:
                emit('connected', {'message': 'تم الاتصال بنجاح'})
                self.logger.info(f"User {session['username']} connected")
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """عند قطع الاتصال"""
            if 'user_id' in session:
                self.logger.info(f"User {session['username']} disconnected")
        
        @self.socketio.on('request_stats')
        def handle_stats_request():
            """طلب الإحصائيات المحدثة"""
            if 'user_id' in session:
                stats = {
                    'total_students': len(self.student_model.get_all_students()),
                    'present_today': self.attendance_model.get_today_attendance_count('حاضر'),
                    'absent_today': self.attendance_model.get_today_attendance_count('غائب'),
                    'late_today': self.attendance_model.get_today_attendance_count('متأخر')
                }
                emit('stats_update', stats)
    
    def run(self, debug=False, host='0.0.0.0'):
        """تشغيل الخادم"""
        self.logger.info(f"Starting web server on {host}:{self.port}")
        self.socketio.run(self.app, host=host, port=self.port, debug=debug)
    
    def run_in_thread(self, debug=False, host='0.0.0.0'):
        """تشغيل الخادم في خيط منفصل"""
        def run_server():
            self.run(debug=debug, host=host)
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        return server_thread

# دالة مساعدة لإنشاء التطبيق
def create_web_app(db_manager: DatabaseManager, port=5000):
    """إنشاء تطبيق ويب جديد"""
    return WebApp(db_manager, port)
