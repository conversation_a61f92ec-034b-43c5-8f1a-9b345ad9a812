import os
import json
import hashlib
import base64
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Tu<PERSON>, Union
from pathlib import Path
from enum import Enum
import logging

from .states import LicenseState
from .device import get_device_fingerprint
from ..config import APP_DATA_DIR, LICENSE_FILE
from ..utils.crypto import encrypt_data, decrypt_data, generate_key_from_password
from ..utils.helpers import ensure_dir_exists

# إعداد التسجيل
logger = logging.getLogger(__name__)

class LicenseType(Enum):
    DEMO = "demo"
    TRIAL = "trial"
    FULL = "full"

class LicenseManager:
    """
    مدير الترخيص الرئيسي المسؤول عن:
    - إدارة حالات الترخيص المختلفة
    - التفعيل عبر الإنترنت وخارجه
    - التحقق من صحة الترخيص
    - إدارة فترات السماح والتنبيهات
    """
    
    # إعدادات الترخيص
    DEMO_DAYS = 14  # مدة النسخة التجريبية باليوم
    TRIAL_DAYS = 30  # مدة الفترة التجريبية باليوم
    GRACE_DAYS = 7  # فترة السماح باليوم
    HEARTBEAT_INTERVAL = 24 * 60 * 60  # 24 ساعة بالثانية
    
    def __init__(self, app_data_dir: str = None):
        """
        تهيئة مدير الترخيص
        """
        # مجلد تخزين بيانات التطبيق
        self.app_data_dir = Path(app_data_dir) if app_data_dir else APP_DATA_DIR
        self.license_file = self.app_data_dir / LICENSE_FILE
        
        # حالة الترخيص الحالية
        self.license_data = {
            'license_id': None,
            'license_type': None,
            'state': LicenseState.UNLICENSED.name,
            'issued_at': None,
            'expires_at': None,
            'device_fingerprint': None,
            'customer_name': None,
            'customer_email': None,
            'features': {},
            'metadata': {}
        }
        
        # تحميل بيانات الترخيص إذا وجدت
        self._load_license()
        
        # تحديث حالة الترخيص
        self._update_license_state()
    
    def _load_license(self) -> bool:
        """
        تحميل بيانات الترخيص من الملف
        """
        try:
            if not self.license_file.exists():
                return False
                
            with open(self.license_file, 'r', encoding='utf-8') as f:
                encrypted_data = f.read()
            
            if not encrypted_data:
                return False
                
            # فك تشفير البيانات
            decrypted_data = self._decrypt_license(encrypted_data)
            if not decrypted_data:
                return False
                
            self.license_data.update(json.loads(decrypted_data))
            return True
            
        except Exception as e:
            logger.error(f"فشل في تحميل الترخيص: {str(e)}")
            return False
    
    def _save_license(self) -> bool:
        """
        حفظ بيانات الترخيص في الملف
        """
        try:
            ensure_dir_exists(self.app_data_dir)
            
            # تشفير البيانات قبل الحفظ
            license_json = json.dumps(self.license_data, ensure_ascii=False, indent=2)
            encrypted_data = self._encrypt_license(license_json)
            
            # حفظ الملف المؤقت أولاً
            temp_file = f"{self.license_file}.tmp"
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(encrypted_data)
            
            # استبدال الملف القديم بالملف الجديد
            if os.path.exists(self.license_file):
                os.replace(temp_file, self.license_file)
            else:
                os.rename(temp_file, self.license_file)
                
            return True
            
        except Exception as e:
            logger.error(f"فشل في حفظ الترخيص: {str(e)}")
            if os.path.exists(temp_file):
                os.remove(temp_file)
            return False
    
    def _encrypt_license(self, data: str) -> str:
        """
        تشفير بيانات الترخيص
        """
        # يمكنك استخدام خوارزمية تشفير أقوى هنا
        # هذا مثال بسيط للتشفير باستخدام base64
        return base64.b64encode(data.encode('utf-8')).decode('utf-8')
    
    def _decrypt_license(self, data: str) -> str:
        """
        فك تشفير بيانات الترخيص
        """
        try:
            return base64.b64decode(data).decode('utf-8')
        except:
            return ""
    
    def _update_license_state(self) -> None:
        """
        تحديث حالة الترخيص بناءً على التاريخ الحالي
        """
        if not self.license_data.get('expires_at'):
            return
            
        current_time = datetime.utcnow()
        expires_at = datetime.fromisoformat(self.license_data['expires_at'])
        
        if current_time > expires_at:
            # التحقق من فترة السماح
            if self.license_data['state'] == LicenseState.ACTIVE.name:
                grace_end = expires_at + timedelta(days=self.GRACE_DAYS)
                if current_time <= grace_end:
                    self.license_data['state'] = LicenseState.GRACE.name
                else:
                    self.license_data['state'] = LicenseState.EXPIRED.name
            elif self.license_data['state'] in [LicenseState.TRIAL.name, LicenseState.DEMO.name]:
                self.license_data['state'] = LicenseState.EXPIRED.name
                
            self._save_license()
    
    def get_license_state(self) -> LicenseState:
        """
        الحصول على حالة الترخيص الحالية
        """
        return LicenseState[self.license_data.get('state', 'UNLICENSED')]
    
    def get_license_info(self) -> Dict[str, Any]:
        """
        الحصول على معلومات الترخيص
        """
        return {
            'license_id': self.license_data.get('license_id'),
            'type': self.license_data.get('license_type'),
            'state': self.license_data.get('state'),
            'issued_at': self.license_data.get('issued_at'),
            'expires_at': self.license_data.get('expires_at'),
            'customer_name': self.license_data.get('customer_name'),
            'customer_email': self.license_data.get('customer_email'),
            'features': self.license_data.get('features', {}),
            'is_active': self.get_license_state().is_active(),
            'is_restricted': self.get_license_state().is_restricted(),
            'is_expired': self.get_license_state().is_expired(),
            'days_remaining': self.get_days_remaining()
        }
    
    def get_days_remaining(self) -> int:
        """
        الحصول على عدد الأيام المتبقية حتى انتهاء الترخيص
        """
        if not self.license_data.get('expires_at'):
            return 0
            
        expires_at = datetime.fromisoformat(self.license_data['expires_at'])
        current_time = datetime.utcnow()
        
        if current_time >= expires_at:
            return 0
            
        return (expires_at - current_time).days
    
    def activate_offline(self, license_key: str, customer_name: str = "") -> Tuple[bool, str]:
        """
        تفعيل الترخيص دون اتصال بالإنترنت
        """
        try:
            # هذا مثال بسيط - يجب استبداله بمنطق التحقق الفعلي
            if not license_key or len(license_key) < 10:
                return False, "مفتاح الترخيص غير صالح"
                
            # في الواقع، يجب التحقق من توقيع الترخيص والمفتاح العام
            # هذا مثال بسيط للتوضيح فقط
            
            # تعيين بيانات الترخيص
            self.license_data.update({
                'license_id': f"LIC-{hashlib.md5(license_key.encode()).hexdigest()[:8]}",
                'license_type': LicenseType.FULL.value,
                'state': LicenseState.ACTIVE.name,
                'issued_at': datetime.utcnow().isoformat(),
                'expires_at': (datetime.utcnow() + timedelta(days=365)).isoformat(),
                'device_fingerprint': get_device_fingerprint(),
                'customer_name': customer_name or "عميل غير معروف",
                'features': {
                    'max_students': 1000,
                    'max_users': 10,
                    'reports': True,
                    'backup': True,
                    'support': True
                }
            })
            
            # حفظ الترخيص
            if self._save_license():
                return True, "تم تفعيل الترخيص بنجاح"
            else:
                return False, "حدث خطأ أثناء حفظ بيانات الترخيص"
                
        except Exception as e:
            logger.error(f"فشل في تفعيل الترخيص: {str(e)}")
            return False, f"حدث خطأ: {str(e)}"
    
    def activate_demo(self) -> Tuple[bool, str]:
        """
        تفعيل وضع التجربة
        """
        try:
            self.license_data.update({
                'license_id': f"DEMO-{hashlib.md5(str(time.time()).encode()).hexdigest()[:8]}",
                'license_type': LicenseType.DEMO.value,
                'state': LicenseState.DEMO.name,
                'issued_at': datetime.utcnow().isoformat(),
                'expires_at': (datetime.utcnow() + timedelta(days=self.DEMO_DAYS)).isoformat(),
                'device_fingerprint': get_device_fingerprint(),
                'customer_name': "نسخة تجريبية",
                'features': {
                    'max_students': 50,
                    'max_users': 1,
                    'reports': False,
                    'backup': False,
                    'support': False,
                    'demo_banner': True
                }
            })
            
            if self._save_license():
                return True, "تم تفعيل النسخة التجريبية بنجاح"
            else:
                return False, "حدث خطأ أثناء حفظ بيانات النسخة التجريبية"
                
        except Exception as e:
            logger.error(f"فشل في تفعيل النسخة التجريبية: {str(e)}")
            return False, f"حدث خطأ: {str(e)}"
    
    def deactivate(self) -> bool:
        """
        تعطيل الترخيص
        """
        try:
            if os.path.exists(self.license_file):
                os.remove(self.license_file)
            self.license_data = {
                'state': LicenseState.UNLICENSED.name
            }
            return True
        except Exception as e:
            logger.error(f"فشل في تعطيل الترخيص: {str(e)}")
            return False
    
    def check_feature_access(self, feature: str) -> bool:
        """
        التحقق من صلاحية استخدام ميزة معينة
        """
        # إذا كان الترخيص منتهيًا، لا تسمح بأي ميزات
        if self.get_license_state().is_expired():
            return False
            
        # الميزات المسموحة في الوضع غير المفعل
        allowed_in_unlicensed = ['license', 'demo', 'exit']
        if self.get_license_state() == LicenseState.UNLICENSED:
            return feature in allowed_in_unlicensed
            
        # التحقق من الميزات المحددة في الترخيص
        return self.license_data.get('features', {}).get(feature, False)
    
    def get_activation_request(self) -> str:
        """
        إنشاء طلب تفعيل للاستخدام في التفعيل دون اتصال
        """
        request_data = {
            'device_fingerprint': get_device_fingerprint(),
            'system_info': {
                'os': platform.system(),
                'hostname': platform.node(),
                'machine': platform.machine(),
                'processor': platform.processor(),
                'timestamp': datetime.utcnow().isoformat()
            }
        }
        return base64.b64encode(json.dumps(request_data).encode('utf-8')).decode('utf-8')
    
    def process_activation_response(self, response_data: str) -> Tuple[bool, str]:
        """
        معالجة استجابة التفعيل من السيرفر
        """
        try:
            # فك تشفير البيانات
            decoded_data = base64.b64decode(response_data).decode('utf-8')
            license_data = json.loads(decoded_data)
            
            # التحقق من التوقيع (يجب تنفيذه)
            if not self._verify_license_signature(license_data):
                return False, "توقيع الترخيص غير صالح"
            
            # تحديث بيانات الترخيص
            self.license_data.update(license_data)
            
            # حفظ الترخيص
            if self._save_license():
                return True, "تم تفعيل الترخيص بنجاح"
            else:
                return False, "حدث خطأ أثناء حفظ بيانات الترخيص"
                
        except Exception as e:
            logger.error(f"فشل في معالجة استجابة التفعيل: {str(e)}")
            return False, f"حدث خطأ: {str(e)}"
    
    def _verify_license_signature(self, license_data: Dict[str, Any]) -> bool:
        """
        التحقق من توقيع الترخيص (وهمي - يجب تنفيذه)
        """
        # في التطبيق الحقيقي، يجب التحقق من التوقيع الرقمي
        # باستخدام المفتاح العام المضمن في التطبيق
        return True
    
    def is_license_valid(self) -> bool:
        """
        التحقق من صلاحية الترخيص
        """
        state = self.get_license_state()
        
        # إذا لم يتم التفعيل بعد
        if state == LicenseState.UNLICENSED:
            return False
            
        # إذا كان الترخيص منتهيًا أو موقوفًا
        if state in [LicenseState.EXPIRED, LicenseState.BLOCKED]:
            return False
            
        # التحقق من بصمة الجهاز
        if self.license_data.get('device_fingerprint') != get_device_fingerprint():
            logger.warning("تم اكتشاف تغيير في بصمة الجهاز")
            # يمكنك اختيار ما إذا كنت تريد رفض الترخيص في هذه الحالة
            # أو السماح مع تسجيل التحذير
            # return False
            
        return True
