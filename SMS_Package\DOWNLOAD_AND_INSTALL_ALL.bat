@echo off
title Student Management System - Complete Installation

cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Complete Installation Package                 ║
echo ║                Student Management System                     ║
echo ║                    Advanced Student Management System                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo This will download and install everything needed to run the application.
echo.

REM Check if we're running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator: YES
) else (
    echo Running as Administrator: NO
    echo Some features may require administrator privileges.
)

echo.
echo Step 1: Checking Python installation...
echo ========================================

python --version >nul 2>&1
if errorlevel 1 (
    echo Python NOT found. Will attempt to install...
    echo.
    
    echo Downloading Python installer...
    echo This may take a few minutes depending on your internet speed.
    echo.
    
    REM Try to download Python using PowerShell
    powershell -Command "& {
        try {
            Write-Host 'Downloading Python 3.11.0...'
            $url = 'https://www.python.org/ftp/python/3.11.0/python-3.11.0-amd64.exe'
            $output = 'python-installer.exe'
            Invoke-WebRequest -Uri $url -OutFile $output
            Write-Host 'Download completed!'
        } catch {
            Write-Host 'Download failed. Please check your internet connection.'
            exit 1
        }
    }"
    
    if exist "python-installer.exe" (
        echo.
        echo Installing Python...
        echo IMPORTANT: When the installer opens, make sure to:
        echo 1. Check "Add Python to PATH"
        echo 2. Choose "Install Now"
        echo.
        pause
        
        REM Install Python silently with PATH
        python-installer.exe /quiet InstallAllUsers=1 PrependPath=1 Include_test=0
        
        echo Waiting for installation to complete...
        timeout /t 30 /nobreak >nul
        
        REM Clean up installer
        del python-installer.exe >nul 2>&1
        
        echo.
        echo Python installation completed!
        echo Please restart this script to continue.
        echo.
        pause
        exit /b 0
    ) else (
        echo.
        echo Failed to download Python installer.
        echo Please manually download and install Python from:
        echo https://python.org/downloads
        echo.
        echo Make sure to check "Add Python to PATH" during installation!
        echo.
        pause
        exit /b 1
    )
) else (
    echo Python found!
    python --version
)

echo.
echo Step 2: Installing required packages...
echo =======================================

echo Installing PyQt5...
pip install PyQt5 --quiet --upgrade
if errorlevel 1 (
    echo Failed with pip, trying alternative...
    pip install PyQt5 --user --quiet
)

echo Installing additional packages...
pip install reportlab openpyxl Pillow --quiet --upgrade

echo.
echo Step 3: Preparing application...
echo ================================

REM Create necessary directories
if not exist "data" mkdir data
if not exist "logs" mkdir logs
if not exist "backups" mkdir backups
if not exist "exports" mkdir exports
if not exist "assets" mkdir assets

echo Created necessary directories.

REM Create a simple launcher script
echo @echo off > LAUNCH_APP.bat
echo title Student Management System >> LAUNCH_APP.bat
echo echo Starting Student Management System... >> LAUNCH_APP.bat
echo echo. >> LAUNCH_APP.bat
echo python main.py >> LAUNCH_APP.bat
echo pause >> LAUNCH_APP.bat

echo Created launcher script.

echo.
echo Step 4: Testing installation...
echo ===============================

echo Testing Python...
python --version
if errorlevel 1 (
    echo Python test failed!
    goto error
)

echo Testing PyQt5...
python -c "import PyQt5.QtWidgets; print('PyQt5 OK')" 2>nul
if errorlevel 1 (
    echo PyQt5 test failed!
    goto error
)

echo Testing application files...
if not exist "main.py" (
    echo main.py not found!
    goto error
)

if not exist "src" (
    echo src directory not found!
    goto error
)

echo All tests passed!

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    INSTALLATION COMPLETE!                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo The Student Management System is now ready to use!
echo.
echo Login credentials:
echo Username: admin
echo Password: admin123
echo.

set /p choice="Do you want to start the application now? (y/n): "
if /i "%choice%"=="y" goto start_app
if /i "%choice%"=="yes" goto start_app

echo.
echo To start the application later, double-click: LAUNCH_APP.bat
echo.
goto end

:start_app
echo.
echo Starting Student Management System...
echo.
python main.py
goto end

:error
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    INSTALLATION FAILED!                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo Please try the following:
echo.
echo 1. Run this script as Administrator
echo 2. Check your internet connection
echo 3. Manually install Python from: https://python.org
echo 4. Make sure to check "Add Python to PATH"
echo.

:end
echo.
echo For help and documentation, see:
echo - USER_GUIDE.md
echo - READ_ME_FIRST.txt
echo.
pause
