Set WshShell = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' الحصول على مسار المجلد الحالي
currentDir = fso.GetParentFolderName(WScript.ScriptFullName)

' تغيير المجلد الحالي
WshShell.CurrentDirectory = currentDir

' محاولة تشغيل البرنامج بطرق مختلفة (بدون إظهار نافذة)
On Error Resume Next

' الطريقة الأولى: pythonw (الأفضل للتطبيقات الرسومية)
WshShell.Run "pythonw main.py", 0, False
If Err.Number = 0 Then
    WScript.Quit
End If

' الطريقة الثانية: python عادي
Err.Clear
WshShell.Run "python main.py", 0, False
If Err.Number = 0 Then
    WScript.Quit
End If

' الطريقة الثالثة: py launcher
Err.Clear
WshShell.Run "py main.py", 0, False
If Err.Number = 0 Then
    WScript.Quit
End If

' إذا فشلت جميع الطرق، عرض رسالة خطأ
MsgBox "خطأ: لم يتم العثور على Python أو فشل في تشغيل البرنامج" & vbCrLf & "يرجى التأكد من تثبيت Python بشكل صحيح", vbCritical, "خطأ في التشغيل"
