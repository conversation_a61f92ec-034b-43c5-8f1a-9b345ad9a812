# -*- coding: utf-8 -*-
"""
نافذة إدارة الطلاب
Students Management Window
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                            QComboBox, QMessageBox, QFrame, QGroupBox, QFormLayout,
                            QHeaderView, QAbstractItemView, QSplitter)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from ..models.student import Student
from ..database.database_manager import DatabaseManager
from ..utils.styles import get_form_style, get_table_style, get_arabic_font_style
from ..utils.message_boxes import show_error_message, show_success_message, show_warning_message, show_confirmation_dialog
from ..utils.messaging import messaging_system

# استيراد مولد QR Code
try:
    import qrcode
    from PIL import Image
    import os
    import json
    from datetime import datetime
    QR_AVAILABLE = True
except ImportError:
    QR_AVAILABLE = False

class StudentsWindow(QWidget):
    """نافذة إدارة الطلاب"""
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
        self.student_model = Student(db_manager)
        self.current_student_id = None
        
        self.init_ui()
        self.setup_styles()
        self.load_students()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة الطلاب")
        self.setGeometry(100, 100, 1000, 700)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout()
        
        # إنشاء المقسم
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - نموذج الطالب
        student_form = self.create_student_form()
        splitter.addWidget(student_form)
        
        # الجانب الأيمن - قائمة الطلاب
        students_list = self.create_students_list()
        splitter.addWidget(students_list)
        
        # تحديد نسب العرض
        splitter.setSizes([300, 700])
        
        main_layout.addWidget(splitter)
        self.setLayout(main_layout)
    
    def create_student_form(self):
        """إنشاء نموذج بيانات الطالب"""
        group = QGroupBox("إدارة الطلاب")
        layout = QVBoxLayout()

        # زر طالب جديد
        self.new_student_button = QPushButton("+ طالب جديد")
        self.new_student_button.setObjectName("newStudentButton")
        self.new_student_button.clicked.connect(self.toggle_student_form)
        self.new_student_button.setStyleSheet("""
            QPushButton#newStudentButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 18px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton#newStudentButton:hover {
                background-color: #229954;
                transform: translateY(-2px);
            }
        """)
        layout.addWidget(self.new_student_button)

        # نموذج إضافة الطالب (مخفي في البداية)
        self.student_form_widget = QWidget()
        self.student_form_widget.setVisible(False)
        form_layout = QFormLayout()
        
        # كود الطالب
        self.student_code_input = QLineEdit()
        self.student_code_input.setPlaceholderText("سيتم توليده تلقائياً")
        form_layout.addRow("كود الطالب:", self.student_code_input)
        
        # الاسم الرباعي
        self.full_name_input = QLineEdit()
        self.full_name_input.setPlaceholderText("أدخل الاسم الرباعي")
        form_layout.addRow("الاسم الرباعي:", self.full_name_input)
        
        # النوع
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["ذكر", "أنثى"])
        form_layout.addRow("النوع:", self.gender_combo)
        
        # المرحلة
        self.stage_combo = QComboBox()
        self.stage_combo.addItems(["إعدادي", "ثانوي"])
        self.stage_combo.currentTextChanged.connect(self.update_grades_combo)
        form_layout.addRow("المرحلة:", self.stage_combo)
        
        # الصف
        self.grade_combo = QComboBox()
        form_layout.addRow("الصف الدراسي:", self.grade_combo)

        # المجموعة
        self.group_combo = QComboBox()
        self.group_combo.setEditable(True)  # يمكن إضافة مجموعة جديدة
        self.group_combo.addItems(["لا توجد مجموعة", "مجموعة أ", "مجموعة ب", "مجموعة ج"])
        form_layout.addRow("المجموعة:", self.group_combo)

        # رقم الهاتف
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("أدخل رقم الهاتف")
        form_layout.addRow("رقم الهاتف:", self.phone_input)

        # رقم ولي الأمر
        self.parent_phone_input = QLineEdit()
        self.parent_phone_input.setPlaceholderText("أدخل رقم ولي الأمر")
        form_layout.addRow("رقم ولي الأمر:", self.parent_phone_input)

        # درجة الجغرافيا
        self.geography_score_input = QLineEdit()
        self.geography_score_input.setPlaceholderText("0")
        form_layout.addRow("درجة الجغرافيا:", self.geography_score_input)

        # درجة التاريخ
        self.history_score_input = QLineEdit()
        self.history_score_input.setPlaceholderText("0")
        form_layout.addRow("درجة التاريخ:", self.history_score_input)
        
        self.student_form_widget.setLayout(form_layout)
        layout.addWidget(self.student_form_widget)
        
        # أزرار العمليات
        buttons_layout = QVBoxLayout()
        
        self.add_button = QPushButton("إضافة طالب")
        self.add_button.clicked.connect(self.add_student)
        buttons_layout.addWidget(self.add_button)
        
        self.update_button = QPushButton("تحديث البيانات")
        self.update_button.clicked.connect(self.update_student)
        self.update_button.setEnabled(False)
        buttons_layout.addWidget(self.update_button)
        
        self.delete_button = QPushButton("حذف الطالب")
        self.delete_button.clicked.connect(self.delete_student)
        self.delete_button.setEnabled(False)
        buttons_layout.addWidget(self.delete_button)
        
        self.clear_button = QPushButton("مسح النموذج")
        self.clear_button.clicked.connect(self.clear_form)
        buttons_layout.addWidget(self.clear_button)
        
        self.generate_code_button = QPushButton("توليد كود جديد")
        self.generate_code_button.clicked.connect(self.generate_student_code)
        buttons_layout.addWidget(self.generate_code_button)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        group.setLayout(layout)
        
        # تحديث قائمة الصفوف عند التهيئة
        self.update_grades_combo()
        
        return group

    def toggle_student_form(self):
        """إظهار/إخفاء نموذج إضافة الطالب"""
        if self.student_form_widget.isVisible():
            self.student_form_widget.setVisible(False)
            self.new_student_button.setText("+ طالب جديد")
        else:
            self.student_form_widget.setVisible(True)
            self.new_student_button.setText("- إخفاء النموذج")
            self.generate_student_code()  # توليد كود جديد

    def create_students_list(self):
        """إنشاء قائمة الطلاب"""
        group = QGroupBox("قائمة الطلاب")
        layout = QVBoxLayout()
        
        # شريط البحث والفلترة
        search_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث بالاسم أو الكود...")
        self.search_input.textChanged.connect(self.search_students)
        search_layout.addWidget(QLabel("البحث:"))
        search_layout.addWidget(self.search_input)
        
        self.filter_stage_combo = QComboBox()
        self.filter_stage_combo.addItems(["الكل", "إعدادي", "ثانوي"])
        self.filter_stage_combo.currentTextChanged.connect(self.filter_students)
        search_layout.addWidget(QLabel("المرحلة:"))
        search_layout.addWidget(self.filter_stage_combo)
        
        self.filter_grade_combo = QComboBox()
        self.filter_grade_combo.setObjectName("filter_grade_combo")
        self.filter_grade_combo.addItem("الكل")
        self.filter_grade_combo.currentTextChanged.connect(self.filter_students)
        search_layout.addWidget(QLabel("الصف:"))
        search_layout.addWidget(self.filter_grade_combo)
        
        layout.addLayout(search_layout)
        
        # جدول الطلاب
        self.students_table = QTableWidget()
        self.students_table.setColumnCount(9)
        self.students_table.setHorizontalHeaderLabels([
            "الكود", "الاسم", "النوع", "المرحلة", "الصف", "جغرافيا", "تاريخ", "📱", "✈️"
        ])
        
        # إعدادات الجدول
        header = self.students_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        self.students_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.students_table.setAlternatingRowColors(True)
        self.students_table.itemSelectionChanged.connect(self.on_student_selected)
        
        layout.addWidget(self.students_table)
        
        # إحصائيات سريعة
        stats_layout = QHBoxLayout()
        self.total_students_label = QLabel("إجمالي الطلاب: 0")
        self.selected_stage_label = QLabel("المرحلة المحددة: 0")
        stats_layout.addWidget(self.total_students_label)
        stats_layout.addWidget(self.selected_stage_label)
        stats_layout.addStretch()
        
        layout.addLayout(stats_layout)
        
        group.setLayout(layout)
        return group
    
    def update_grades_combo(self):
        """تحديث قائمة الصفوف حسب المرحلة"""
        self.grade_combo.clear()
        stage = self.stage_combo.currentText()
        
        if stage == "إعدادي":
            grades = ["أولى إعدادي", "ثانية إعدادي", "ثالثة إعدادي"]
        else:  # ثانوي
            grades = ["أولى ثانوي", "ثانية ثانوي", "ثالثة ثانوي"]
        
        self.grade_combo.addItems(grades)
        
        # تحديث فلتر الصفوف أيضاً
        if hasattr(self, 'filter_grade_combo'):
            current_filter = self.filter_grade_combo.currentText()
            self.filter_grade_combo.clear()
            self.filter_grade_combo.addItem("الكل")
            self.filter_grade_combo.addItems(grades)

            # استعادة الفلتر السابق إن أمكن
            index = self.filter_grade_combo.findText(current_filter)
            if index >= 0:
                self.filter_grade_combo.setCurrentIndex(index)
    
    def generate_student_code(self):
        """توليد كود طالب جديد"""
        code = self.student_model.generate_student_code()
        self.student_code_input.setText(code)
    
    def clear_form(self):
        """مسح نموذج البيانات"""
        self.student_code_input.clear()
        self.full_name_input.clear()
        self.phone_input.clear()
        self.parent_phone_input.clear()
        self.gender_combo.setCurrentIndex(0)
        self.stage_combo.setCurrentIndex(0)
        self.grade_combo.setCurrentIndex(0)
        self.group_combo.setCurrentIndex(0)
        self.geography_score_input.clear()
        self.history_score_input.clear()
        
        self.current_student_id = None
        self.add_button.setEnabled(True)
        self.update_button.setEnabled(False)
        self.delete_button.setEnabled(False)
    
    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.student_code_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال كود الطالب")
            return False
        
        if not self.full_name_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال الاسم الرباعي")
            return False
        
        # التحقق من الدرجات
        try:
            geography_score = float(self.geography_score_input.text() or "0")
            history_score = float(self.history_score_input.text() or "0")
            
            if geography_score < 0 or geography_score > 100:
                QMessageBox.warning(self, "خطأ", "درجة الجغرافيا يجب أن تكون بين 0 و 100")
                return False
            
            if history_score < 0 or history_score > 100:
                QMessageBox.warning(self, "خطأ", "درجة التاريخ يجب أن تكون بين 0 و 100")
                return False
                
        except ValueError:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال درجات صحيحة")
            return False
        
        return True

    def add_student(self):
        """إضافة طالب جديد"""
        if not self.validate_form():
            return

        student_data = {
            'student_code': self.student_code_input.text().strip(),
            'full_name': self.full_name_input.text().strip(),
            'gender': self.gender_combo.currentText(),
            'stage': self.stage_combo.currentText(),
            'grade': self.grade_combo.currentText(),
            'group_name': self.group_combo.currentText(),
            'phone': self.phone_input.text().strip(),
            'parent_phone': self.parent_phone_input.text().strip(),
            'geography_score': float(self.geography_score_input.text() or "0"),
            'history_score': float(self.history_score_input.text() or "0")
        }

        student_id = self.student_model.create_student(student_data)

        if student_id:
            # توليد QR Code تلقائياً للطالب الجديد
            self.generate_student_qr_code(student_data['student_code'], student_data['full_name'])

            show_success_message(self, "نجح", "تم إضافة الطالب بنجاح وتوليد QR Code")
            self.clear_form()
            self.load_students()
            self.student_form_widget.setVisible(False)
            self.new_student_button.setText("+ طالب جديد")
        else:
            show_error_message(self, "خطأ", "فشل في إضافة الطالب")

    def update_student(self):
        """تحديث بيانات الطالب"""
        if not self.current_student_id or not self.validate_form():
            return

        student_data = {
            'student_code': self.student_code_input.text().strip(),
            'full_name': self.full_name_input.text().strip(),
            'gender': self.gender_combo.currentText(),
            'stage': self.stage_combo.currentText(),
            'grade': self.grade_combo.currentText(),
            'geography_score': float(self.geography_score_input.text() or "0"),
            'history_score': float(self.history_score_input.text() or "0")
        }

        if self.student_model.update_student(self.current_student_id, student_data):
            QMessageBox.information(self, "نجح", "تم تحديث بيانات الطالب بنجاح")
            self.load_students()
        else:
            QMessageBox.critical(self, "خطأ", "فشل في تحديث بيانات الطالب")

    def delete_student(self):
        """حذف الطالب"""
        if not self.current_student_id:
            return

        reply = QMessageBox.question(
            self, 'تأكيد الحذف',
            'هل تريد حذف هذا الطالب؟\nسيتم حذف جميع بياناته بما في ذلك الحضور والدرجات.',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            if self.student_model.delete_student(self.current_student_id):
                QMessageBox.information(self, "نجح", "تم حذف الطالب بنجاح")
                self.clear_form()
                self.load_students()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حذف الطالب")

    def load_students(self):
        """تحميل قائمة الطلاب"""
        students = self.student_model.get_all_students()

        self.students_table.setRowCount(len(students))

        for row, student in enumerate(students):
            self.students_table.setItem(row, 0, QTableWidgetItem(student['student_code']))
            self.students_table.setItem(row, 1, QTableWidgetItem(student['full_name']))
            self.students_table.setItem(row, 2, QTableWidgetItem(student['gender']))
            self.students_table.setItem(row, 3, QTableWidgetItem(student['stage']))
            self.students_table.setItem(row, 4, QTableWidgetItem(student['grade']))
            self.students_table.setItem(row, 5, QTableWidgetItem(f"{student['geography_score']:.1f}"))
            self.students_table.setItem(row, 6, QTableWidgetItem(f"{student['history_score']:.1f}"))

            # أزرار الرسائل
            self.add_messaging_buttons(row, student)

            # حفظ معرف الطالب في البيانات
            self.students_table.item(row, 0).setData(Qt.UserRole, student['id'])

        self.update_statistics()

    def add_messaging_buttons(self, row, student):
        """إضافة أزرار الرسائل للطالب"""
        # زر WhatsApp
        whatsapp_btn = QPushButton("📱")
        whatsapp_btn.setToolTip("إرسال رسالة عبر WhatsApp")
        whatsapp_btn.setStyleSheet("""
            QPushButton {
                background-color: #25D366;
                color: white;
                border: none;
                border-radius: 15px;
                font-size: 16px;
                max-width: 30px;
                max-height: 30px;
                min-width: 30px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #128C7E;
                transform: scale(1.1);
            }
        """)
        whatsapp_btn.clicked.connect(lambda: self.send_whatsapp_message(student))
        self.students_table.setCellWidget(row, 7, whatsapp_btn)

        # زر Telegram
        telegram_btn = QPushButton("✈️")
        telegram_btn.setToolTip("إرسال رسالة عبر Telegram")
        telegram_btn.setStyleSheet("""
            QPushButton {
                background-color: #0088cc;
                color: white;
                border: none;
                border-radius: 15px;
                font-size: 16px;
                max-width: 30px;
                max-height: 30px;
                min-width: 30px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #006699;
                transform: scale(1.1);
            }
        """)
        telegram_btn.clicked.connect(lambda: self.send_telegram_message(student))
        self.students_table.setCellWidget(row, 8, telegram_btn)

    def send_whatsapp_message(self, student):
        """إرسال رسالة WhatsApp"""
        if not student.get('parent_phone'):
            show_warning_message(self, "تحذير", "رقم ولي الأمر غير متوفر")
            return

        message = f"""
مرحباً،

هذه رسالة من نظام إدارة الطلاب
بخصوص الطالب/ة: {student['full_name']}

للاستفسار يرجى التواصل مع:
نظام إدارة الطلاب المتطور - نظام شامل لإدارة الطلاب
        """.strip()

        messaging_system.show_messaging_options(
            self,
            student['parent_phone'],
            message,
            "رسالة عامة"
        )

    def send_telegram_message(self, student):
        """إرسال رسالة Telegram"""
        if not student.get('parent_phone'):
            show_warning_message(self, "تحذير", "رقم ولي الأمر غير متوفر")
            return

        message = f"""
مرحباً،

هذه رسالة من نظام إدارة الطلاب
بخصوص الطالب/ة: {student['full_name']}

للاستفسار يرجى التواصل مع:
نظام إدارة الطلاب المتطور - نظام شامل لإدارة الطلاب
        """.strip()

        messaging_system.show_messaging_options(
            self,
            student['parent_phone'],
            message,
            "رسالة عامة"
        )

    def search_students(self):
        """البحث عن الطلاب"""
        search_term = self.search_input.text().strip()

        if search_term:
            students = self.student_model.search_students(search_term)
        else:
            students = self.student_model.get_all_students()

        self.display_students(students)

    def filter_students(self):
        """فلترة الطلاب حسب المرحلة والصف"""
        stage_filter = self.filter_stage_combo.currentText()
        grade_filter = self.filter_grade_combo.currentText()

        students = self.student_model.get_all_students()

        # تطبيق فلتر المرحلة
        if stage_filter != "الكل":
            students = [s for s in students if s['stage'] == stage_filter]

        # تطبيق فلتر الصف
        if grade_filter != "الكل":
            students = [s for s in students if s['grade'] == grade_filter]

        self.display_students(students)

    def display_students(self, students):
        """عرض قائمة الطلاب"""
        self.students_table.setRowCount(len(students))

        for row, student in enumerate(students):
            self.students_table.setItem(row, 0, QTableWidgetItem(student['student_code']))
            self.students_table.setItem(row, 1, QTableWidgetItem(student['full_name']))
            self.students_table.setItem(row, 2, QTableWidgetItem(student['gender']))
            self.students_table.setItem(row, 3, QTableWidgetItem(student['stage']))
            self.students_table.setItem(row, 4, QTableWidgetItem(student['grade']))
            self.students_table.setItem(row, 5, QTableWidgetItem(f"{student['geography_score']:.1f}"))
            self.students_table.setItem(row, 6, QTableWidgetItem(f"{student['history_score']:.1f}"))

            # حفظ معرف الطالب في البيانات
            self.students_table.item(row, 0).setData(Qt.UserRole, student['id'])

        self.update_statistics()

    def on_student_selected(self):
        """معالجة اختيار طالب من القائمة"""
        current_row = self.students_table.currentRow()

        if current_row >= 0:
            # جلب معرف الطالب
            student_id_item = self.students_table.item(current_row, 0)
            if student_id_item:
                student_id = student_id_item.data(Qt.UserRole)
                self.load_student_data(student_id)

    def load_student_data(self, student_id):
        """تحميل بيانات طالب معين في النموذج"""
        student = self.student_model.get_student_by_id(student_id)

        if student:
            self.current_student_id = student_id

            self.student_code_input.setText(student['student_code'])
            self.full_name_input.setText(student['full_name'])

            # تحديد النوع
            gender_index = self.gender_combo.findText(student['gender'])
            if gender_index >= 0:
                self.gender_combo.setCurrentIndex(gender_index)

            # تحديد المرحلة
            stage_index = self.stage_combo.findText(student['stage'])
            if stage_index >= 0:
                self.stage_combo.setCurrentIndex(stage_index)

            # تحديد الصف
            grade_index = self.grade_combo.findText(student['grade'])
            if grade_index >= 0:
                self.grade_combo.setCurrentIndex(grade_index)

            self.geography_score_input.setText(str(student['geography_score']))
            self.history_score_input.setText(str(student['history_score']))

            # تفعيل أزرار التحديث والحذف
            self.add_button.setEnabled(False)
            self.update_button.setEnabled(True)
            self.delete_button.setEnabled(True)

    def update_statistics(self):
        """تحديث الإحصائيات"""
        total_rows = self.students_table.rowCount()
        self.total_students_label.setText(f"إجمالي الطلاب: {total_rows}")

        # إحصائيات المرحلة المحددة
        stage_filter = self.filter_stage_combo.currentText()
        if stage_filter != "الكل":
            stage_count = 0
            for row in range(total_rows):
                stage_item = self.students_table.item(row, 3)
                if stage_item and stage_item.text() == stage_filter:
                    stage_count += 1
            self.selected_stage_label.setText(f"{stage_filter}: {stage_count}")
        else:
            self.selected_stage_label.setText("جميع المراحل")

    def generate_student_qr_code(self, student_code, student_name):
        """توليد QR Code للطالب تلقائياً"""
        if not QR_AVAILABLE:
            print("⚠️ مكتبات QR Code غير متوفرة")
            return False

        try:
            # إنشاء مجلد QR Codes إذا لم يكن موجوداً
            qr_folder = "qr_codes"
            os.makedirs(qr_folder, exist_ok=True)

            # بيانات QR Code
            qr_data = {
                'student_code': student_code,
                'student_name': student_name,
                'generated_date': str(datetime.now().date()),
                'type': 'student_attendance'
            }

            # تحويل البيانات إلى JSON
            import json
            qr_text = json.dumps(qr_data, ensure_ascii=False)

            # إنشاء QR Code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(qr_text)
            qr.make(fit=True)

            # إنشاء الصورة
            img = qr.make_image(fill_color="black", back_color="white")

            # حفظ الصورة
            filename = f"qr_{student_code}.png"
            filepath = os.path.join(qr_folder, filename)
            img.save(filepath)

            print(f"✅ تم توليد QR Code للطالب {student_name}: {filepath}")
            return True

        except Exception as e:
            print(f"❌ خطأ في توليد QR Code: {e}")
            return False

    def setup_styles(self):
        """تطبيق الأنماط"""
        # تطبيق الأنماط الجديدة
        style = get_form_style() + get_table_style() + get_arabic_font_style()
        self.setStyleSheet(style)
