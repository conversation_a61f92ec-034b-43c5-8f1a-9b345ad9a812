@echo off
chcp 65001 >nul
title نظام الفيديوهات التعليمية والامتحانات المحمية

cls
color 0C
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              نظام الفيديوهات التعليمية المحمية             ║
echo ║                   نظام إدارة الطلاب المتطور                           ║
echo ║              نظام شامل لإدارة الطلاب                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎥 نظام الفيديوهات التعليمية والامتحانات المحمية
echo.

echo 🔒 مميزات الحماية المتقدمة:
echo    ✅ تشفير الفيديوهات بـ AES-256
echo    ✅ حد أقصى 3 مشاهدات لكل فيديو
echo    ✅ منع تسجيل الشاشة تلقائياً
echo    ✅ منع لقطات الشاشة
echo    ✅ شاشة كاملة إجبارية
echo    ✅ منع النسخ والمشاركة
echo    ✅ مراقبة العمليات المحظورة
echo.

echo 📚 مميزات النظام التعليمي:
echo    ✅ ربط الفيديو بالامتحان
echo    ✅ امتحانات تفاعلية
echo    ✅ تصحيح تلقائي
echo    ✅ تقارير مفصلة
echo    ✅ ربط بتليجرام (قريباً)
echo.

echo 🚀 تشغيل النظام...
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود
    echo 🏪 فتح Microsoft Store لتثبيت Python...
    start ms-windows-store://pdp/?ProductId=9NRWMJP3717K
    goto end
)

echo ✅ Python موجود
python --version

echo.
echo 📦 تثبيت المكتبات المطلوبة...

REM تثبيت المكتبات
python -m pip install --quiet PyQt5 cryptography psutil pywin32

echo ✅ تم تثبيت المكتبات

echo.
echo 🎯 تشغيل نظام الفيديوهات...
echo.

python video_exam_system.py

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    دليل النظام المحمي                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🎥 كيفية استخدام نظام الفيديوهات:
echo.
echo 1️⃣ رفع الفيديوهات:
echo    📁 ضع الفيديوهات في مجلد videos/
echo    🔐 سيتم تشفيرها تلقائياً
echo    📝 ربطها بالمادة والصف والترم
echo.
echo 2️⃣ مشاهدة الفيديو:
echo    👤 تسجيل دخول الطالب
echo    📹 اختيار الفيديو المطلوب
echo    🔒 تشغيل في وضع الحماية الكاملة
echo    ⏱️ 3 مشاهدات كحد أقصى
echo.
echo 3️⃣ الامتحان:
echo    📝 يظهر تلقائياً بعد انتهاء الفيديو
echo    ❓ أسئلة اختيارية وصح/خطأ
echo    ⏰ مدة زمنية محددة
echo    ✅ تصحيح تلقائي
echo.
echo 🔒 الحماية المتقدمة:
echo    🚫 منع OBS, Bandicam, Camtasia
echo    🚫 منع Print Screen و Alt+Tab
echo    🚫 منع Escape و F11
echo    🚫 شاشة كاملة إجبارية
echo    🚫 منع النقر بالزر الأيمن
echo.
echo ⚠️ تحذيرات مهمة:
echo    • أي محاولة تسجيل = إغلاق فوري
echo    • أي محاولة نسخ = إغلاق فوري
echo    • استخدام مفاتيح محظورة = إغلاق فوري
echo    • تشغيل برامج تسجيل = إغلاق فوري
echo.
echo 📊 التقارير:
echo    📈 نتائج الامتحانات
echo    📊 إحصائيات المشاهدة
echo    📝 تقارير مفصلة للمعلم
echo    📱 إشعارات تلقائية
echo.
echo 🎓 تم التطوير بواسطة: م/ حسام أسامة
echo    مهندس برمجيات - مطور تطبيقات
echo    مصمم خصيصاً لنظام إدارة الطلاب المتطور
echo.
echo 🏆 أول نظام فيديوهات تعليمية محمي باللغة العربية!
echo.
pause
