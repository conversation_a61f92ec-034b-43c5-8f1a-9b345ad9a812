# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات المتقدم
Advanced Database Manager
"""

import os
import json
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime
import sqlite3

try:
    from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String, DateTime, Text, Float, Boolean
    from sqlalchemy.orm import sessionmaker, declarative_base
    from sqlalchemy.exc import SQLAlchemyError
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False
    logging.warning("SQLAlchemy غير متاح - سيتم استخدام SQLite فقط")

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# قاعدة النماذج
if SQLALCHEMY_AVAILABLE:
    Base = declarative_base()
else:
    Base = None

class DatabaseConfig:
    """إعدادات قاعدة البيانات"""
    
    def __init__(self, config_file="database_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """تحميل إعدادات قاعدة البيانات"""
        default_config = {
            "database_type": "sqlite",  # sqlite, postgresql, mysql
            "sqlite": {
                "database_path": "data/students.db"
            },
            "postgresql": {
                "host": "localhost",
                "port": 5432,
                "database": "sms_db",
                "username": "sms_user",
                "password": "sms_password"
            },
            "mysql": {
                "host": "localhost",
                "port": 3306,
                "database": "sms_db",
                "username": "sms_user",
                "password": "sms_password"
            },
            "connection_pool": {
                "pool_size": 10,
                "max_overflow": 20,
                "pool_timeout": 30,
                "pool_recycle": 3600
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # دمج مع الإعدادات الافتراضية
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            except Exception as e:
                logger.warning(f"فشل في تحميل إعدادات قاعدة البيانات: {e}")
        
        # حفظ الإعدادات الافتراضية
        self.save_config(default_config)
        return default_config
    
    def save_config(self, config: Dict[str, Any]):
        """حفظ إعدادات قاعدة البيانات"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"فشل في حفظ إعدادات قاعدة البيانات: {e}")
    
    def get_connection_string(self) -> str:
        """الحصول على نص الاتصال بقاعدة البيانات"""
        db_type = self.config.get("database_type", "sqlite")
        
        if db_type == "sqlite":
            db_path = self.config["sqlite"]["database_path"]
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            return f"sqlite:///{db_path}"
        
        elif db_type == "postgresql":
            pg_config = self.config["postgresql"]
            return (f"postgresql://{pg_config['username']}:{pg_config['password']}"
                   f"@{pg_config['host']}:{pg_config['port']}/{pg_config['database']}")
        
        elif db_type == "mysql":
            mysql_config = self.config["mysql"]
            return (f"mysql+pymysql://{mysql_config['username']}:{mysql_config['password']}"
                   f"@{mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}")
        
        else:
            raise ValueError(f"نوع قاعدة البيانات غير مدعوم: {db_type}")

class AdvancedDatabaseManager:
    """مدير قاعدة البيانات المتقدم"""
    
    def __init__(self, config_file="database_config.json"):
        if not SQLALCHEMY_AVAILABLE:
            raise ImportError("SQLAlchemy غير متاح. يرجى تثبيته: pip install SQLAlchemy")

        self.config = DatabaseConfig(config_file)
        self.engine = None
        self.Session = None
        self.metadata = MetaData()

        # إعداد الاتصال
        self.setup_connection()
    
    def setup_connection(self):
        """إعداد الاتصال بقاعدة البيانات"""
        try:
            connection_string = self.config.get_connection_string()
            pool_config = self.config.config.get("connection_pool", {})
            
            # إنشاء المحرك
            self.engine = create_engine(
                connection_string,
                pool_size=pool_config.get("pool_size", 10),
                max_overflow=pool_config.get("max_overflow", 20),
                pool_timeout=pool_config.get("pool_timeout", 30),
                pool_recycle=pool_config.get("pool_recycle", 3600),
                echo=False  # تعيين True لرؤية استعلامات SQL
            )
            
            # إنشاء جلسة
            self.Session = sessionmaker(bind=self.engine)
            
            logger.info(f"تم إعداد الاتصال بقاعدة البيانات: {self.config.config['database_type']}")
            
        except Exception as e:
            logger.error(f"فشل في إعداد الاتصال بقاعدة البيانات: {e}")
            raise
    
    def test_connection(self) -> bool:
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            with self.engine.connect() as connection:
                connection.execute("SELECT 1")
            logger.info("اختبار الاتصال بقاعدة البيانات نجح")
            return True
        except Exception as e:
            logger.error(f"فشل اختبار الاتصال بقاعدة البيانات: {e}")
            return False
    
    def create_tables(self):
        """إنشاء الجداول"""
        try:
            # إنشاء جدول الطلاب
            students_table = Table(
                'students', self.metadata,
                Column('id', Integer, primary_key=True),
                Column('student_code', String(20), unique=True, nullable=False),
                Column('full_name', String(100), nullable=False),
                Column('gender', String(10), nullable=False),
                Column('stage', String(20), nullable=False),
                Column('grade', String(20), nullable=False),
                Column('phone', String(20)),
                Column('guardian_phone', String(20)),
                Column('created_at', DateTime, default=datetime.now),
                Column('updated_at', DateTime, default=datetime.now, onupdate=datetime.now),
                Column('is_active', Boolean, default=True)
            )
            
            # إنشاء جدول الحضور
            attendance_table = Table(
                'attendance', self.metadata,
                Column('id', Integer, primary_key=True),
                Column('student_id', Integer, nullable=False),
                Column('date', DateTime, nullable=False),
                Column('status', String(20), nullable=False),
                Column('notes', Text),
                Column('created_at', DateTime, default=datetime.now),
                Column('created_by', String(50))
            )
            
            # إنشاء جدول الدرجات
            grades_table = Table(
                'grades', self.metadata,
                Column('id', Integer, primary_key=True),
                Column('student_id', Integer, nullable=False),
                Column('subject', String(50), nullable=False),
                Column('exam_type', String(50), nullable=False),
                Column('score', Float, nullable=False),
                Column('max_score', Float, nullable=False),
                Column('exam_date', DateTime, nullable=False),
                Column('notes', Text),
                Column('created_at', DateTime, default=datetime.now),
                Column('created_by', String(50))
            )
            
            # إنشاء جدول المستخدمين
            users_table = Table(
                'users', self.metadata,
                Column('id', Integer, primary_key=True),
                Column('username', String(50), unique=True, nullable=False),
                Column('password_hash', String(255), nullable=False),
                Column('full_name', String(100), nullable=False),
                Column('email', String(100)),
                Column('role', String(20), default='teacher'),
                Column('is_active', Boolean, default=True),
                Column('created_at', DateTime, default=datetime.now),
                Column('last_login', DateTime)
            )
            
            # إنشاء جدول الإعدادات
            settings_table = Table(
                'settings', self.metadata,
                Column('id', Integer, primary_key=True),
                Column('key', String(100), unique=True, nullable=False),
                Column('value', Text),
                Column('description', Text),
                Column('updated_at', DateTime, default=datetime.now, onupdate=datetime.now)
            )
            
            # إنشاء جدول سجل النشاطات
            activity_log_table = Table(
                'activity_log', self.metadata,
                Column('id', Integer, primary_key=True),
                Column('user_id', Integer),
                Column('action', String(100), nullable=False),
                Column('table_name', String(50)),
                Column('record_id', Integer),
                Column('old_values', Text),
                Column('new_values', Text),
                Column('ip_address', String(45)),
                Column('user_agent', Text),
                Column('created_at', DateTime, default=datetime.now)
            )
            
            # إنشاء الجداول
            self.metadata.create_all(self.engine)
            logger.info("تم إنشاء جميع الجداول بنجاح")
            
        except Exception as e:
            logger.error(f"فشل في إنشاء الجداول: {e}")
            raise
    
    def migrate_from_sqlite(self, sqlite_db_path: str):
        """ترحيل البيانات من SQLite"""
        try:
            logger.info(f"بدء ترحيل البيانات من SQLite: {sqlite_db_path}")
            
            if not os.path.exists(sqlite_db_path):
                logger.warning(f"ملف SQLite غير موجود: {sqlite_db_path}")
                return
            
            # الاتصال بقاعدة SQLite
            sqlite_conn = sqlite3.connect(sqlite_db_path)
            sqlite_conn.row_factory = sqlite3.Row
            
            session = self.Session()
            
            try:
                # ترحيل الطلاب
                cursor = sqlite_conn.execute("SELECT * FROM students")
                students = cursor.fetchall()
                
                for student in students:
                    session.execute(
                        self.metadata.tables['students'].insert().values(
                            student_code=student['student_code'],
                            full_name=student['full_name'],
                            gender=student['gender'],
                            stage=student['stage'],
                            grade=student['grade'],
                            phone=student.get('phone', ''),
                            guardian_phone=student.get('guardian_phone', ''),
                            created_at=datetime.now()
                        )
                    )
                
                # ترحيل الحضور
                cursor = sqlite_conn.execute("SELECT * FROM attendance")
                attendance_records = cursor.fetchall()
                
                for record in attendance_records:
                    session.execute(
                        self.metadata.tables['attendance'].insert().values(
                            student_id=record['student_id'],
                            date=datetime.fromisoformat(record['date']),
                            status=record['status'],
                            notes=record.get('notes', ''),
                            created_at=datetime.now()
                        )
                    )
                
                # ترحيل الدرجات
                cursor = sqlite_conn.execute("SELECT * FROM grades")
                grades_records = cursor.fetchall()
                
                for record in grades_records:
                    session.execute(
                        self.metadata.tables['grades'].insert().values(
                            student_id=record['student_id'],
                            subject=record['subject'],
                            exam_type=record['exam_type'],
                            score=record['score'],
                            max_score=record['max_score'],
                            exam_date=datetime.fromisoformat(record['exam_date']),
                            notes=record.get('notes', ''),
                            created_at=datetime.now()
                        )
                    )
                
                session.commit()
                logger.info("تم ترحيل البيانات بنجاح")
                
            except Exception as e:
                session.rollback()
                logger.error(f"فشل في ترحيل البيانات: {e}")
                raise
            finally:
                session.close()
                sqlite_conn.close()
                
        except Exception as e:
            logger.error(f"خطأ في ترحيل البيانات: {e}")
            raise
    
    def get_session(self):
        """الحصول على جلسة قاعدة البيانات"""
        return self.Session()
    
    def execute_query(self, query: str, params: Dict = None) -> List[Dict]:
        """تنفيذ استعلام مخصص"""
        try:
            with self.engine.connect() as connection:
                result = connection.execute(query, params or {})
                return [dict(row) for row in result]
        except Exception as e:
            logger.error(f"فشل في تنفيذ الاستعلام: {e}")
            raise
    
    def backup_database(self, backup_path: str):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            logger.info(f"إنشاء نسخة احتياطية: {backup_path}")
            
            # هنا يمكن إضافة منطق النسخ الاحتياطي حسب نوع قاعدة البيانات
            if self.config.config["database_type"] == "sqlite":
                import shutil
                source_path = self.config.config["sqlite"]["database_path"]
                shutil.copy2(source_path, backup_path)
            else:
                # للقواعد الأخرى، يمكن استخدام أدوات النسخ الاحتياطي المخصصة
                logger.warning("النسخ الاحتياطي للقواعد المتقدمة يتطلب أدوات خاصة")
            
            logger.info("تم إنشاء النسخة الاحتياطية بنجاح")
            
        except Exception as e:
            logger.error(f"فشل في إنشاء النسخة الاحتياطية: {e}")
            raise
    
    def get_database_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            stats = {}
            
            with self.engine.connect() as connection:
                # عدد الطلاب
                result = connection.execute("SELECT COUNT(*) as count FROM students WHERE is_active = true")
                stats['total_students'] = result.fetchone()['count']
                
                # عدد سجلات الحضور
                result = connection.execute("SELECT COUNT(*) as count FROM attendance")
                stats['total_attendance_records'] = result.fetchone()['count']
                
                # عدد الدرجات
                result = connection.execute("SELECT COUNT(*) as count FROM grades")
                stats['total_grades'] = result.fetchone()['count']
                
                # حجم قاعدة البيانات (للـ SQLite فقط)
                if self.config.config["database_type"] == "sqlite":
                    db_path = self.config.config["sqlite"]["database_path"]
                    if os.path.exists(db_path):
                        stats['database_size_mb'] = round(os.path.getsize(db_path) / (1024 * 1024), 2)
            
            return stats
            
        except Exception as e:
            logger.error(f"فشل في الحصول على إحصائيات قاعدة البيانات: {e}")
            return {}
    
    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        try:
            if self.engine:
                self.engine.dispose()
                logger.info("تم إغلاق الاتصال بقاعدة البيانات")
        except Exception as e:
            logger.error(f"خطأ في إغلاق الاتصال: {e}")

# دالة مساعدة لإنشاء مدير قاعدة البيانات
def create_advanced_database_manager(config_file="database_config.json"):
    """إنشاء مدير قاعدة بيانات متقدم"""
    return AdvancedDatabaseManager(config_file)
