# -*- coding: utf-8 -*-
"""
نافذة إدارة الامتحانات المحسنة
Enhanced Exams Management Window

عرض طلاب المجموعة مع إمكانية التعديل المباشر والترتيب بالأكواد
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QComboBox, QTableWidget, QTableWidgetItem,
                            QMessageBox, QHeaderView, QGroupBox, QFormLayout,
                            QLineEdit, QDoubleSpinBox, QDateEdit, QTextEdit,
                            QTabWidget, QFrame, QSpinBox, QCheckBox)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from datetime import datetime, date

from ..models.student import Student
from ..models.grades import Grades
from ..models.groups import Groups
from ..database.database_manager import DatabaseManager

class ExamsManagementWindow(QWidget):
    """نافذة إدارة الامتحانات المحسنة"""
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
        self.student_model = Student(db_manager)
        self.grades_model = Grades(db_manager)
        self.groups_model = Groups(db_manager)
        
        self.current_group = None
        self.current_exam_info = {}
        
        self.init_ui()
        self.apply_styles()
        self.load_groups()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة الامتحانات المحسنة")
        self.setGeometry(100, 100, 1400, 900)
        
        layout = QVBoxLayout()
        
        # عنوان النافذة
        title_label = QLabel("📝 إدارة الامتحانات المحسنة")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("titleLabel")
        layout.addWidget(title_label)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب إنشاء امتحان جديد
        new_exam_tab = self.create_new_exam_tab()
        self.tabs.addTab(new_exam_tab, "📝 امتحان جديد")
        
        # تبويب إدارة الدرجات
        manage_grades_tab = self.create_manage_grades_tab()
        self.tabs.addTab(manage_grades_tab, "📊 إدارة الدرجات")
        
        layout.addWidget(self.tabs)
        self.setLayout(layout)
    
    def create_new_exam_tab(self):
        """إنشاء تبويب امتحان جديد"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # معلومات الامتحان
        exam_info_group = QGroupBox("معلومات الامتحان")
        exam_info_layout = QFormLayout()
        
        # اختيار المجموعة
        self.group_combo = QComboBox()
        self.group_combo.addItem("اختر مجموعة", None)
        self.group_combo.currentTextChanged.connect(self.load_group_students)
        exam_info_layout.addRow("المجموعة:", self.group_combo)
        
        # نوع الامتحان
        self.exam_type_input = QLineEdit()
        self.exam_type_input.setPlaceholderText("مثال: امتحان شهري، واجب، مشاركة")
        exam_info_layout.addRow("نوع الامتحان:", self.exam_type_input)
        
        # المادة
        self.subject_input = QLineEdit()
        self.subject_input.setPlaceholderText("مثال: رياضيات، علوم، لغة عربية")
        exam_info_layout.addRow("المادة:", self.subject_input)
        
        # الدرجة الكاملة
        self.max_score_input = QDoubleSpinBox()
        self.max_score_input.setRange(1, 1000)
        self.max_score_input.setValue(100)
        self.max_score_input.setSuffix(" درجة")
        exam_info_layout.addRow("الدرجة الكاملة:", self.max_score_input)
        
        # تاريخ الامتحان
        self.exam_date_input = QDateEdit()
        self.exam_date_input.setDate(QDate.currentDate())
        exam_info_layout.addRow("تاريخ الامتحان:", self.exam_date_input)
        
        # ملاحظات
        self.exam_notes_input = QTextEdit()
        self.exam_notes_input.setMaximumHeight(80)
        self.exam_notes_input.setPlaceholderText("ملاحظات إضافية عن الامتحان...")
        exam_info_layout.addRow("ملاحظات:", self.exam_notes_input)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.create_exam_btn = QPushButton("📝 إنشاء الامتحان")
        self.create_exam_btn.clicked.connect(self.create_new_exam)
        
        self.clear_form_btn = QPushButton("🗑️ مسح النموذج")
        self.clear_form_btn.clicked.connect(self.clear_exam_form)
        
        buttons_layout.addWidget(self.create_exam_btn)
        buttons_layout.addWidget(self.clear_form_btn)
        buttons_layout.addStretch()
        
        exam_info_layout.addRow("", buttons_layout)
        exam_info_group.setLayout(exam_info_layout)
        layout.addWidget(exam_info_group)
        
        # جدول الطلاب
        students_group = QGroupBox("طلاب المجموعة")
        students_layout = QVBoxLayout()
        
        # معلومات المجموعة
        self.group_info_label = QLabel("اختر مجموعة لعرض طلابها")
        self.group_info_label.setObjectName("groupInfo")
        students_layout.addWidget(self.group_info_label)
        
        # جدول الطلاب مع الدرجات
        self.students_table = QTableWidget()
        self.students_table.setColumnCount(7)
        self.students_table.setHorizontalHeaderLabels([
            "الكود", "الاسم", "الصف", "الدرجة", "حالة الحضور", "ملاحظات", "إجراءات"
        ])
        
        # إعدادات الجدول
        header = self.students_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.students_table.setAlternatingRowColors(True)
        
        students_layout.addWidget(self.students_table)
        students_group.setLayout(students_layout)
        layout.addWidget(students_group)
        
        tab.setLayout(layout)
        return tab
    
    def create_manage_grades_tab(self):
        """إنشاء تبويب إدارة الدرجات"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # فلاتر البحث
        filters_group = QGroupBox("فلاتر البحث والعرض")
        filters_layout = QFormLayout()
        
        # فلتر المجموعة
        self.manage_group_combo = QComboBox()
        self.manage_group_combo.addItem("جميع المجموعات")
        self.manage_group_combo.currentTextChanged.connect(self.filter_grades_by_group)
        filters_layout.addRow("المجموعة:", self.manage_group_combo)
        
        # فلتر نوع الامتحان
        self.manage_exam_type_combo = QComboBox()
        self.manage_exam_type_combo.addItem("جميع الامتحانات")
        self.populate_exam_types()
        self.manage_exam_type_combo.currentTextChanged.connect(self.filter_grades_by_exam_type)
        filters_layout.addRow("نوع الامتحان:", self.manage_exam_type_combo)
        
        # البحث بالكود
        search_layout = QHBoxLayout()
        self.search_code_input = QLineEdit()
        self.search_code_input.setPlaceholderText("ابحث بكود الطالب...")
        search_btn = QPushButton("🔍 بحث")
        search_btn.clicked.connect(self.search_by_student_code)
        search_layout.addWidget(self.search_code_input)
        search_layout.addWidget(search_btn)
        filters_layout.addRow("البحث بالكود:", search_layout)
        
        # أزرار التحكم
        control_buttons_layout = QHBoxLayout()
        
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.load_all_grades)
        
        export_btn = QPushButton("📤 تصدير Excel")
        export_btn.clicked.connect(self.export_grades_to_excel)
        
        control_buttons_layout.addWidget(refresh_btn)
        control_buttons_layout.addWidget(export_btn)
        control_buttons_layout.addStretch()
        
        filters_layout.addRow("", control_buttons_layout)
        filters_group.setLayout(filters_layout)
        layout.addWidget(filters_group)
        
        # جدول الدرجات
        grades_group = QGroupBox("جدول الدرجات")
        grades_layout = QVBoxLayout()
        
        self.grades_table = QTableWidget()
        self.grades_table.setColumnCount(9)
        self.grades_table.setHorizontalHeaderLabels([
            "الكود", "الاسم", "المجموعة", "المادة", "نوع الامتحان", 
            "الدرجة", "من", "النسبة", "التاريخ"
        ])
        
        # إعدادات الجدول
        header = self.grades_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.grades_table.setAlternatingRowColors(True)
        self.grades_table.setSortingEnabled(True)
        
        # إمكانية التعديل المباشر
        self.grades_table.itemChanged.connect(self.on_grade_item_changed)
        
        grades_layout.addWidget(self.grades_table)
        grades_group.setLayout(grades_layout)
        layout.addWidget(grades_group)

        tab.setLayout(layout)
        return tab

    def load_groups(self):
        """تحميل قائمة المجموعات"""
        try:
            groups = self.groups_model.get_all_groups()

            # تحديث قائمة المجموعات في تبويب الامتحان الجديد
            self.group_combo.clear()
            self.group_combo.addItem("اختر مجموعة", None)
            for group in groups:
                self.group_combo.addItem(f"{group['group_name']} ({group['student_count']} طالب)", group['group_name'])

            # تحديث قائمة المجموعات في تبويب إدارة الدرجات
            self.manage_group_combo.clear()
            self.manage_group_combo.addItem("جميع المجموعات")
            for group in groups:
                self.manage_group_combo.addItem(group['group_name'])

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المجموعات: {str(e)}")

    def load_group_students(self):
        """تحميل طلاب المجموعة المحددة"""
        group_name = self.group_combo.currentData()
        if not group_name:
            self.students_table.setRowCount(0)
            self.group_info_label.setText("اختر مجموعة لعرض طلابها")
            return

        try:
            students = self.groups_model.get_students_in_group(group_name)

            # ترتيب الطلاب حسب الكود
            students.sort(key=lambda x: int(x['student_code']) if x['student_code'].isdigit() else 0)

            self.students_table.setRowCount(len(students))
            self.group_info_label.setText(f"المجموعة: {group_name} - عدد الطلاب: {len(students)}")

            for row, student in enumerate(students):
                # الكود
                self.students_table.setItem(row, 0, QTableWidgetItem(student['student_code']))

                # الاسم
                self.students_table.setItem(row, 1, QTableWidgetItem(student['full_name']))

                # الصف
                self.students_table.setItem(row, 2, QTableWidgetItem(student['grade']))

                # حقل الدرجة (قابل للتعديل)
                score_item = QTableWidgetItem("0")
                score_item.setData(Qt.UserRole, student['id'])  # حفظ معرف الطالب
                self.students_table.setItem(row, 3, score_item)

                # حالة الحضور
                attendance_combo = QComboBox()
                attendance_combo.addItems(["حاضر", "غائب", "متأخر"])
                self.students_table.setCellWidget(row, 4, attendance_combo)

                # ملاحظات
                notes_item = QTableWidgetItem("")
                self.students_table.setItem(row, 5, notes_item)

                # زر حفظ
                save_btn = QPushButton("💾 حفظ")
                save_btn.clicked.connect(lambda checked, r=row: self.save_student_grade(r))
                self.students_table.setCellWidget(row, 6, save_btn)

            self.current_group = group_name

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل طلاب المجموعة: {str(e)}")

    def create_new_exam(self):
        """إنشاء امتحان جديد"""
        if not self.validate_exam_form():
            return

        try:
            # جمع معلومات الامتحان
            self.current_exam_info = {
                'group_name': self.group_combo.currentData(),
                'exam_type': self.exam_type_input.text().strip(),
                'subject': self.subject_input.text().strip(),
                'max_score': self.max_score_input.value(),
                'exam_date': self.exam_date_input.date().toPyDate(),
                'notes': self.exam_notes_input.toPlainText().strip()
            }

            QMessageBox.information(self, "تم", "تم إنشاء الامتحان بنجاح. يمكنك الآن إدخال درجات الطلاب.")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء الامتحان: {str(e)}")

    def validate_exam_form(self):
        """التحقق من صحة نموذج الامتحان"""
        if not self.group_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مجموعة")
            return False

        if not self.exam_type_input.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال نوع الامتحان")
            return False

        if not self.subject_input.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال المادة")
            return False

        return True

    def save_student_grade(self, row):
        """حفظ درجة طالب واحد"""
        if not self.current_exam_info:
            QMessageBox.warning(self, "تحذير", "يرجى إنشاء الامتحان أولاً")
            return

        try:
            # جلب بيانات الطالب
            student_id = self.students_table.item(row, 3).data(Qt.UserRole)
            score_text = self.students_table.item(row, 3).text()

            # التحقق من صحة الدرجة
            try:
                score = float(score_text)
                if score < 0 or score > self.current_exam_info['max_score']:
                    QMessageBox.warning(self, "تحذير",
                                      f"الدرجة يجب أن تكون بين 0 و {self.current_exam_info['max_score']}")
                    return
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال درجة صحيحة")
                return

            # حفظ الدرجة
            grade_id = self.grades_model.add_grade(
                student_id=student_id,
                subject=self.current_exam_info['subject'],
                exam_type=self.current_exam_info['exam_type'],
                score=score,
                max_score=self.current_exam_info['max_score'],
                exam_date=self.current_exam_info['exam_date'],
                notes=self.current_exam_info['notes']
            )

            if grade_id:
                # تغيير لون الصف للإشارة إلى الحفظ
                for col in range(self.students_table.columnCount()):
                    item = self.students_table.item(row, col)
                    if item:
                        item.setBackground(QColor(200, 255, 200))  # أخضر فاتح

                QMessageBox.information(self, "تم", "تم حفظ الدرجة بنجاح")
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ الدرجة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الدرجة: {str(e)}")

    def clear_exam_form(self):
        """مسح نموذج الامتحان"""
        self.group_combo.setCurrentIndex(0)
        self.exam_type_input.clear()
        self.subject_input.clear()
        self.max_score_input.setValue(100)
        self.exam_date_input.setDate(QDate.currentDate())
        self.exam_notes_input.clear()
        self.students_table.setRowCount(0)
        self.group_info_label.setText("اختر مجموعة لعرض طلابها")
        self.current_exam_info = {}

    def populate_exam_types(self):
        """تعبئة قائمة أنواع الامتحانات"""
        try:
            exam_types = self.grades_model.get_exam_types()
            self.manage_exam_type_combo.clear()
            self.manage_exam_type_combo.addItem("جميع الامتحانات")
            for exam_type in exam_types:
                self.manage_exam_type_combo.addItem(exam_type)
        except Exception as e:
            print(f"خطأ في تحميل أنواع الامتحانات: {e}")

    def load_all_grades(self):
        """تحميل جميع الدرجات"""
        try:
            grades = self.grades_model.get_all_grades_with_student_info()

            # ترتيب الدرجات حسب كود الطالب
            grades.sort(key=lambda x: int(x['student_code']) if x['student_code'].isdigit() else 0)

            self.grades_table.setRowCount(len(grades))

            for row, grade in enumerate(grades):
                self.grades_table.setItem(row, 0, QTableWidgetItem(grade['student_code']))
                self.grades_table.setItem(row, 1, QTableWidgetItem(grade['full_name']))
                self.grades_table.setItem(row, 2, QTableWidgetItem(grade.get('group_name', 'لا توجد مجموعة')))
                self.grades_table.setItem(row, 3, QTableWidgetItem(grade['subject']))
                self.grades_table.setItem(row, 4, QTableWidgetItem(grade['exam_type']))

                # الدرجة (قابلة للتعديل)
                score_item = QTableWidgetItem(f"{grade['score']:.1f}")
                score_item.setData(Qt.UserRole, grade['id'])  # حفظ معرف الدرجة
                self.grades_table.setItem(row, 5, score_item)

                self.grades_table.setItem(row, 6, QTableWidgetItem(f"{grade['max_score']:.1f}"))

                # النسبة
                percentage = (grade['score'] / grade['max_score']) * 100
                self.grades_table.setItem(row, 7, QTableWidgetItem(f"{percentage:.1f}%"))

                # التاريخ
                exam_date = grade.get('exam_date', '')
                self.grades_table.setItem(row, 8, QTableWidgetItem(str(exam_date)))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الدرجات: {str(e)}")

    def filter_grades_by_group(self):
        """فلترة الدرجات حسب المجموعة"""
        selected_group = self.manage_group_combo.currentText()

        for row in range(self.grades_table.rowCount()):
            show_row = True

            if selected_group != "جميع المجموعات":
                group_item = self.grades_table.item(row, 2)  # عمود المجموعة
                if group_item and group_item.text() != selected_group:
                    show_row = False

            self.grades_table.setRowHidden(row, not show_row)

    def filter_grades_by_exam_type(self):
        """فلترة الدرجات حسب نوع الامتحان"""
        selected_exam_type = self.manage_exam_type_combo.currentText()

        for row in range(self.grades_table.rowCount()):
            show_row = True

            if selected_exam_type != "جميع الامتحانات":
                exam_type_item = self.grades_table.item(row, 4)  # عمود نوع الامتحان
                if exam_type_item and exam_type_item.text() != selected_exam_type:
                    show_row = False

            self.grades_table.setRowHidden(row, not show_row)

    def search_by_student_code(self):
        """البحث بكود الطالب"""
        search_code = self.search_code_input.text().strip()

        if not search_code:
            # إظهار جميع الصفوف
            for row in range(self.grades_table.rowCount()):
                self.grades_table.setRowHidden(row, False)
            return

        found = False
        for row in range(self.grades_table.rowCount()):
            code_item = self.grades_table.item(row, 0)  # عمود الكود
            if code_item and search_code in code_item.text():
                self.grades_table.setRowHidden(row, False)
                found = True
            else:
                self.grades_table.setRowHidden(row, True)

        if not found:
            QMessageBox.information(self, "البحث", f"لم يتم العثور على طالب بالكود: {search_code}")

    def on_grade_item_changed(self, item):
        """معالجة تغيير درجة في الجدول"""
        if item.column() == 5:  # عمود الدرجة
            try:
                new_score = float(item.text())
                grade_id = item.data(Qt.UserRole)

                # تحديث الدرجة في قاعدة البيانات
                success = self.grades_model.update_grade(grade_id, {'score': new_score})

                if success:
                    # تحديث النسبة
                    max_score_item = self.grades_table.item(item.row(), 6)
                    if max_score_item:
                        max_score = float(max_score_item.text())
                        percentage = (new_score / max_score) * 100
                        percentage_item = self.grades_table.item(item.row(), 7)
                        if percentage_item:
                            percentage_item.setText(f"{percentage:.1f}%")

                    # تغيير لون الخلية للإشارة إلى التحديث
                    item.setBackground(QColor(255, 255, 200))  # أصفر فاتح
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في تحديث الدرجة")

            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال درجة صحيحة")
                item.setText("0")  # إعادة تعيين القيمة

    def export_grades_to_excel(self):
        """تصدير الدرجات إلى Excel"""
        try:
            from openpyxl import Workbook

            wb = Workbook()
            ws = wb.active
            ws.title = "درجات الامتحانات"

            # العناوين
            headers = ["الكود", "الاسم", "المجموعة", "المادة", "نوع الامتحان", "الدرجة", "من", "النسبة", "التاريخ"]
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # البيانات
            for row in range(self.grades_table.rowCount()):
                if not self.grades_table.isRowHidden(row):  # فقط الصفوف المرئية
                    for col in range(self.grades_table.columnCount()):
                        item = self.grades_table.item(row, col)
                        if item:
                            ws.cell(row=row+2, column=col+1, value=item.text())

            # حفظ الملف
            filename = f"درجات_الامتحانات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            wb.save(filename)

            QMessageBox.information(self, "تم", f"تم تصدير الدرجات إلى: {filename}")

        except ImportError:
            QMessageBox.warning(self, "تحذير", "يرجى تثبيت مكتبة openpyxl لتصدير Excel")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير الدرجات: {str(e)}")

    def apply_styles(self):
        """تطبيق الأنماط"""
        style = """
        QWidget {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            font-size: 12px;
            background-color: #f8f9fa;
        }

        #titleLabel {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        #groupInfo {
            font-size: 14px;
            font-weight: bold;
            color: #34495e;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 4px;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
            background-color: white;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #2c3e50;
        }

        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: bold;
        }

        QPushButton:hover {
            background-color: #2980b9;
        }

        QPushButton:pressed {
            background-color: #21618c;
        }

        QTableWidget {
            background-color: white;
            border: 1px solid #bdc3c7;
            border-radius: 6px;
            gridline-color: #ecf0f1;
        }

        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #ecf0f1;
        }

        QTableWidget::item:selected {
            background-color: #3498db;
            color: white;
        }

        QHeaderView::section {
            background-color: #34495e;
            color: white;
            padding: 10px;
            border: none;
            font-weight: bold;
        }

        QComboBox, QLineEdit, QDoubleSpinBox, QSpinBox, QDateEdit {
            padding: 8px;
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            background-color: white;
        }

        QComboBox:focus, QLineEdit:focus, QDoubleSpinBox:focus, QSpinBox:focus, QDateEdit:focus {
            border-color: #3498db;
        }

        QTextEdit {
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            background-color: white;
            padding: 5px;
        }

        QTextEdit:focus {
            border-color: #3498db;
        }
        """

        self.setStyleSheet(style)
