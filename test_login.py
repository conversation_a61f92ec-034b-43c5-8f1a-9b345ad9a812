#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تسجيل الدخول
Login Test Script
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox

# Add src directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from src.utils.auth import AuthManager
from src.ui.login_dialog import LoginDialog

def test_login():
    """اختبار وظيفة تسجيل الدخول"""
    try:
        print("🔍 جاري اختبار نظام تسجيل الدخول...")
        
        # تهيئة مدير قاعدة البيانات
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # تهيئة مدير المصادقة
        auth_manager = AuthManager(db_manager)
        
        # اختبار بيانات الاعتبارات الافتراضية
        print("\n🔑 اختبار بيانات الاعتماد الافتراضية:")
        print("اسم المستخدم: admin")
        print("كلمة المرور: admin123")
        
        # اختبار تسجيل الدخول مباشرة
        success, message = auth_manager.login("admin", "admin123")
        
        if success:
            print("✅ تم تسجيل الدخول بنجاح!")
            print(f"مرحباً {auth_manager.current_user['full_name']}")
            return True
        else:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
            
    except Exception as e:
        print(f"⚠️ حدث خطأ أثناء الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_login_dialog():
    """اختبار واجهة تسجيل الدخول"""
    try:
        print("\n🖥️ اختبار واجهة تسجيل الدخول...")
        
        app = QApplication(sys.argv)
        
        # تهيئة مدير قاعدة البيانات والمصادقة
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        auth_manager = AuthManager(db_manager)
        
        # إنشاء وعرض نافذة تسجيل الدخول
        login_dialog = LoginDialog(auth_manager)
        login_dialog.setWindowTitle("اختبار تسجيل الدخول")
        
        # ملء حقول تسجيل الدخول تلقائيًا للاختبار
        for child in login_dialog.findChildren(QLineEdit):
            if child.placeholderText() == "اسم المستخدم":
                child.setText("admin")
            elif child.placeholderText() == "كلمة المرور":
                child.setText("admin123")
        
        # عرض رسالة توضيحية
        msg = QMessageBox()
        msg.setWindowTitle("تعليمات الاختبار")
        msg.setText("سيتم الآن اختبار نافذة تسجيل الدخول\n\nبيانات الدخول الافتراضية:\nاسم المستخدم: admin\nكلمة المرور: admin123\n\nانقر على زر تسجيل الدخول للمتابعة")
        msg.setStandardButtons(QMessageBox.Ok)
        msg.exec_()
        
        # تشغيل نافذة تسجيل الدخول
        result = login_dialog.exec_()
        
        if result == LoginDialog.Accepted:
            print("✅ تم تسجيل الدخول بنجاح من خلال الواجهة!")
            return True
        else:
            print("❌ تم إلغاء تسجيل الدخول أو فشل المصادقة")
            return False
            
    except Exception as e:
        print(f"⚠️ حدث خطأ أثناء اختبار واجهة المستخدم: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("اختبار نظام تسجيل الدخول")
    print("=" * 50)
    
    # اختبار الدالة المباشرة أولاً
    if test_login():
        print("\n" + "-" * 50)
        print("✅ اجتاز اختبار الدالة المباشرة بنجاح!")
        
        # إذا نجح الاختبار الأول، قم باختبار الواجهة
        if test_login_dialog():
            print("\n✅ تم اجتياز جميع اختبارات تسجيل الدخول بنجاح!")
        else:
            print("\n❌ فشل اختبار واجهة تسجيل الدخول")
    else:
        print("\n❌ فشل اختبار الدالة المباشرة. الرجاء التحقق من إعدادات قاعدة البيانات.")
    
    input("\nاضغط Enter للخروج...")
