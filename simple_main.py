#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الطلاب المبسط
Simple Student Management System
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, <PERSON>Widget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QLineEdit, 
                            QMessageBox, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QPixmap
import sqlite3
from datetime import datetime

class LoginWindow(QWidget):
    """نافذة تسجيل الدخول"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة تسجيل الدخول"""
        self.setWindowTitle("تسجيل الدخول - نظام إدارة الطلاب")
        self.setFixedSize(400, 300)
        self.setStyleSheet("""
            QWidget {
                background-color: #f0f0f0;
                font-family: 'Segoe UI';
            }
            QLabel {
                color: #2c3e50;
                font-size: 14px;
                font-weight: bold;
            }
            QLineEdit {
                padding: 10px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(20)
        
        # العنوان
        title = QLabel("🎓 نظام إدارة الطلاب")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 24px; color: #2c3e50; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # معلومات المعلم
        teacher_info = QLabel("نظام إدارة الطلاب المتطور\nنظام شامل لإدارة الطلاب")
        teacher_info.setAlignment(Qt.AlignCenter)
        teacher_info.setStyleSheet("font-size: 16px; color: #7f8c8d; margin-bottom: 30px;")
        layout.addWidget(teacher_info)
        
        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        layout.addWidget(self.username_input)
        
        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        layout.addWidget(self.password_input)
        
        # زر تسجيل الدخول
        login_button = QPushButton("🔐 تسجيل الدخول")
        login_button.clicked.connect(self.login)
        layout.addWidget(login_button)
        
        # معلومات تسجيل الدخول
        info_label = QLabel("اسم المستخدم: admin\nكلمة المرور: admin123")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("font-size: 12px; color: #95a5a6; margin-top: 20px;")
        layout.addWidget(info_label)
        
        self.setLayout(layout)
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.desktop().screenGeometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        if username == "admin" and password == "admin123":
            self.hide()
            self.main_window = MainWindow()
            self.main_window.show()
        else:
            QMessageBox.warning(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة!")

class MainWindow(QMainWindow):
    """النافذة الرئيسية"""
    
    def __init__(self):
        super().__init__()
        self.init_database()
        self.init_ui()
    
    def init_database(self):
        """إنشاء قاعدة البيانات"""
        try:
            os.makedirs('data', exist_ok=True)
            self.conn = sqlite3.connect('data/students.db')
            self.cursor = self.conn.cursor()
            
            # إنشاء جدول الطلاب
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS students (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    stage TEXT NOT NULL,
                    grade TEXT NOT NULL,
                    phone TEXT,
                    parent_phone TEXT,
                    group_name TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            self.conn.commit()
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في قاعدة البيانات: {e}")
    
    def init_ui(self):
        """إعداد الواجهة الرئيسية"""
        self.setWindowTitle("نظام إدارة الطلاب - نظام إدارة الطلاب المتطور")
        self.setGeometry(100, 100, 1000, 700)
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ecf0f1;
            }
            QLabel {
                color: #2c3e50;
                font-weight: bold;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout()
        
        # العنوان
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background-color: #2c3e50;
                border-radius: 10px;
                margin: 10px;
            }
            QLabel {
                color: white;
                padding: 20px;
            }
        """)
        header_layout = QVBoxLayout()
        
        title = QLabel("🎓 نظام إدارة الطلاب")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 28px; font-weight: bold;")
        header_layout.addWidget(title)
        
        subtitle = QLabel("نظام إدارة الطلاب المتطور - نظام شامل لإدارة الطلاب")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("font-size: 16px;")
        header_layout.addWidget(subtitle)
        
        header.setLayout(header_layout)
        layout.addWidget(header)
        
        # الإحصائيات
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 10px;
                margin: 10px;
                padding: 20px;
            }
        """)
        stats_layout = QHBoxLayout()
        
        # عدد الطلاب
        students_count = self.get_students_count()
        stats_label = QLabel(f"📊 إجمالي الطلاب: {students_count}")
        stats_label.setStyleSheet("font-size: 18px; color: #27ae60;")
        stats_layout.addWidget(stats_label)
        
        # التاريخ
        today = datetime.now().strftime("%Y-%m-%d")
        date_label = QLabel(f"📅 التاريخ: {today}")
        date_label.setStyleSheet("font-size: 18px; color: #e74c3c;")
        stats_layout.addWidget(date_label)
        
        stats_frame.setLayout(stats_layout)
        layout.addWidget(stats_frame)
        
        # الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 10px;
                margin: 10px;
                padding: 20px;
            }
        """)
        buttons_layout = QVBoxLayout()
        
        # صف الأزرار الأول
        row1 = QHBoxLayout()
        
        students_btn = QPushButton("👥 إدارة الطلاب")
        students_btn.clicked.connect(self.show_students_info)
        row1.addWidget(students_btn)
        
        attendance_btn = QPushButton("📋 تسجيل الحضور")
        attendance_btn.clicked.connect(self.show_attendance_info)
        row1.addWidget(attendance_btn)
        
        grades_btn = QPushButton("📊 إدارة الدرجات")
        grades_btn.clicked.connect(self.show_grades_info)
        row1.addWidget(grades_btn)
        
        buttons_layout.addLayout(row1)
        
        # صف الأزرار الثاني
        row2 = QHBoxLayout()
        
        reports_btn = QPushButton("📈 التقارير")
        reports_btn.clicked.connect(self.show_reports_info)
        row2.addWidget(reports_btn)
        
        settings_btn = QPushButton("⚙️ الإعدادات")
        settings_btn.clicked.connect(self.show_settings_info)
        row2.addWidget(settings_btn)
        
        about_btn = QPushButton("ℹ️ حول التطبيق")
        about_btn.clicked.connect(self.show_about)
        row2.addWidget(about_btn)
        
        buttons_layout.addLayout(row2)
        
        buttons_frame.setLayout(buttons_layout)
        layout.addWidget(buttons_frame)
        
        # معلومات المطور
        footer = QLabel("🎓 تم التطوير بواسطة: م/ حسام أسامة - مهندس برمجيات")
        footer.setAlignment(Qt.AlignCenter)
        footer.setStyleSheet("font-size: 12px; color: #7f8c8d; margin: 20px;")
        layout.addWidget(footer)
        
        central_widget.setLayout(layout)
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.desktop().screenGeometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    def get_students_count(self):
        """الحصول على عدد الطلاب"""
        try:
            self.cursor.execute("SELECT COUNT(*) FROM students")
            count = self.cursor.fetchone()[0]
            return count
        except:
            return 0
    
    def show_students_info(self):
        """عرض معلومات إدارة الطلاب"""
        QMessageBox.information(self, "إدارة الطلاب", 
                               "🎯 مميزات إدارة الطلاب:\n\n"
                               "• إضافة طلاب جدد\n"
                               "• تعديل بيانات الطلاب\n"
                               "• حذف الطلاب\n"
                               "• البحث والفلترة\n"
                               "• تنظيم المجموعات\n\n"
                               "سيتم تفعيل هذه الميزة في التحديث القادم")
    
    def show_attendance_info(self):
        """عرض معلومات تسجيل الحضور"""
        QMessageBox.information(self, "تسجيل الحضور", 
                               "📋 مميزات تسجيل الحضور:\n\n"
                               "• تسجيل حضور يومي\n"
                               "• رسائل تلقائية لأولياء الأمور\n"
                               "• إحصائيات الحضور\n"
                               "• تقارير شهرية\n"
                               "• نظام QR Code (قريباً)\n\n"
                               "سيتم تفعيل هذه الميزة في التحديث القادم")
    
    def show_grades_info(self):
        """عرض معلومات إدارة الدرجات"""
        QMessageBox.information(self, "إدارة الدرجات", 
                               "📊 مميزات إدارة الدرجات:\n\n"
                               "• إدخال درجات الامتحانات\n"
                               "• حساب المتوسطات\n"
                               "• ترتيب الطلاب\n"
                               "• إشعارات الدرجات\n"
                               "• تحليل الأداء\n\n"
                               "سيتم تفعيل هذه الميزة في التحديث القادم")
    
    def show_reports_info(self):
        """عرض معلومات التقارير"""
        QMessageBox.information(self, "التقارير", 
                               "📈 مميزات التقارير:\n\n"
                               "• تقارير PDF احترافية\n"
                               "• تقارير Excel\n"
                               "• إحصائيات شاملة\n"
                               "• رسوم بيانية\n"
                               "• تصدير البيانات\n\n"
                               "سيتم تفعيل هذه الميزة في التحديث القادم")
    
    def show_settings_info(self):
        """عرض معلومات الإعدادات"""
        QMessageBox.information(self, "الإعدادات", 
                               "⚙️ مميزات الإعدادات:\n\n"
                               "• إعدادات النظام\n"
                               "• إعدادات الرسائل\n"
                               "• تخصيص الواجهة\n"
                               "• النسخ الاحتياطية\n"
                               "• إدارة المستخدمين\n\n"
                               "سيتم تفعيل هذه الميزة في التحديث القادم")
    
    def show_about(self):
        """عرض معلومات حول التطبيق"""
        QMessageBox.about(self, "حول التطبيق", 
                         "🎓 نظام إدارة الطلاب\n"
                         "الإصدار 1.0\n\n"
                         "👨‍🏫 مصمم خصيصاً لـ:\n"
                         "نظام إدارة الطلاب المتطور\n"
                         "نظام شامل لإدارة الطلاب\n\n"
                         "👨‍💻 تم التطوير بواسطة:\n"
                         "م/ حسام أسامة\n"
                         "مهندس برمجيات - مطور تطبيقات\n\n"
                         "🏆 أول نظام إدارة طلاب عربي متكامل!")

def main():
    """الدالة الرئيسية"""
    try:
        app = QApplication(sys.argv)
        
        # إعداد خصائص التطبيق
        app.setApplicationName("نظام إدارة الطلاب")
        app.setApplicationVersion("1.0")
        app.setOrganizationName("نظام إدارة الطلاب المتطور")
        
        # إعداد الخط العربي
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        
        # إعداد اتجاه النص للعربية
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("🚀 تشغيل نظام إدارة الطلاب...")
        print("✅ تم تحميل النظام بنجاح!")
        
        # إنشاء وعرض نافذة تسجيل الدخول
        login_window = LoginWindow()
        login_window.show()
        
        print("🔐 نافذة تسجيل الدخول جاهزة!")
        print("👤 اسم المستخدم: admin")
        print("🔑 كلمة المرور: admin123")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
