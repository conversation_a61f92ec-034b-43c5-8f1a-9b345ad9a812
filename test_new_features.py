#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار المميزات الجديدة
Test New Features

اختبار إزالة الأسماء وإضافة معلومات المطور وإعدادات المواد
"""

import sys
import os
import json
import unittest
from pathlib import Path

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

class TestNewFeatures(unittest.TestCase):
    """اختبار المميزات الجديدة"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        print(f"\n🧪 اختبار المميزات الجديدة...")
    
    def test_subjects_config_file(self):
        """اختبار ملف إعدادات المواد"""
        print("📚 اختبار ملف إعدادات المواد...")
        
        # فحص وجود الملف
        subjects_file = "subjects_config.json"
        self.assertTrue(os.path.exists(subjects_file), "ملف إعدادات المواد غير موجود")
        
        # فحص محتوى الملف
        with open(subjects_file, 'r', encoding='utf-8') as f:
            subjects_data = json.load(f)
        
        self.assertIn('subjects', subjects_data, "مفتاح 'subjects' غير موجود")
        self.assertIsInstance(subjects_data['subjects'], list, "المواد يجب أن تكون قائمة")
        self.assertGreater(len(subjects_data['subjects']), 0, "يجب أن تحتوي على مواد")
        
        # فحص هيكل المواد
        for subject in subjects_data['subjects']:
            self.assertIn('name', subject, "كل مادة يجب أن تحتوي على اسم")
            self.assertIn('description', subject, "كل مادة يجب أن تحتوي على وصف")
        
        print("✅ ملف إعدادات المواد صحيح")
    
    def test_settings_window_imports(self):
        """اختبار استيرادات نافذة الإعدادات"""
        print("⚙️ اختبار نافذة الإعدادات المحدثة...")
        
        try:
            from src.ui.settings_window import SettingsWindow
            print("✅ تم استيراد نافذة الإعدادات بنجاح")
        except ImportError as e:
            self.fail(f"فشل في استيراد نافذة الإعدادات: {e}")
    
    def test_web_templates_updated(self):
        """اختبار تحديث قوالب الويب"""
        print("🌐 اختبار قوالب الويب المحدثة...")
        
        # فحص قالب base.html
        base_template = "src/web_server/templates/base.html"
        if os.path.exists(base_template):
            with open(base_template, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص عدم وجود الأسماء القديمة
            self.assertNotIn("مستر أحمد عادل", content, "يجب إزالة اسم مستر أحمد عادل")
            
            # فحص وجود النص الجديد
            self.assertIn("نظام إدارة الطلاب المتطور", content, "يجب وجود النص الجديد")
            
            print("✅ قالب base.html محدث بشكل صحيح")
        
        # فحص قالب login.html
        login_template = "src/web_server/templates/login.html"
        if os.path.exists(login_template):
            with open(login_template, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص عدم وجود الأسماء القديمة
            self.assertNotIn("مستر أحمد عادل", content, "يجب إزالة اسم مستر أحمد عادل من صفحة تسجيل الدخول")
            
            print("✅ قالب login.html محدث بشكل صحيح")
    
    def test_main_window_updated(self):
        """اختبار تحديث النافذة الرئيسية"""
        print("🏠 اختبار النافذة الرئيسية المحدثة...")
        
        main_window_file = "src/ui/main_window.py"
        if os.path.exists(main_window_file):
            with open(main_window_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص وجود النص الجديد
            self.assertIn("نظام إدارة الطلاب المتطور", content, "يجب وجود النص الجديد في النافذة الرئيسية")
            
            print("✅ النافذة الرئيسية محدثة بشكل صحيح")
    
    def test_enhanced_main_updated(self):
        """اختبار تحديث الملف الرئيسي المحسن"""
        print("🚀 اختبار الملف الرئيسي المحسن...")
        
        enhanced_main_file = "enhanced_main.py"
        if os.path.exists(enhanced_main_file):
            with open(enhanced_main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص عدم وجود الأسماء القديمة في التعليقات
            lines = content.split('\n')
            for i, line in enumerate(lines[:20]):  # فحص أول 20 سطر
                if "مستر أحمد عادل" in line and not line.strip().startswith('#'):
                    self.fail(f"يجب إزالة اسم مستر أحمد عادل من السطر {i+1}")
            
            print("✅ الملف الرئيسي المحسن محدث بشكل صحيح")
    
    def test_batch_files_updated(self):
        """اختبار تحديث ملفات التشغيل"""
        print("📄 اختبار ملفات التشغيل المحدثة...")
        
        batch_files = [
            "START_ULTIMATE_SYSTEM.bat",
            "RUN_ENHANCED_SYSTEM.bat"
        ]
        
        for batch_file in batch_files:
            if os.path.exists(batch_file):
                with open(batch_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص وجود النص الجديد
                self.assertIn("نظام إدارة الطلاب المتطور", content, 
                            f"يجب وجود النص الجديد في {batch_file}")
                
                print(f"✅ {batch_file} محدث بشكل صحيح")
    
    def test_developer_contact_info(self):
        """اختبار معلومات التواصل مع المطور"""
        print("👨‍💻 اختبار معلومات المطور...")
        
        settings_file = "src/ui/settings_window.py"
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص وجود معلومات المطور
            self.assertIn("Eng / Hossam Osama", content, "يجب وجود اسم المطور")
            self.assertIn("01225396729", content, "يجب وجود رقم الهاتف")
            self.assertIn("H - TECH", content, "يجب وجود اسم الصفحة")
            self.assertIn("facebook.com", content, "يجب وجود رابط فيسبوك")
            self.assertIn("wa.me", content, "يجب وجود رابط واتساب")
            
            print("✅ معلومات المطور موجودة بشكل صحيح")

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("=" * 60)
    print("🧪 اختبار المميزات الجديدة")
    print("=" * 60)
    print()
    print("📝 الاختبارات المطلوبة:")
    print("   • إزالة اسم مستر أحمد عادل من جميع الواجهات")
    print("   • إضافة معلومات المطور في الإعدادات")
    print("   • إضافة إعدادات المواد المتعددة")
    print("   • تحديث النصوص في جميع الملفات")
    print()
    
    # تشغيل الاختبارات
    unittest.main(argv=[''], exit=False, verbosity=2)

def main():
    """الدالة الرئيسية"""
    print("🎯 اختبار المميزات الجديدة - نظام إدارة الطلاب")
    print("الإصدار 2.0.1 - 2025")
    print()
    
    try:
        run_tests()
        print()
        print("🎉 تم الانتهاء من جميع الاختبارات!")
        return 0
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبارات: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
