@echo off
title نظام إدارة المدارس

echo ===================================================
echo    نظام إدارة المدارس - School Management System
echo    الإصدار 1.0.0
echo ===================================================

:: Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: Python غير مثبت أو غير مضاف إلى متغيرات النظام.
    echo الرجاء تثبيت Python 3.8 أو أحدث من الموقع الرسمي.
    pause
    exit /b 1
)

:: Check if virtual environment exists, if not create it
if not exist "venv\" (
    echo جاري إعداد البيئة الافتراضية...
    python -m venv venv
    call venv\Scripts\activate.bat
    pip install --upgrade pip
    pip install -r requirements.txt
) else (
    call venv\Scripts\activate.bat
)

:: Create necessary directories
if not exist "data" mkdir data
if not exist "logs" mkdir logs

:: Run the application
echo.
echo جاري تشغيل النظام...
python main.py

:: If there was an error, show message
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo حدث خطأ أثناء تشغيل التطبيق.
    echo يرجى مراجعة ملف السجل في مجلد logs لمزيد من التفاصيل.
    pause
)

pause
