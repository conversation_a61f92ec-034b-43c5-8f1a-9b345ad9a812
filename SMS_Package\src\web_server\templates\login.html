<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الطلاب</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-header {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .login-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .login-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 0;
        }
        
        .channel-info {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
            text-align: center;
            padding: 15px;
            margin: 20px 0;
            border-radius: 10px;
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .login-form {
            padding: 40px;
        }
        
        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(45deg, #3498db, #2980b9);
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            font-size: 1.2rem;
            font-weight: bold;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
            color: white;
        }
        
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px 0 0 15px;
            border-left: none;
        }
        
        .input-group .form-control {
            border-radius: 0 15px 15px 0;
            border-right: none;
        }
        
        .alert {
            border-radius: 15px;
            border: none;
        }
        
        .features-list {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .features-list h6 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .features-list ul {
            list-style: none;
            padding: 0;
        }
        
        .features-list li {
            padding: 5px 0;
            color: #34495e;
        }
        
        .features-list i {
            color: #27ae60;
            margin-left: 10px;
        }
        
        .developer-info {
            text-align: center;
            margin-top: 20px;
            padding: 15px;
            background: #ecf0f1;
            border-radius: 10px;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="row g-0">
                <!-- Header Section -->
                <div class="col-12">
                    <div class="login-header">
                        <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                        <h1>نظام إدارة الطلاب المتطور</h1>
                        <p>نظام شامل لإدارة الطلاب والدرجات والحضور</p>
                    </div>
                </div>

                <!-- Channel Info -->
                <div class="col-12">
                    <div class="px-4">
                        <div class="channel-info">
                            <i class="fas fa-graduation-cap me-2"></i>
                            نظام إدارة الطلاب المتطور – دروس، امتحانات، ومتابعة مستمرة.
                        </div>
                    </div>
                </div>

                <!-- Login Form -->
                <div class="col-md-6">
                    <div class="login-form">
                        <h3 class="text-center mb-4">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </h3>
                        
                        <!-- Flash Messages -->
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="username" class="form-label">اسم المستخدم</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-user"></i>
                                    </span>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           placeholder="أدخل اسم المستخدم" required>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="password" class="form-label">كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           placeholder="أدخل كلمة المرور" required>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-login">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                دخول
                            </button>
                        </form>
                        
                        <div class="developer-info">
                            <small>
                                <i class="fas fa-code me-1"></i>
                                تم التطوير بواسطة: م/ حسام أسامة
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- Features Section -->
                <div class="col-md-6">
                    <div class="login-form">
                        <div class="features-list">
                            <h6>
                                <i class="fas fa-star me-2"></i>
                                المميزات الجديدة
                            </h6>
                            <ul>
                                <li><i class="fas fa-check"></i>الوصول عن بُعد من أي جهاز</li>
                                <li><i class="fas fa-check"></i>تحديثات مباشرة في الوقت الفعلي</li>
                                <li><i class="fas fa-check"></i>واجهة ويب متجاوبة</li>
                                <li><i class="fas fa-check"></i>دعم متعدد المستخدمين</li>
                                <li><i class="fas fa-check"></i>نسخ احتياطي تلقائي</li>
                                <li><i class="fas fa-check"></i>تقارير محسنة</li>
                            </ul>
                        </div>
                        
                        <div class="features-list">
                            <h6>
                                <i class="fas fa-mobile-alt me-2"></i>
                                متوافق مع جميع الأجهزة
                            </h6>
                            <ul>
                                <li><i class="fas fa-desktop"></i>أجهزة الكمبيوتر</li>
                                <li><i class="fas fa-laptop"></i>أجهزة اللابتوب</li>
                                <li><i class="fas fa-tablet"></i>الأجهزة اللوحية</li>
                                <li><i class="fas fa-mobile-alt"></i>الهواتف الذكية</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> يمكنك الآن الوصول للنظام من أي مكان باستخدام المتصفح!
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تركيز على حقل اسم المستخدم عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });
        
        // إضافة تأثيرات بصرية
        document.querySelectorAll('.form-control').forEach(function(input) {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
