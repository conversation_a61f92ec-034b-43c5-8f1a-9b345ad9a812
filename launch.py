#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل التطبيق النهائي
Final Application Launcher

هذا الملف يوفر طريقة شاملة ومحسنة لتشغيل التطبيق مع جميع الفحوصات والإعدادات
"""

import sys
import os
import traceback
import logging
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

def setup_logging():
    """إعداد نظام السجلات"""
    logs_dir = project_root / 'logs'
    logs_dir.mkdir(exist_ok=True)
    
    log_file = logs_dir / f"app_{datetime.now().strftime('%Y%m%d')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

def check_python_version(logger):
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 6):
        logger.error("Python 3.6+ مطلوب")
        print("❌ خطأ: يتطلب التطبيق Python 3.6 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    logger.info(f"Python version: {sys.version}")
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def check_dependencies(logger):
    """التحقق من المكتبات المطلوبة"""
    required_packages = [
        ('PyQt5', 'PyQt5'),
        ('sqlite3', 'sqlite3'),
        ('pathlib', 'pathlib'),
        ('datetime', 'datetime')
    ]
    
    optional_packages = [
        ('reportlab', 'reportlab'),
        ('openpyxl', 'openpyxl'),
        ('Pillow', 'PIL')
    ]
    
    missing_required = []
    missing_optional = []
    
    # فحص المكتبات المطلوبة
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            logger.info(f"✅ {package_name}: موجود")
            print(f"✅ {package_name}: موجود")
        except ImportError:
            missing_required.append(package_name)
            logger.error(f"❌ {package_name}: غير موجود")
            print(f"❌ {package_name}: غير موجود")
    
    # فحص المكتبات الاختيارية
    for package_name, import_name in optional_packages:
        try:
            __import__(import_name)
            logger.info(f"✅ {package_name}: موجود")
            print(f"✅ {package_name}: موجود")
        except ImportError:
            missing_optional.append(package_name)
            logger.warning(f"⚠️ {package_name}: غير موجود (اختياري)")
            print(f"⚠️ {package_name}: غير موجود (اختياري)")
    
    if missing_required:
        logger.error(f"المكتبات المطلوبة المفقودة: {', '.join(missing_required)}")
        print(f"\n❌ المكتبات المطلوبة المفقودة: {', '.join(missing_required)}")
        print("يرجى تثبيت المكتبات المطلوبة باستخدام:")
        print("pip install -r requirements.txt")
        return False
    
    if missing_optional:
        logger.warning(f"المكتبات الاختيارية المفقودة: {', '.join(missing_optional)}")
        print(f"\nℹ️ المكتبات الاختيارية المفقودة: {', '.join(missing_optional)}")
        print("بعض المميزات قد لا تعمل بشكل كامل")
    
    return True

def setup_environment(logger):
    """إعداد البيئة"""
    try:
        # إنشاء المجلدات المطلوبة
        required_dirs = ['data', 'logs', 'backups', 'exports', 'temp', 'assets']
        for dir_name in required_dirs:
            dir_path = project_root / dir_name
            dir_path.mkdir(exist_ok=True)
            logger.info(f"مجلد {dir_name}: جاهز")
        
        # إعداد متغيرات البيئة
        os.environ['PYTHONPATH'] = str(project_root)
        
        logger.info("تم إعداد البيئة بنجاح")
        print("✅ تم إعداد البيئة بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"فشل في إعداد البيئة: {e}")
        print(f"❌ فشل في إعداد البيئة: {e}")
        return False

def initialize_database(logger):
    """تهيئة قاعدة البيانات"""
    try:
        from src.database.database_manager import DatabaseManager
        
        logger.info("بدء تهيئة قاعدة البيانات")
        print("📊 تهيئة قاعدة البيانات...")
        
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        logger.info("تم تهيئة قاعدة البيانات بنجاح")
        print("✅ تم تهيئة قاعدة البيانات بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"فشل في تهيئة قاعدة البيانات: {e}")
        print(f"❌ فشل في تهيئة قاعدة البيانات: {e}")
        return False

def create_application(logger):
    """إنشاء تطبيق PyQt5"""
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont, QIcon
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد خصائص التطبيق
        app.setApplicationName("نظام إدارة الطلاب")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("نظام إدارة الطلاب المتطور")
        app.setApplicationDisplayName("نظام إدارة الطلاب - نظام إدارة الطلاب المتطور")
        
        # إعداد الأيقونة
        icon_path = project_root / 'assets' / 'icon.ico'
        if icon_path.exists():
            app.setWindowIcon(QIcon(str(icon_path)))
            logger.info("تم تحميل أيقونة التطبيق")
        
        # إعداد الخط العربي
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        
        # إعداد اتجاه النص للعربية
        app.setLayoutDirection(Qt.RightToLeft)
        
        logger.info("تم إنشاء تطبيق PyQt5 بنجاح")
        print("✅ تم إنشاء تطبيق PyQt5 بنجاح")
        return app
        
    except Exception as e:
        logger.error(f"فشل في إنشاء تطبيق PyQt5: {e}")
        print(f"❌ فشل في إنشاء تطبيق PyQt5: {e}")
        return None

def show_login_window(logger):
    """عرض نافذة تسجيل الدخول"""
    try:
        from src.ui.login_window import LoginWindow
        
        logger.info("إنشاء نافذة تسجيل الدخول")
        print("🔐 فتح نافذة تسجيل الدخول...")
        
        login_window = LoginWindow()
        login_window.show()
        
        logger.info("تم عرض نافذة تسجيل الدخول بنجاح")
        print("✅ تم عرض نافذة تسجيل الدخول بنجاح")
        return login_window
        
    except Exception as e:
        logger.error(f"فشل في عرض نافذة تسجيل الدخول: {e}")
        print(f"❌ فشل في عرض نافذة تسجيل الدخول: {e}")
        return None

def show_startup_info():
    """عرض معلومات بدء التشغيل"""
    print("🚀 نظام إدارة الطلاب لنظام إدارة الطلاب المتطور")
    print("=" * 50)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    print(f"💻 النظام: {sys.platform}")
    print(f"📁 المسار: {project_root}")
    print("=" * 50)

def main():
    """الدالة الرئيسية"""
    # عرض معلومات بدء التشغيل
    show_startup_info()
    
    # إعداد نظام السجلات
    logger = setup_logging()
    logger.info("بدء تشغيل التطبيق")
    
    try:
        # التحقق من إصدار Python
        if not check_python_version(logger):
            input("اضغط Enter للخروج...")
            return 1
        
        print("\n📋 التحقق من المتطلبات:")
        # التحقق من المكتبات
        if not check_dependencies(logger):
            input("اضغط Enter للخروج...")
            return 1
        
        print("\n⚙️ إعداد البيئة:")
        # إعداد البيئة
        if not setup_environment(logger):
            input("اضغط Enter للخروج...")
            return 1
        
        print("\n📊 إعداد قاعدة البيانات:")
        # تهيئة قاعدة البيانات
        if not initialize_database(logger):
            input("اضغط Enter للخروج...")
            return 1
        
        print("\n🎨 إنشاء الواجهة:")
        # إنشاء التطبيق
        app = create_application(logger)
        if not app:
            input("اضغط Enter للخروج...")
            return 1
        
        print("\n🔐 تسجيل الدخول:")
        # عرض نافذة تسجيل الدخول
        login_window = show_login_window(logger)
        if not login_window:
            input("اضغط Enter للخروج...")
            return 1
        
        print("\n🎉 تم تشغيل التطبيق بنجاح!")
        print("💡 نصيحة: استخدم اسم المستخدم 'admin' وكلمة المرور 'admin123'")
        print("📖 راجع ملف USER_GUIDE.md للحصول على دليل الاستخدام الكامل")
        
        logger.info("تم تشغيل التطبيق بنجاح")
        
        # تشغيل التطبيق
        exit_code = app.exec_()
        logger.info(f"تم إغلاق التطبيق برمز الخروج: {exit_code}")
        return exit_code
        
    except KeyboardInterrupt:
        logger.info("تم إلغاء التشغيل بواسطة المستخدم")
        print("\n⚠️ تم إلغاء التشغيل")
        return 0
        
    except Exception as e:
        logger.error(f"خطأ غير متوقع: {e}")
        logger.error(traceback.format_exc())
        print(f"\n❌ خطأ غير متوقع: {e}")
        print("📋 تفاصيل الخطأ تم حفظها في ملف السجلات")
        traceback.print_exc()
        input("اضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
