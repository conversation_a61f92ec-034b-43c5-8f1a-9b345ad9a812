Set WshShell = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' Get the directory of the current script
currentDir = fso.GetParentFolderName(WScript.ScriptFullName)

' Set the current working directory
WshShell.CurrentDirectory = currentDir

' Attempt to run the ultimate system using pythonw to hide the console
WshShell.Run "pythonw ultimate_system.py", 0, False

' Check for errors, if pythonw fails, try with regular python
If Err.Number <> 0 Then
    Err.Clear
    WshShell.Run "python ultimate_system.py", 0, False
End If

' If all methods fail, show an error message
If Err.Number <> 0 Then
    MsgBox "Error: Could not find Python or failed to run the script." & vbCrLf & "Please ensure Python is installed correctly.", vbCritical, "Execution Error"
End If

WScript.Quit
