#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل الخادم الويب
Web Server Launcher
"""

import sys
import os
import threading
import time
import webbrowser
from PyQt5.QtWidgets import QApplication, QSystemTrayIcon, QMenu, QAction, QMessageBox
from PyQt5.QtGui import QIcon
from PyQt5.QtCore import QTimer

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.database.database_manager import DatabaseManager
from src.web_server.web_app import create_web_app

class WebServerManager:
    """مدير الخادم الويب"""
    
    def __init__(self):
        self.db_manager = None
        self.web_app = None
        self.server_thread = None
        self.port = 5000
        self.host = '0.0.0.0'
        self.is_running = False
        
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            print("📊 تهيئة قاعدة البيانات...")
            self.db_manager = DatabaseManager()
            self.db_manager.initialize_database()
            print("✅ تم تهيئة قاعدة البيانات بنجاح")
            return True
        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {str(e)}")
            return False
    
    def start_web_server(self):
        """بدء تشغيل الخادم الويب"""
        try:
            print(f"🌐 بدء تشغيل الخادم الويب على {self.host}:{self.port}")
            
            # إنشاء تطبيق الويب
            self.web_app = create_web_app(self.db_manager, self.port)
            
            # تشغيل الخادم في خيط منفصل
            self.server_thread = self.web_app.run_in_thread(
                debug=False, 
                host=self.host
            )
            
            self.is_running = True
            print("✅ تم تشغيل الخادم الويب بنجاح")
            
            # انتظار قليل للتأكد من بدء الخادم
            time.sleep(2)
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل الخادم الويب: {str(e)}")
            return False
    
    def stop_web_server(self):
        """إيقاف الخادم الويب"""
        try:
            if self.is_running:
                print("🛑 إيقاف الخادم الويب...")
                self.is_running = False
                print("✅ تم إيقاف الخادم الويب")
            return True
        except Exception as e:
            print(f"❌ خطأ في إيقاف الخادم الويب: {str(e)}")
            return False
    
    def open_browser(self):
        """فتح المتصفح"""
        try:
            url = f"http://localhost:{self.port}"
            print(f"🌐 فتح المتصفح على: {url}")
            webbrowser.open(url)
            return True
        except Exception as e:
            print(f"❌ خطأ في فتح المتصفح: {str(e)}")
            return False
    
    def get_server_info(self):
        """الحصول على معلومات الخادم"""
        return {
            'host': self.host,
            'port': self.port,
            'is_running': self.is_running,
            'local_url': f"http://localhost:{self.port}",
            'network_url': f"http://{self.get_local_ip()}:{self.port}"
        }
    
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            import socket
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            return local_ip
        except:
            return "127.0.0.1"

class SystemTrayApp:
    """تطبيق شريط النظام"""
    
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.server_manager = WebServerManager()
        
        # إنشاء أيقونة شريط النظام
        self.tray_icon = QSystemTrayIcon()
        self.tray_icon.setIcon(QIcon("assets/icon.png") if os.path.exists("assets/icon.png") else self.app.style().standardIcon(self.app.style().SP_ComputerIcon))
        
        # إنشاء القائمة
        self.create_menu()
        
        # إعداد الأحداث
        self.tray_icon.activated.connect(self.on_tray_icon_activated)
        
        # عرض الأيقونة
        self.tray_icon.show()
        
        # بدء الخادم تلقائياً
        self.start_server()
    
    def create_menu(self):
        """إنشاء قائمة شريط النظام"""
        menu = QMenu()
        
        # عنوان القائمة
        title_action = QAction("نظام إدارة الطلاب - الخادم الويب")
        title_action.setEnabled(False)
        menu.addAction(title_action)
        menu.addSeparator()
        
        # فتح المتصفح
        open_browser_action = QAction("🌐 فتح في المتصفح")
        open_browser_action.triggered.connect(self.open_browser)
        menu.addAction(open_browser_action)
        
        # معلومات الخادم
        server_info_action = QAction("ℹ️ معلومات الخادم")
        server_info_action.triggered.connect(self.show_server_info)
        menu.addAction(server_info_action)
        
        menu.addSeparator()
        
        # إعادة تشغيل الخادم
        restart_action = QAction("🔄 إعادة تشغيل الخادم")
        restart_action.triggered.connect(self.restart_server)
        menu.addAction(restart_action)
        
        # إيقاف الخادم
        stop_action = QAction("🛑 إيقاف الخادم")
        stop_action.triggered.connect(self.stop_server)
        menu.addAction(stop_action)
        
        menu.addSeparator()
        
        # خروج
        exit_action = QAction("❌ خروج")
        exit_action.triggered.connect(self.quit_application)
        menu.addAction(exit_action)
        
        self.tray_icon.setContextMenu(menu)
    
    def on_tray_icon_activated(self, reason):
        """عند النقر على أيقونة شريط النظام"""
        if reason == QSystemTrayIcon.DoubleClick:
            self.open_browser()
    
    def start_server(self):
        """بدء تشغيل الخادم"""
        try:
            # تهيئة قاعدة البيانات
            if not self.server_manager.initialize_database():
                self.show_error("فشل في تهيئة قاعدة البيانات")
                return
            
            # بدء الخادم
            if self.server_manager.start_web_server():
                self.tray_icon.showMessage(
                    "نظام إدارة الطلاب",
                    "تم تشغيل الخادم الويب بنجاح!\nانقر مرتين لفتح المتصفح",
                    QSystemTrayIcon.Information,
                    3000
                )
                
                # فتح المتصفح تلقائياً بعد 3 ثوان
                QTimer.singleShot(3000, self.open_browser)
            else:
                self.show_error("فشل في تشغيل الخادم الويب")
                
        except Exception as e:
            self.show_error(f"خطأ في بدء الخادم: {str(e)}")
    
    def stop_server(self):
        """إيقاف الخادم"""
        if self.server_manager.stop_web_server():
            self.tray_icon.showMessage(
                "نظام إدارة الطلاب",
                "تم إيقاف الخادم الويب",
                QSystemTrayIcon.Information,
                2000
            )
    
    def restart_server(self):
        """إعادة تشغيل الخادم"""
        self.stop_server()
        time.sleep(1)
        self.start_server()
    
    def open_browser(self):
        """فتح المتصفح"""
        if self.server_manager.is_running:
            self.server_manager.open_browser()
        else:
            self.show_error("الخادم غير مشغل")
    
    def show_server_info(self):
        """عرض معلومات الخادم"""
        info = self.server_manager.get_server_info()
        
        message = f"""
معلومات الخادم الويب:

🌐 الحالة: {'مشغل' if info['is_running'] else 'متوقف'}
🏠 العنوان المحلي: {info['local_url']}
🌍 عنوان الشبكة: {info['network_url']}
🔌 المنفذ: {info['port']}

يمكن الوصول للنظام من أي جهاز على نفس الشبكة باستخدام عنوان الشبكة.
        """
        
        QMessageBox.information(None, "معلومات الخادم", message)
    
    def show_error(self, message):
        """عرض رسالة خطأ"""
        QMessageBox.critical(None, "خطأ", message)
        self.tray_icon.showMessage(
            "خطأ",
            message,
            QSystemTrayIcon.Critical,
            3000
        )
    
    def quit_application(self):
        """إنهاء التطبيق"""
        self.stop_server()
        self.tray_icon.hide()
        self.app.quit()
    
    def run(self):
        """تشغيل التطبيق"""
        return self.app.exec_()

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل نظام إدارة الطلاب - الخادم الويب")
    print("=" * 50)
    
    # التحقق من دعم شريط النظام
    if not QSystemTrayIcon.isSystemTrayAvailable():
        print("❌ شريط النظام غير متاح")
        # تشغيل الخادم مباشرة بدون واجهة
        server_manager = WebServerManager()
        if server_manager.initialize_database():
            if server_manager.start_web_server():
                print("✅ الخادم يعمل الآن")
                print(f"🌐 افتح المتصفح على: http://localhost:{server_manager.port}")
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n🛑 إيقاف الخادم...")
                    server_manager.stop_web_server()
        return
    
    # تشغيل تطبيق شريط النظام
    try:
        app = SystemTrayApp()
        return app.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
