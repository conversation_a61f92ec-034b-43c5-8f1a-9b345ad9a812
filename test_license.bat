@echo off
title اختبار نظام الترخيص

echo ===================================================
echo    اختبار نظام الترخيص - School Management System
echo    الإصدار 1.0.0
echo ===================================================

:: Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: Python غير مثبت أو غير مضاف إلى متغيرات النظام.
    echo الرجاء تثبيت Python 3.8 أو أحدث من الموقع الرسمي.
    pause
    exit /b 1
)

:: Run the test script
echo.
echo جاري تشغيل اختبارات نظام الترخيص...
python test_license.py

:: Show result
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم تنفيذ جميع الاختبارات بنجاح!
) else (
    echo.
    echo ❌ حدث خطأ أثناء تنفيذ الاختبارات.
)

echo.
pause
