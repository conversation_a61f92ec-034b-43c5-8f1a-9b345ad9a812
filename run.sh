#!/bin/bash

echo "==================================================="
echo "   نظام إدارة المدارس - School Management System"
echo "   الإصدار 1.0.0"
echo "==================================================="

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "خطأ: Python 3 غير مثبت أو غير مضاف إلى متغيرات النظام."
    echo "الرجاء تثبيت Python 3.8 أو أحدث."
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "جاري إعداد البيئة الافتراضية..."
    python3 -m venv venv
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
else
    source venv/bin/activate
fi

# Create necessary directories
mkdir -p data logs

# Run the application
echo ""
echo "جاري تشغيل النظام..."
python3 main.py

# Check if there was an error
if [ $? -ne 0 ]; then
    echo ""
    echo "حدث خطأ أثناء تشغيل التطبيق."
    echo "يرجى مراجعة ملف السجل في مجلد logs لمزيد من التفاصيل."
    read -p "اضغط Enter للمتابعة..."
fi
