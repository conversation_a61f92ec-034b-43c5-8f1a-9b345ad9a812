@echo off
title نظام إدارة المدارس

:: Hide the console window
if "%1" == "" (
    start "" /B "%~f0" _
    exit /b
)

:: Set the working directory to the script's directory
cd /d "%~dp0"

echo ===================================================
echo    نظام إدارة المدارس - School Management System
echo    الإصدار 1.0.0
echo ===================================================

:: Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    msg * خطأ: Python غير مثبت أو غير مضاف إلى متغيرات النظام.
    start https://www.python.org/downloads/
    exit /b 1
)

:: Install required packages if not installed
python -m pip install --upgrade pip
pip install -r requirements.txt

:: Run the application
start "" /B pythonw main.py

exit
