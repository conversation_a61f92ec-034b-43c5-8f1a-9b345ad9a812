# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول
Login Window
"""

import sys
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QMessageBox, QFrame,
                            QApplication, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QIcon

from ..database.database_manager import DatabaseManager
from ..utils.auth import AuthManager
from ..utils.styles import get_login_style, get_arabic_font_style
from .main_window import MainWindow

class LoginWindow(QWidget):
    """نافذة تسجيل الدخول"""
    
    # إشارة نجاح تسجيل الدخول
    login_successful = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.auth_manager = AuthManager(self.db_manager)
        self.main_window = None
        
        self.init_ui()
        self.setup_styles()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - نظام إدارة الطلاب")
        self.setFixedSize(800, 900)  # تكبير أكثر للوضوح الكامل
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint)
        
        # تمركز النافذة في الشاشة
        self.center_window()
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 40, 40, 40)
        
        # إطار العنوان
        title_frame = self.create_title_frame()
        main_layout.addWidget(title_frame)
        
        # إطار تسجيل الدخول
        login_frame = self.create_login_frame()
        main_layout.addWidget(login_frame)
        
        # إطار الأزرار
        buttons_frame = self.create_buttons_frame()
        main_layout.addWidget(buttons_frame)
        
        main_layout.addStretch()
        self.setLayout(main_layout)
    
    def create_title_frame(self):
        """إنشاء إطار العنوان"""
        frame = QFrame()
        layout = QVBoxLayout()
        
        # عنوان التطبيق
        title_label = QLabel("نظام إدارة الطلاب")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("title")
        
        # وصف النظام
        teacher_label = QLabel("نظام شامل لإدارة الطلاب والدرجات والحضور")
        teacher_label.setAlignment(Qt.AlignCenter)
        teacher_label.setObjectName("teacher")
        
        # خط فاصل
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        
        layout.addWidget(title_label)
        layout.addWidget(teacher_label)
        layout.addWidget(line)
        
        frame.setLayout(layout)
        return frame
    
    def create_login_frame(self):
        """إنشاء إطار تسجيل الدخول"""
        frame = QFrame()
        frame.setObjectName("loginFrame")
        layout = QVBoxLayout()
        layout.setSpacing(15)
        
        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setStyleSheet("font-size: 20px; font-weight: bold; color: #333; margin-bottom: 8px;")
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        # إزالة القيمة الافتراضية للأمان
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 20px;
                border: 3px solid #ddd;
                border-radius: 12px;
                font-size: 28px;
                font-weight: bold;
                background-color: white;
                min-height: 35px;
                color: #333;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
                background-color: #f8fff8;
            }
        """)

        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        password_label.setStyleSheet("font-size: 20px; font-weight: bold; color: #333; margin-bottom: 8px;")
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        # إزالة القيمة الافتراضية للأمان
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 20px;
                border: 3px solid #ddd;
                border-radius: 12px;
                font-size: 28px;
                font-weight: bold;
                background-color: white;
                min-height: 35px;
                color: #333;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
                background-color: #f8fff8;
            }
        """)
        
        # خانة إظهار كلمة المرور
        self.show_password_checkbox = QCheckBox("إظهار كلمة المرور")
        self.show_password_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 14px;
                font-weight: bold;
                color: #555;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        self.show_password_checkbox.toggled.connect(self.toggle_password_visibility)
        
        layout.addWidget(username_label)
        layout.addWidget(self.username_input)
        layout.addWidget(password_label)
        layout.addWidget(self.password_input)
        layout.addWidget(self.show_password_checkbox)
        
        frame.setLayout(layout)
        return frame
    
    def create_buttons_frame(self):
        """إنشاء إطار الأزرار"""
        frame = QFrame()
        layout = QVBoxLayout()
        layout.setSpacing(10)
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setObjectName("loginButton")
        self.login_button.clicked.connect(self.handle_login)
        
        # زر الخروج
        self.exit_button = QPushButton("خروج")
        self.exit_button.setObjectName("exitButton")
        self.exit_button.clicked.connect(self.close)
        
        layout.addWidget(self.login_button)
        layout.addWidget(self.exit_button)
        
        frame.setLayout(layout)
        return frame
    

    
    def setup_styles(self):
        """تطبيق الأنماط"""
        # تطبيق الأنماط الجديدة
        style = get_login_style() + get_arabic_font_style()
        self.setStyleSheet(style)

        # تعيين أسماء الكائنات للأنماط
        self.setObjectName("loginWindow")
    
    def center_window(self):
        """تمركز النافذة في الشاشة"""
        screen = QApplication.desktop().screenGeometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    def toggle_password_visibility(self, checked):
        """تبديل إظهار/إخفاء كلمة المرور"""
        if checked:
            self.password_input.setEchoMode(QLineEdit.Normal)
        else:
            self.password_input.setEchoMode(QLineEdit.Password)
    
    def handle_login(self):
        """معالجة تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        # التحقق من صحة البيانات
        if not username:
            self.show_error("يرجى إدخال اسم المستخدم")
            self.username_input.setFocus()
            return
        
        if not password:
            self.show_error("يرجى إدخال كلمة المرور")
            self.password_input.setFocus()
            return
        
        # محاولة تسجيل الدخول
        if self.auth_manager.login(username, password):
            self.show_success("تم تسجيل الدخول بنجاح")
            self.open_main_window()
        else:
            self.show_error("اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_input.clear()
            self.password_input.setFocus()
    
    def open_main_window(self):
        """فتح النافذة الرئيسية"""
        try:
            self.main_window = MainWindow(self.auth_manager)
            self.main_window.show()
            self.hide()
            
            # ربط إشارة إغلاق النافذة الرئيسية
            self.main_window.logout_signal.connect(self.show_login_again)
            
        except Exception as e:
            self.show_error(f"خطأ في فتح النافذة الرئيسية: {str(e)}")
    
    def show_login_again(self):
        """إظهار نافذة تسجيل الدخول مرة أخرى"""
        self.password_input.clear()
        self.username_input.setFocus()
        self.show()
        
        if self.main_window:
            self.main_window.close()
            self.main_window = None
    
    def show_error(self, message):
        """إظهار رسالة خطأ"""
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Critical)
        msg.setWindowTitle("خطأ")
        msg.setText(message)
        msg.setStyleSheet("""
            QMessageBox {
                background-color: white;
                color: black;
                font-size: 16px;
                font-weight: bold;
            }
            QMessageBox QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        msg.exec_()
    
    def show_success(self, message):
        """إظهار رسالة نجاح"""
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Information)
        msg.setWindowTitle("نجح")
        msg.setText(message)
        msg.setStyleSheet("""
            QMessageBox {
                background-color: white;
                color: black;
                font-size: 16px;
                font-weight: bold;
            }
            QMessageBox QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background-color: #229954;
            }
        """)
        msg.exec_()
    
    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.handle_login()
        else:
            super().keyPressEvent(event)
    
    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        reply = QMessageBox.question(
            self, 'تأكيد الخروج',
            'هل تريد إغلاق التطبيق؟',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # تنظيف الموارد
            if self.main_window:
                self.main_window.close()
            self.db_manager.disconnect()
            event.accept()
            QApplication.quit()
        else:
            event.ignore()
