#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إزالة الأسماء القديمة من جميع الملفات
Remove Old Names from All Files

هذا الملف يبحث عن ويزيل جميع المراجع للأسماء القديمة
"""

import os
import re
from pathlib import Path

def find_and_replace_in_file(file_path, replacements):
    """البحث والاستبدال في ملف واحد"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # تطبيق جميع الاستبدالات
        for old_text, new_text in replacements.items():
            content = content.replace(old_text, new_text)
        
        # حفظ الملف إذا تم تغييره
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"خطأ في معالجة {file_path}: {e}")
        return False

def clean_all_files():
    """تنظيف جميع الملفات من الأسماء القديمة"""
    
    # قائمة الاستبدالات
    replacements = {
        "مستر أحمد عادل": "نظام إدارة الطلاب المتطور",
        "معلم الجغرافيا والتاريخ": "نظام شامل لإدارة الطلاب",
        "معلم التاريخ والجغرافيا": "نظام شامل لإدارة الطلاب",
        "قناة نظام طلاب مستر أحمد عادل": "نظام إدارة الطلاب المتطور",
        "Mr. Ahmed Adel": "Advanced Student Management System",
        "Geography and History Teacher": "Comprehensive Student Management",
        "Ahmed Adel": "Student Management System"
    }
    
    # أنواع الملفات المراد معالجتها
    file_extensions = ['.py', '.md', '.txt', '.bat', '.html', '.js', '.css', '.json']
    
    # مجلدات يجب تجاهلها
    ignore_dirs = {'.git', '__pycache__', '.venv', 'venv', 'node_modules', 'dist', 'build'}
    
    # ملفات يجب تجاهلها
    ignore_files = {'remove_old_names.py', 'test_new_features.py'}
    
    processed_files = []
    changed_files = []
    
    # البحث في جميع الملفات
    for root, dirs, files in os.walk('.'):
        # تجاهل المجلدات المحددة
        dirs[:] = [d for d in dirs if d not in ignore_dirs]
        
        for file in files:
            # تجاهل الملفات المحددة
            if file in ignore_files:
                continue
                
            file_path = Path(root) / file
            
            # معالجة الملفات ذات الامتدادات المحددة فقط
            if file_path.suffix.lower() in file_extensions:
                processed_files.append(str(file_path))
                
                if find_and_replace_in_file(file_path, replacements):
                    changed_files.append(str(file_path))
                    print(f"✅ تم تحديث: {file_path}")
    
    return processed_files, changed_files

def main():
    """الدالة الرئيسية"""
    print("🧹 إزالة الأسماء القديمة من جميع الملفات")
    print("=" * 50)
    print()
    
    print("🔍 البحث عن الملفات...")
    processed_files, changed_files = clean_all_files()
    
    print()
    print("📊 النتائج:")
    print(f"   • تم فحص {len(processed_files)} ملف")
    print(f"   • تم تحديث {len(changed_files)} ملف")
    
    if changed_files:
        print()
        print("📝 الملفات المحدثة:")
        for file in changed_files:
            print(f"   - {file}")
    
    print()
    print("✅ تم الانتهاء من تنظيف جميع الملفات!")
    
    return len(changed_files) > 0

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 تم تحديث الملفات بنجاح!")
    else:
        print("\n💡 لا توجد ملفات تحتاج إلى تحديث")
