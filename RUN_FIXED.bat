@echo off
chcp 65001 >nul
title نظام إدارة الطلاب - نظام إدارة الطلاب المتطور

cls
color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة الطلاب                        ║
echo ║                   نظام إدارة الطلاب المتطور                           ║
echo ║              نظام شامل لإدارة الطلاب                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 تشغيل النظام...
echo.

REM اختبار Python
python -c "print('✅ Python يعمل')" 2>nul
if errorlevel 1 (
    echo ❌ Python لا يعمل
    goto error
)

REM اختبار PyQt5
python -c "import PyQt5; print('✅ PyQt5 يعمل')" 2>nul
if errorlevel 1 (
    echo ❌ PyQt5 لا يعمل
    echo 📦 تثبيت PyQt5...
    python -m pip install PyQt5
)

echo.
echo 🎯 تشغيل النظام الأساسي...
echo.

REM تشغيل النظام
python main.py
if not errorlevel 1 goto success

echo.
echo 🔄 محاولة من مجلد SMS_Package...
if exist "SMS_Package\main.py" (
    cd SMS_Package
    python main.py
    if not errorlevel 1 goto success
)

echo.
echo ❌ فشل في تشغيل النظام
goto error

:success
echo.
echo ✅ تم تشغيل النظام بنجاح!
goto end

:error
echo.
echo ❌ مشكلة في تشغيل النظام
echo.
echo 🔧 حلول مقترحة:
echo 1. تأكد من وجود ملفات src/
echo 2. جرب ملف RUN_COMPACT_FINAL.bat
echo 3. أو جرب ملفات أخرى من مجلد SMS_Package
echo.

:end
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🎓 تم التطوير بواسطة: م/ حسام أسامة
echo.
pause
