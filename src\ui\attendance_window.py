# -*- coding: utf-8 -*-
"""
نافذة تسجيل الحضور
Attendance Window
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                            QComboBox, QMessageBox, QFrame, QGroupBox, QFormLayout,
                            QHeaderView, QAbstractItemView, QSplitter, QDateEdit,
                            QTextEdit, QTabWidget)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import QFont

from datetime import date, datetime
from ..models.student import Student
from ..models.attendance import Attendance
from ..models.groups import Groups
from ..database.database_manager import DatabaseManager
from ..utils.messaging import messaging_system

class AttendanceWindow(QWidget):
    """نافذة تسجيل الحضور"""
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
        self.student_model = Student(db_manager)
        self.attendance_model = Attendance(db_manager)
        self.groups_model = Groups(db_manager)
        
        self.init_ui()
        self.setup_styles()
        self.load_today_attendance()
        
        # تحديث الوقت كل ثانية
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)
        self.update_time()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تسجيل الحضور والانصراف")
        self.setGeometry(100, 100, 1200, 800)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # شريط المعلومات العلوي
        info_frame = self.create_info_frame()
        main_layout.addWidget(info_frame)
        
        # التبويبات
        tabs = QTabWidget()
        
        # تبويب تسجيل الحضور السريع
        quick_attendance_tab = self.create_quick_attendance_tab()
        tabs.addTab(quick_attendance_tab, "تسجيل سريع")
        
        # تبويب إدارة الحضور
        manage_attendance_tab = self.create_manage_attendance_tab()
        tabs.addTab(manage_attendance_tab, "إدارة الحضور")
        
        # تبويب التقارير
        reports_tab = self.create_reports_tab()
        tabs.addTab(reports_tab, "تقارير الحضور")
        
        main_layout.addWidget(tabs)
        self.setLayout(main_layout)
    
    def create_info_frame(self):
        """إنشاء إطار المعلومات العلوي"""
        frame = QFrame()
        frame.setObjectName("infoFrame")
        layout = QHBoxLayout()
        
        # التاريخ والوقت
        self.date_label = QLabel()
        self.time_label = QLabel()
        self.date_label.setObjectName("dateLabel")
        self.time_label.setObjectName("timeLabel")
        
        layout.addWidget(QLabel("التاريخ:"))
        layout.addWidget(self.date_label)
        layout.addStretch()
        layout.addWidget(QLabel("الوقت:"))
        layout.addWidget(self.time_label)
        
        frame.setLayout(layout)
        return frame
    
    def create_quick_attendance_tab(self):
        """إنشاء تبويب التسجيل السريع"""
        widget = QWidget()
        layout = QVBoxLayout()

        # فلتر المجموعة
        group_filter_frame = QGroupBox("فلتر المجموعة")
        group_filter_layout = QHBoxLayout()

        group_filter_layout.addWidget(QLabel("المجموعة:"))
        self.group_filter_combo = QComboBox()
        self.group_filter_combo.addItem("جميع المجموعات")
        self.populate_group_filter()
        self.group_filter_combo.currentTextChanged.connect(self.filter_students_by_group)
        group_filter_layout.addWidget(self.group_filter_combo)
        group_filter_layout.addStretch()

        group_filter_frame.setLayout(group_filter_layout)
        layout.addWidget(group_filter_frame)

        # إطار التسجيل السريع
        quick_group = QGroupBox("تسجيل الحضور السريع")
        quick_layout = QVBoxLayout()
        
        # حقل إدخال الكود
        code_layout = QHBoxLayout()

        self.student_code_input = QLineEdit()
        self.student_code_input.setPlaceholderText("أدخل كود الطالب واضغط Enter")
        self.student_code_input.returnPressed.connect(self.quick_mark_attendance)
        self.student_code_input.setFont(QFont("Arial", 16))

        self.mark_button = QPushButton("تسجيل حضور")
        self.mark_button.clicked.connect(self.quick_mark_attendance)

        code_layout.addWidget(QLabel("كود الطالب:"))
        code_layout.addWidget(self.student_code_input)
        code_layout.addWidget(self.mark_button)

        quick_layout.addLayout(code_layout)

        # حقل البحث بالاسم
        name_layout = QHBoxLayout()

        self.student_name_input = QLineEdit()
        self.student_name_input.setPlaceholderText("أو ابحث بالاسم...")
        self.student_name_input.textChanged.connect(self.search_student_by_name)
        self.student_name_input.setFont(QFont("Arial", 14))

        self.name_search_button = QPushButton("بحث بالاسم")
        self.name_search_button.clicked.connect(self.search_and_mark_by_name)

        name_layout.addWidget(QLabel("اسم الطالب:"))
        name_layout.addWidget(self.student_name_input)
        name_layout.addWidget(self.name_search_button)

        quick_layout.addLayout(name_layout)

        # قائمة نتائج البحث
        self.search_results_combo = QComboBox()
        self.search_results_combo.setVisible(False)
        self.search_results_combo.currentTextChanged.connect(self.select_student_from_search)
        quick_layout.addWidget(self.search_results_combo)
        
        # معلومات الطالب
        self.student_info_label = QLabel("أدخل كود الطالب لعرض معلوماته")
        self.student_info_label.setObjectName("studentInfo")
        quick_layout.addWidget(self.student_info_label)
        
        # حالة الحضور
        status_layout = QHBoxLayout()
        self.status_combo = QComboBox()
        self.status_combo.addItems(["حاضر", "متأخر", "غائب"])
        
        self.notes_input = QLineEdit()
        self.notes_input.setPlaceholderText("ملاحظات (اختياري)")
        
        status_layout.addWidget(QLabel("الحالة:"))
        status_layout.addWidget(self.status_combo)
        status_layout.addWidget(QLabel("ملاحظات:"))
        status_layout.addWidget(self.notes_input)
        
        quick_layout.addLayout(status_layout)
        
        quick_group.setLayout(quick_layout)
        layout.addWidget(quick_group)
        
        # إحصائيات اليوم
        stats_group = self.create_daily_stats_group()
        layout.addWidget(stats_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def create_manage_attendance_tab(self):
        """إنشاء تبويب إدارة الحضور"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # شريط التحكم
        control_layout = QHBoxLayout()

        # فلتر المجموعة
        control_layout.addWidget(QLabel("المجموعة:"))
        self.manage_group_filter_combo = QComboBox()
        self.manage_group_filter_combo.addItem("جميع المجموعات")
        self.populate_manage_group_filter()
        self.manage_group_filter_combo.currentTextChanged.connect(self.load_today_attendance)
        control_layout.addWidget(self.manage_group_filter_combo)

        # اختيار التاريخ
        control_layout.addWidget(QLabel("التاريخ:"))
        self.date_picker = QDateEdit()
        self.date_picker.setDate(QDate.currentDate())
        self.date_picker.dateChanged.connect(self.load_attendance_for_date)
        control_layout.addWidget(self.date_picker)

        self.refresh_button = QPushButton("تحديث")
        self.refresh_button.clicked.connect(self.load_today_attendance)
        control_layout.addWidget(self.refresh_button)
        control_layout.addStretch()

        layout.addLayout(control_layout)
        
        # جدول الحضور
        self.attendance_table = QTableWidget()
        self.attendance_table.setColumnCount(7)
        self.attendance_table.setHorizontalHeaderLabels([
            "الكود", "الاسم", "المجموعة", "الصف", "الحالة", "الوقت", "ملاحظات"
        ])
        
        # إعدادات الجدول
        header = self.attendance_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        self.attendance_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.attendance_table.setAlternatingRowColors(True)
        self.attendance_table.itemDoubleClicked.connect(self.edit_attendance_record)
        
        layout.addWidget(self.attendance_table)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        self.mark_all_present_button = QPushButton("تسجيل الكل حاضر")
        self.mark_all_present_button.clicked.connect(self.mark_all_present)
        
        self.mark_selected_absent_button = QPushButton("تسجيل المحدد غائب")
        self.mark_selected_absent_button.clicked.connect(self.mark_selected_absent)
        
        self.export_button = QPushButton("تصدير لـ Excel")
        self.export_button.clicked.connect(self.export_attendance)
        
        buttons_layout.addWidget(self.mark_all_present_button)
        buttons_layout.addWidget(self.mark_selected_absent_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.export_button)
        
        layout.addLayout(buttons_layout)
        
        widget.setLayout(layout)
        return widget
    
    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # فلاتر التقرير
        filters_group = QGroupBox("فلاتر التقرير")
        filters_layout = QFormLayout()
        
        self.report_start_date = QDateEdit()
        self.report_start_date.setDate(QDate.currentDate().addDays(-30))
        
        self.report_end_date = QDateEdit()
        self.report_end_date.setDate(QDate.currentDate())
        
        self.report_stage_combo = QComboBox()
        self.report_stage_combo.addItems(["الكل", "إعدادي", "ثانوي"])
        
        self.generate_report_button = QPushButton("إنشاء التقرير")
        self.generate_report_button.clicked.connect(self.generate_attendance_report)
        
        filters_layout.addRow("من تاريخ:", self.report_start_date)
        filters_layout.addRow("إلى تاريخ:", self.report_end_date)
        filters_layout.addRow("المرحلة:", self.report_stage_combo)
        filters_layout.addRow("", self.generate_report_button)
        
        filters_group.setLayout(filters_layout)
        layout.addWidget(filters_group)
        
        # منطقة عرض التقرير
        self.report_text = QTextEdit()
        self.report_text.setReadOnly(True)
        layout.addWidget(self.report_text)
        
        widget.setLayout(layout)
        return widget
    
    def create_daily_stats_group(self):
        """إنشاء مجموعة إحصائيات اليوم"""
        group = QGroupBox("إحصائيات اليوم")
        layout = QHBoxLayout()
        
        self.total_students_stat = QLabel("إجمالي الطلاب: 0")
        self.present_students_stat = QLabel("الحاضرين: 0")
        self.absent_students_stat = QLabel("الغائبين: 0")
        self.late_students_stat = QLabel("المتأخرين: 0")
        self.attendance_rate_stat = QLabel("معدل الحضور: 0%")
        
        layout.addWidget(self.total_students_stat)
        layout.addWidget(self.present_students_stat)
        layout.addWidget(self.absent_students_stat)
        layout.addWidget(self.late_students_stat)
        layout.addWidget(self.attendance_rate_stat)
        
        group.setLayout(layout)
        return group
    
    def update_time(self):
        """تحديث الوقت والتاريخ"""
        now = datetime.now()
        self.date_label.setText(now.strftime("%Y-%m-%d"))
        self.time_label.setText(now.strftime("%H:%M:%S"))
    
    def quick_mark_attendance(self):
        """تسجيل الحضور السريع"""
        student_code = self.student_code_input.text().strip()
        
        if not student_code:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال كود الطالب")
            return
        
        # البحث عن الطالب
        student = self.student_model.get_student_by_code(student_code)
        
        if not student:
            QMessageBox.warning(self, "خطأ", f"لم يتم العثور على طالب بالكود: {student_code}")
            self.student_code_input.clear()
            self.student_info_label.setText("طالب غير موجود")
            return
        
        # عرض معلومات الطالب
        self.student_info_label.setText(
            f"الطالب: {student['full_name']} | الصف: {student['grade']} | النوع: {student['gender']}"
        )
        
        # تسجيل الحضور
        status = self.status_combo.currentText()
        notes = self.notes_input.text().strip()
        
        if self.attendance_model.mark_attendance_by_code(student_code, date.today(), status, notes):
            QMessageBox.information(self, "نجح", f"تم تسجيل {status} للطالب {student['full_name']}")

            # إرسال رسالة تلقائية في حالة الغياب
            if status == "غائب" and student.get('parent_phone'):
                try:
                    messaging_system.send_absence_notification(
                        student['full_name'],
                        student['parent_phone'],
                        date.today().strftime("%Y-%m-%d")
                    )
                except Exception as e:
                    print(f"خطأ في إرسال رسالة الغياب: {e}")

            self.student_code_input.clear()
            self.notes_input.clear()
            self.student_info_label.setText("تم التسجيل بنجاح")

            # تحديث الجداول والإحصائيات
            self.load_today_attendance()
            self.update_daily_stats()
        else:
            QMessageBox.critical(self, "خطأ", "فشل في تسجيل الحضور")
        
        # التركيز على حقل الإدخال
        self.student_code_input.setFocus()

    def load_today_attendance(self):
        """تحميل حضور اليوم"""
        today = date.today()
        self.load_attendance_for_date(QDate.currentDate())

    def load_attendance_for_date(self, qdate):
        """تحميل الحضور لتاريخ معين مع فلترة المجموعة"""
        selected_date = qdate.toPyDate()
        attendance_records = self.attendance_model.get_daily_attendance(selected_date)

        # فلترة حسب المجموعة المحددة
        if hasattr(self, 'manage_group_filter_combo'):
            selected_group = self.manage_group_filter_combo.currentText()
            if selected_group != "جميع المجموعات":
                attendance_records = [r for r in attendance_records if r.get('group_name') == selected_group]

        self.attendance_table.setRowCount(len(attendance_records))

        for row, record in enumerate(attendance_records):
            self.attendance_table.setItem(row, 0, QTableWidgetItem(record['student_code']))
            self.attendance_table.setItem(row, 1, QTableWidgetItem(record['full_name']))
            self.attendance_table.setItem(row, 2, QTableWidgetItem(record.get('group_name', 'لا توجد مجموعة')))
            self.attendance_table.setItem(row, 3, QTableWidgetItem(record['grade']))

            status = record.get('status', 'غير مسجل')
            self.attendance_table.setItem(row, 4, QTableWidgetItem(status))

            # الوقت (إذا كان متوفراً)
            time_str = ""
            if record.get('attendance_date'):
                time_str = str(record['attendance_date'])
            self.attendance_table.setItem(row, 5, QTableWidgetItem(time_str))

            notes = record.get('notes', '')
            self.attendance_table.setItem(row, 6, QTableWidgetItem(notes))

            # حفظ معرف الطالب
            self.attendance_table.item(row, 0).setData(Qt.UserRole, record['id'])

            # تلوين الصفوف حسب الحالة
            if status == 'حاضر':
                color = Qt.green
            elif status == 'غائب':
                color = Qt.red
            elif status == 'متأخر':
                color = Qt.yellow
            else:
                color = Qt.white

            for col in range(6):
                if self.attendance_table.item(row, col):
                    self.attendance_table.item(row, col).setBackground(color)

        self.update_daily_stats()

    def update_daily_stats(self):
        """تحديث إحصائيات اليوم"""
        total_rows = self.attendance_table.rowCount()
        present_count = 0
        absent_count = 0
        late_count = 0

        for row in range(total_rows):
            status_item = self.attendance_table.item(row, 3)
            if status_item:
                status = status_item.text()
                if status == 'حاضر':
                    present_count += 1
                elif status == 'غائب':
                    absent_count += 1
                elif status == 'متأخر':
                    late_count += 1
                else:  # غير مسجل
                    absent_count += 1

        # حساب معدل الحضور
        if total_rows > 0:
            attendance_rate = (present_count + late_count) / total_rows * 100
        else:
            attendance_rate = 0

        # تحديث التسميات
        self.total_students_stat.setText(f"إجمالي الطلاب: {total_rows}")
        self.present_students_stat.setText(f"الحاضرين: {present_count}")
        self.absent_students_stat.setText(f"الغائبين: {absent_count}")
        self.late_students_stat.setText(f"المتأخرين: {late_count}")
        self.attendance_rate_stat.setText(f"معدل الحضور: {attendance_rate:.1f}%")

    def mark_all_present(self):
        """تسجيل جميع الطلاب حاضرين"""
        reply = QMessageBox.question(
            self, 'تأكيد',
            'هل تريد تسجيل جميع الطلاب كحاضرين؟',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            today = date.today()
            all_students = self.student_model.get_all_students()

            success_count = 0
            for student in all_students:
                if self.attendance_model.mark_attendance(student['id'], today, 'حاضر'):
                    success_count += 1

            QMessageBox.information(self, "تم", f"تم تسجيل {success_count} طالب كحاضرين")
            self.load_today_attendance()

    def mark_selected_absent(self):
        """تسجيل الطلاب المحددين غائبين"""
        selected_rows = set()
        for item in self.attendance_table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار الطلاب المراد تسجيلهم غائبين")
            return

        today = date.today()
        success_count = 0

        for row in selected_rows:
            student_id_item = self.attendance_table.item(row, 0)
            if student_id_item:
                student_id = student_id_item.data(Qt.UserRole)
                if self.attendance_model.mark_attendance(student_id, today, 'غائب'):
                    success_count += 1

        QMessageBox.information(self, "تم", f"تم تسجيل {success_count} طالب كغائبين")
        self.load_today_attendance()

    def edit_attendance_record(self, item):
        """تعديل سجل حضور"""
        row = item.row()
        student_id_item = self.attendance_table.item(row, 0)

        if student_id_item:
            student_id = student_id_item.data(Qt.UserRole)
            student_name = self.attendance_table.item(row, 1).text()
            current_status = self.attendance_table.item(row, 3).text()

            # حوار تعديل الحالة
            from PyQt5.QtWidgets import QDialog, QDialogButtonBox

            dialog = QDialog(self)
            dialog.setWindowTitle(f"تعديل حضور: {student_name}")
            dialog.setModal(True)

            layout = QVBoxLayout()

            status_combo = QComboBox()
            status_combo.addItems(["حاضر", "غائب", "متأخر"])
            status_combo.setCurrentText(current_status)

            notes_input = QLineEdit()
            current_notes = self.attendance_table.item(row, 5).text()
            notes_input.setText(current_notes)

            layout.addWidget(QLabel("الحالة:"))
            layout.addWidget(status_combo)
            layout.addWidget(QLabel("ملاحظات:"))
            layout.addWidget(notes_input)

            buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            buttons.accepted.connect(dialog.accept)
            buttons.rejected.connect(dialog.reject)
            layout.addWidget(buttons)

            dialog.setLayout(layout)

            if dialog.exec_() == QDialog.Accepted:
                new_status = status_combo.currentText()
                new_notes = notes_input.text()

                if self.attendance_model.mark_attendance(student_id, date.today(), new_status, new_notes):
                    QMessageBox.information(self, "تم", "تم تحديث سجل الحضور")
                    self.load_today_attendance()
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في تحديث سجل الحضور")

    def export_attendance(self):
        """تصدير الحضور لملف Excel"""
        try:
            from openpyxl import Workbook
            from PyQt5.QtWidgets import QFileDialog

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الحضور",
                f"attendance_{date.today().strftime('%Y-%m-%d')}.xlsx",
                "Excel Files (*.xlsx)"
            )

            if file_path:
                wb = Workbook()
                ws = wb.active
                ws.title = "تقرير الحضور"

                # العناوين
                headers = ["الكود", "الاسم", "الصف", "الحالة", "التاريخ", "ملاحظات"]
                for col, header in enumerate(headers, 1):
                    ws.cell(row=1, column=col, value=header)

                # البيانات
                for row in range(self.attendance_table.rowCount()):
                    for col in range(6):
                        item = self.attendance_table.item(row, col)
                        if item:
                            ws.cell(row=row+2, column=col+1, value=item.text())

                wb.save(file_path)
                QMessageBox.information(self, "تم", f"تم حفظ التقرير في:\n{file_path}")

        except ImportError:
            QMessageBox.warning(self, "خطأ", "مكتبة openpyxl غير مثبتة")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير: {str(e)}")

    def generate_attendance_report(self):
        """إنشاء تقرير الحضور"""
        start_date = self.report_start_date.date().toPyDate()
        end_date = self.report_end_date.date().toPyDate()
        stage_filter = self.report_stage_combo.currentText()

        # جلب إحصائيات الحضور
        stats = self.attendance_model.get_attendance_statistics(start_date, end_date)

        # إنشاء التقرير
        report = f"""
تقرير الحضور
من {start_date} إلى {end_date}
المرحلة: {stage_filter}

الإحصائيات العامة:
- إجمالي السجلات: {stats.get('total_records', 0)}
- الحاضرين: {stats.get('by_status', {}).get('حاضر', 0)}
- الغائبين: {stats.get('by_status', {}).get('غائب', 0)}
- المتأخرين: {stats.get('by_status', {}).get('متأخر', 0)}

الإحصائيات اليومية:
"""

        for day_stat in stats.get('daily_stats', []):
            report += f"- {day_stat['attendance_date']}: حاضر {day_stat['present']}, غائب {day_stat['absent']}, متأخر {day_stat['late']}\n"

        report += "\nأفضل الطلاب حضوراً:\n"
        for student in stats.get('top_attendance', [])[:10]:
            report += f"- {student['full_name']} ({student['student_code']}): {student['present_days']}/{student['total_days']} يوم\n"

        self.report_text.setText(report)

    def setup_styles(self):
        """تطبيق الأنماط"""
        style = """
        #infoFrame {
            background-color: #34495e;
            color: white;
            padding: 10px;
            border-radius: 5px;
        }

        #dateLabel, #timeLabel {
            font-size: 16px;
            font-weight: bold;
        }

        #studentInfo {
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }

        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px;
            border-radius: 4px;
            font-weight: bold;
        }

        QPushButton:hover {
            background-color: #2980b9;
        }

        QPushButton:pressed {
            background-color: #21618c;
        }

        QLineEdit, QComboBox, QDateEdit {
            padding: 5px;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
        }

        QLineEdit:focus, QComboBox:focus, QDateEdit:focus {
            border-color: #3498db;
        }

        QTableWidget {
            gridline-color: #bdc3c7;
            background-color: white;
            alternate-background-color: #f8f9fa;
        }

        QTableWidget::item:selected {
            background-color: #3498db;
            color: white;
        }

        QHeaderView::section {
            background-color: #34495e;
            color: white;
            padding: 8px;
            border: none;
            font-weight: bold;
        }
        """

        self.setStyleSheet(style)

    def search_student_by_name(self):
        """البحث عن الطالب بالاسم مع فلترة المجموعة"""
        search_text = self.student_name_input.text().strip()

        if len(search_text) < 2:
            self.search_results_combo.setVisible(False)
            return

        # البحث في قاعدة البيانات
        students = self.student_model.search_students_by_name(search_text)

        # فلترة حسب المجموعة المحددة
        selected_group = self.group_filter_combo.currentText()
        if selected_group != "جميع المجموعات":
            students = [s for s in students if s.get('group_name') == selected_group]

        if students:
            self.search_results_combo.clear()
            self.search_results_combo.addItem("اختر طالب...")

            for student in students:
                group_name = student.get('group_name', 'لا توجد مجموعة')
                display_text = f"{student['full_name']} ({student['student_code']}) - {group_name}"
                self.search_results_combo.addItem(display_text, student)

            self.search_results_combo.setVisible(True)
        else:
            self.search_results_combo.setVisible(False)

    def select_student_from_search(self):
        """اختيار طالب من نتائج البحث"""
        current_index = self.search_results_combo.currentIndex()

        if current_index > 0:  # تجاهل "اختر طالب..."
            student = self.search_results_combo.currentData()
            if student:
                # ملء حقل الكود
                self.student_code_input.setText(student['student_code'])

                # عرض معلومات الطالب
                self.student_info_label.setText(
                    f"الطالب: {student['full_name']} | الصف: {student['grade']} | النوع: {student['gender']}"
                )

                # إخفاء قائمة البحث
                self.search_results_combo.setVisible(False)
                self.student_name_input.clear()

    def search_and_mark_by_name(self):
        """البحث وتسجيل الحضور بالاسم"""
        search_text = self.student_name_input.text().strip()

        if not search_text:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم الطالب")
            return

        # البحث عن الطالب
        students = self.student_model.search_students_by_name(search_text)

        if not students:
            QMessageBox.warning(self, "خطأ", f"لم يتم العثور على طالب بالاسم: {search_text}")
            return
        elif len(students) == 1:
            # طالب واحد فقط، تسجيل مباشر
            student = students[0]
            self.mark_attendance_for_student(student)
        else:
            # عدة طلاب، عرض قائمة للاختيار
            self.search_student_by_name()

    def mark_attendance_for_student(self, student):
        """تسجيل الحضور لطالب محدد"""
        status = self.status_combo.currentText()
        notes = self.notes_input.text().strip()

        if self.attendance_model.mark_attendance_by_code(student['student_code'], date.today(), status, notes):
            QMessageBox.information(self, "نجح", f"تم تسجيل {status} للطالب {student['full_name']}")

            # إرسال رسالة تلقائية في حالة الغياب
            if status == "غائب" and student.get('parent_phone'):
                try:
                    messaging_system.send_absence_notification(
                        student['full_name'],
                        student['parent_phone'],
                        date.today().strftime("%Y-%m-%d")
                    )
                except Exception as e:
                    print(f"خطأ في إرسال رسالة الغياب: {e}")

            # مسح الحقول
            self.student_code_input.clear()
            self.student_name_input.clear()
            self.notes_input.clear()
            self.student_info_label.setText("تم التسجيل بنجاح")
            self.search_results_combo.setVisible(False)

            # تحديث الجداول والإحصائيات
            self.load_today_attendance()
            self.update_daily_stats()
        else:
            QMessageBox.warning(self, "خطأ", "فشل في تسجيل الحضور")

    def populate_group_filter(self):
        """تعبئة قائمة فلتر المجموعات للتسجيل السريع"""
        try:
            groups = self.groups_model.get_group_names()
            self.group_filter_combo.clear()
            self.group_filter_combo.addItem("جميع المجموعات")
            for group in groups:
                self.group_filter_combo.addItem(group)
        except Exception as e:
            print(f"خطأ في تحميل المجموعات: {e}")

    def populate_manage_group_filter(self):
        """تعبئة قائمة فلتر المجموعات لإدارة الحضور"""
        try:
            groups = self.groups_model.get_group_names()
            self.manage_group_filter_combo.clear()
            self.manage_group_filter_combo.addItem("جميع المجموعات")
            for group in groups:
                self.manage_group_filter_combo.addItem(group)
        except Exception as e:
            print(f"خطأ في تحميل المجموعات: {e}")

    def filter_students_by_group(self):
        """فلترة الطلاب حسب المجموعة المحددة"""
        selected_group = self.group_filter_combo.currentText()

        # تحديث البحث بالاسم ليعرض طلاب المجموعة المحددة فقط
        if hasattr(self, 'student_name_input'):
            current_text = self.student_name_input.text()
            if current_text:
                self.search_student_by_name()

    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        if hasattr(self, 'timer'):
            self.timer.stop()
        event.accept()
