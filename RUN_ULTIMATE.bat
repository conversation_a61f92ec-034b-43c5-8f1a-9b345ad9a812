@echo off
chcp 65001 >nul
title نظام إدارة الطلاب - الإصدار النهائي الشامل

cls
color 0F
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة الطلاب                        ║
echo ║             الإصدار النهائي الشامل v5.0                     ║
echo ║                   نظام إدارة الطلاب المتطور                           ║
echo ║              نظام شامل لإدارة الطلاب                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎉 المميزات الجديدة في هذا الإصدار:
echo.
echo ✅ خانة المجموعة في إضافة الطلاب
echo ✅ نافذة إدارة المجموعات الكاملة
echo ✅ نظام رسائل تلقائية للغياب والدرجات
echo ✅ أيقونات WhatsApp و Telegram
echo ✅ تحديث معلومات المطور: Eng / Hossam Osama
echo ✅ إصلاح جميع الخلفيات السوداء لتصبح بيضاء
echo ✅ واجهة محسنة ومتكاملة بالكامل
echo.

echo 🚀 بدء تشغيل النظام الشامل...
echo.

REM محاولة تشغيل البرنامج بطرق مختلفة

REM الطريقة الأولى: py launcher
py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python!
    py --version
    echo.
    echo 🎯 تشغيل نظام إدارة الطلاب الشامل...
    echo.
    py main.py
    goto end
)

REM الطريقة الثانية: python command
python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python!
    python --version
    echo.
    echo 🎯 تشغيل نظام إدارة الطلاب الشامل...
    echo.
    python main.py
    goto end
)

REM الطريقة الثالثة: python3 command
python3 --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python3!
    python3 --version
    echo.
    echo 🎯 تشغيل نظام إدارة الطلاب الشامل...
    echo.
    python3 main.py
    goto end
)

echo ❌ لم يتم العثور على Python!
echo.
echo 💡 الحلول المتاحة:
echo.
echo 1️⃣ تثبيت Python من Microsoft Store:
echo    - افتح Microsoft Store
echo    - ابحث عن "Python"
echo    - ثبت Python 3.9 أو أحدث
echo.
echo 2️⃣ تثبيت Python من الموقع الرسمي:
echo    - اذهب إلى: https://python.org/downloads
echo    - حمل وثبت Python
echo    - تأكد من تحديد "Add Python to PATH"
echo.

echo 🌐 فتح صفحة تحميل Python...
start https://python.org/downloads

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    دليل الاستخدام الشامل                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🧭 شريط التنقل المتقدم:
echo    🏠 الرئيسية - لوحة التحكم الرئيسية
echo    👥 إدارة الطلاب - إضافة وتعديل الطلاب
echo    👨‍👩‍👧‍👦 إدارة المجموعات - تنظيم الطلاب في مجموعات
echo    📋 تسجيل الحضور - تسجيل حضور الطلاب
echo    📊 إدارة الدرجات - إدخال ومتابعة الدرجات
echo    📈 التقارير - تقارير شاملة PDF/Excel
echo    ⚙️ الإعدادات - إعدادات النظام ومعلومات المطور
echo    🚪 تسجيل الخروج - الخروج الآمن
echo.
echo 📝 المميزات الجديدة:
echo.
echo 🔹 إدارة المجموعات:
echo    • إنشاء مجموعات جديدة
echo    • تعديل أسماء المجموعات
echo    • عرض طلاب كل مجموعة
echo    • نقل الطلاب بين المجموعات
echo.
echo 🔹 نظام الرسائل التلقائية:
echo    • إشعارات الغياب لأولياء الأمور
echo    • تهنئة بالدرجات الممتازة
echo    • تنبيهات للدرجات المنخفضة
echo    • إرسال عبر WhatsApp و Telegram
echo.
echo 🔹 أيقونات التواصل:
echo    📱 WhatsApp - إرسال رسائل فورية
echo    ✈️ Telegram - تواصل بديل
echo.
echo 📚 جميع المميزات:
echo    ✅ إدارة الطلاب مع المجموعات
echo    ✅ تسجيل الحضور السريع
echo    ✅ إدارة درجات الجغرافيا والتاريخ
echo    ✅ نظام رسائل تلقائية ذكي
echo    ✅ تقارير احترافية PDF/Excel
echo    ✅ نظام نسخ احتياطي متقدم
echo    ✅ واجهة عربية محسنة بالكامل
echo    ✅ خلفيات بيضاء واضحة
echo    ✅ شريط تنقل متطور
echo    ✅ إدارة مجموعات متكاملة
echo.
echo 🎓 تم التطوير والتحسين بواسطة:
echo    Eng / Hossam Osama
echo    مهندس برمجيات - مطور تطبيقات
echo.
echo 🎯 مصمم خصيصاً لنظام إدارة الطلاب المتطور
echo    نظام شامل لإدارة الطلاب
echo.
echo 📞 للدعم الفني: راجع تبويب "حول التطبيق" في الإعدادات
echo.
echo 🎉 استمتع بالنظام الشامل المتكامل!
echo.
pause
