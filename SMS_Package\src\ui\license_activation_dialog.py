from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QLabel, QLineEdit, 
                             QPushButton, QTabWidget, QWidget, QFormLayout, 
                             QMessageBox, QTextEdit, QGroupBox, QHBoxLayout)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon

from ..licensing.license_manager import LicenseManager, LicenseState
from ..config import APP_ICON

class LicenseActivationDialog(QDialog):
    def __init__(self, license_manager: LicenseManager, parent=None):
        super().__init__(parent)
        self.license_manager = license_manager
        self.setup_ui()
        self.load_styles()
        self.update_license_status()
    
    def setup_ui(self):
        self.setWindowTitle("تفعيل الترخيص")
        self.setWindowIcon(QIcon(APP_ICON))
        self.setMinimumSize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # Title
        title = QLabel("تفعيل الترخيص")
        title.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title.setFont(title_font)
        
        # Status
        self.status_label = QLabel()
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setWordWrap(True)
        
        # Tabs
        self.tabs = QTabWidget()
        self.setup_online_tab()
        self.setup_offline_tab()
        self.setup_demo_tab()
        
        # Buttons
        btn_layout = QHBoxLayout()
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.reject)
        btn_layout.addStretch()
        btn_layout.addWidget(close_btn)
        
        # Add widgets to layout
        layout.addWidget(title)
        layout.addWidget(self.status_label)
        layout.addWidget(self.tabs)
        layout.addLayout(btn_layout)
    
    def setup_online_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        form = QFormLayout()
        self.email_edit = QLineEdit()
        self.license_key_edit = QLineEdit()
        
        form.addRow("البريد الإلكتروني:", self.email_edit)
        form.addRow("كود الترخيص:", self.license_key_edit)
        
        activate_btn = QPushButton("تفعيل")
        activate_btn.clicked.connect(self.activate_online)
        
        layout.addLayout(form)
        layout.addWidget(activate_btn)
        layout.addStretch()
        
        self.tabs.addTab(tab, "التفعيل عبر الإنترنت")
    
    def setup_offline_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Activation code
        code_group = QGroupBox("كود التفعيل")
        code_layout = QVBoxLayout()
        self.activation_code = QTextEdit()
        self.activation_code.setReadOnly(True)
        copy_btn = QPushButton("نسخ")
        copy_btn.clicked.connect(self.copy_activation_code)
        code_layout.addWidget(self.activation_code)
        code_layout.addWidget(copy_btn)
        code_group.setLayout(code_layout)
        
        # License key
        key_group = QGroupBox("كود الترخيص")
        key_layout = QVBoxLayout()
        self.offline_key = QTextEdit()
        activate_btn = QPushButton("تفعيل")
        activate_btn.clicked.connect(self.activate_offline)
        key_layout.addWidget(self.offline_key)
        key_layout.addWidget(activate_btn)
        key_group.setLayout(key_layout)
        
        layout.addWidget(code_group)
        layout.addWidget(key_group)
        layout.addStretch()
        
        self.tabs.addTab(tab, "التفعيل دون اتصال")
    
    def setup_demo_tab(self):
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        info = QLabel(
            "النسخة التجريبية تتيح لك تجربة النظام مع بعض القيود:\n"
            "• 50 طالب كحد أقصى\n"
            "• مستخدم واحد\n"
            "• 14 يوم صلاحية"
        )
        info.setWordWrap(True)
        
        activate_btn = QPushButton("تفعيل النسخة التجريبية")
        activate_btn.clicked.connect(self.activate_demo)
        
        layout.addWidget(info)
        layout.addWidget(activate_btn)
        layout.addStretch()
        
        self.tabs.addTab(tab, "النسخة التجريبية")
    
    def load_styles(self):
        self.setStyleSheet("""
            QDialog { font-family: Arial; }
            QLabel { font-size: 12px; }
            QPushButton { 
                padding: 6px 12px;
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
            }
            QPushButton:disabled { background: #6c757d; }
            QLineEdit, QTextEdit { 
                padding: 6px; 
                border: 1px solid #ced4da;
                border-radius: 4px;
            }
            QGroupBox { 
                border: 1px solid #dee2e6;
                border-radius: 4px;
                margin-top: 10px;
                padding: 10px;
            }
            QGroupBox::title { padding: 0 5px; }
        """)
    
    def update_license_status(self):
        state = self.license_manager.get_license_state()
        info = self.license_manager.get_license_info()
        
        status = f"<b>حالة الترخيص:</b> {state.get_display_name()}"
        
        if state != LicenseState.UNLICENSED:
            if info.get('customer_name'):
                status += f"<br><b>العميل:</b> {info['customer_name']}"
            if info.get('expires_at'):
                expires = info['expires_at'].split('T')[0]
                status += f"<br><b>ينتهي في:</b> {expires}"
                
                days_left = info.get('days_remaining', 0)
                if days_left > 0:
                    status += f" ({days_left} يوم متبقي)"
                
                if state == LicenseState.GRACE:
                    status += "<br><span style='color: #dc3545;'>فترة السماح: 7 أيام</span>"
                elif state == LicenseState.EXPIRED:
                    status += "<br><span style='color: #dc3545;'>انتهت صلاحية الترخيص</span>"
        
        self.status_label.setText(status)
    
    def activate_online(self):
        email = self.email_edit.text().strip()
        key = self.license_key_edit.text().strip()
        
        if not email or not key:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال البريد الإلكتروني وكود الترخيص")
            return
            
        success, message = self.license_manager.activate_online(email, key)
        if success:
            QMessageBox.information(self, "تم", "تم تفعيل الترخيص بنجاح")
            self.update_license_status()
            self.accept()
        else:
            QMessageBox.critical(self, "خطأ", f"فشل التفعيل: {message}")
    
    def activate_offline(self):
        key = self.offline_key.toPlainText().strip()
        if not key:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال كود الترخيص")
            return
            
        success, message = self.license_manager.activate_offline(key)
        if success:
            QMessageBox.information(self, "تم", "تم تفعيل الترخيص بنجاح")
            self.update_license_status()
            self.accept()
        else:
            QMessageBox.critical(self, "خطأ", f"فشل التفعيل: {message}")
    
    def activate_demo(self):
        success, message = self.license_manager.activate_demo()
        if success:
            QMessageBox.information(self, "تم", "تم تفعيل النسخة التجريبية بنجاح")
            self.update_license_status()
            self.accept()
        else:
            QMessageBox.critical(self, "خطأ", f"فشل التفعيل: {message}")
    
    def copy_activation_code(self):
        clipboard = QApplication.clipboard()
        clipboard.setText(self.activation_code.toPlainText())
        QMessageBox.information(self, "تم", "تم نسخ كود التفعيل")
