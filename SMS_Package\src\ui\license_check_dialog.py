from PyQt5.QtWidgets import (QDialog, QVBox<PERSON>ayout, QLabel, QPushButton, 
                             QHBoxLayout, QMessageBox, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon, QPixmap

from ..licensing.license_manager import LicenseManager, LicenseState
from .license_activation_dialog import LicenseActivationDialog
from ..config import APP_ICON, STYLES_DIR

class LicenseCheckDialog(QDialog):
    def __init__(self, license_manager: LicenseManager, parent=None):
        super().__init__(parent)
        self.license_manager = license_manager
        self.setup_ui()
        self.load_styles()
        self.update_status()
    
    def setup_ui(self):
        self.setWindowTitle("التحقق من الترخيص")
        self.setWindowIcon(QIcon(APP_ICON))
        self.setFixedSize(500, 400)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # App logo
        logo_label = QLabel()
        logo_pixmap = QPixmap(APP_ICON).scaled(64, 64, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        logo_label.setPixmap(logo_pixmap)
        logo_label.setAlignment(Qt.AlignCenter)
        
        # Title
        title = QLabel("نظام إدارة المدارس")
        title.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title.setFont(title_font)
        
        # Status
        self.status_icon = QLabel()
        self.status_icon.setAlignment(Qt.AlignCenter)
        
        self.status_text = QLabel()
        self.status_text.setAlignment(Qt.AlignCenter)
        self.status_text.setWordWrap(True)
        
        # License info
        self.license_info = QLabel()
        self.license_info.setAlignment(Qt.AlignRight | Qt.AlignTop)
        self.license_info.setWordWrap(True)
        
        # Buttons
        btn_layout = QHBoxLayout()
        
        self.activate_btn = QPushButton("تفعيل الترخيص")
        self.activate_btn.clicked.connect(self.show_activation_dialog)
        
        self.continue_btn = QPushButton("المتابعة")
        self.continue_btn.clicked.connect(self.accept)
        
        self.quit_btn = QPushButton("خروج")
        self.quit_btn.clicked.connect(self.reject)
        
        btn_layout.addWidget(self.activate_btn)
        btn_layout.addWidget(self.continue_btn)
        btn_layout.addWidget(self.quit_btn)
        
        # Add widgets to layout
        layout.addWidget(logo_label)
        layout.addWidget(title)
        layout.addSpacing(10)
        layout.addWidget(self.status_icon)
        layout.addWidget(self.status_text)
        layout.addWidget(self.license_info)
        layout.addStretch()
        layout.addLayout(btn_layout)
    
    def load_styles(self):
        self.setStyleSheet("""
            QDialog {
                font-family: Arial;
                background-color: #f8f9fa;
            }
            QLabel {
                font-size: 12px;
                color: #212529;
            }
            QPushButton {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                min-width: 100px;
            }
            #statusOk {
                color: #28a745;
                font-weight: bold;
            }
            #statusWarning {
                color: #ffc107;
                font-weight: bold;
            }
            #statusError {
                color: #dc3545;
                font-weight: bold;
            }
        """)
    
    def update_status(self):
        state = self.license_manager.get_license_state()
        info = self.license_manager.get_license_info()
        
        # Set status icon and text based on license state
        if state == LicenseState.ACTIVE:
            self.status_icon.setText("✓")
            self.status_icon.setStyleSheet("font-size: 48px; color: #28a745;")
            self.status_text.setText("<h3 style='color:#28a745;'>الترخيص نشط</h3>")
            self.continue_btn.setEnabled(True)
            self.activate_btn.setVisible(False)
        elif state == LicenseState.DEMO:
            self.status_icon.setText("!")
            self.status_icon.setStyleSheet("font-size: 48px; color: #ffc107;")
            self.status_text.setText(
                "<h3 style='color:#ffc107;'>النسخة التجريبية</h3>"
                f"<p>الأيام المتبقية: {info.get('days_remaining', 0)} يوم</p>"
            )
            self.continue_btn.setEnabled(True)
            self.activate_btn.setVisible(True)
        elif state == LicenseState.GRACE:
            self.status_icon.setText("!")
            self.status_icon.setStyleSheet("font-size: 48px; color: #ffc107;")
            self.status_text.setText(
                "<h3 style='color:#ffc107;'>فترة السماح</h3>"
                "<p>انتهت صلاحية الترخيص. لديك 7 أيام لتجديده.</p>"
            )
            self.continue_btn.setEnabled(True)
            self.activate_btn.setVisible(True)
        elif state == LicenseState.EXPIRED or state == LicenseState.BLOCKED:
            self.status_icon.setText("✗")
            self.status_icon.setStyleSheet("font-size: 48px; color: #dc3545;")
            self.status_text.setText(
                "<h3 style='color:#dc3545;'>انتهت صلاحية الترخيص</h3>"
                "<p>الرجاء تجديد الترخيص لمواصلة الاستخدام.</p>"
            )
            self.continue_btn.setEnabled(False)
            self.activate_btn.setVisible(True)
        else:  # UNLICENSED
            self.status_icon.setText("?")
            self.status_icon.setStyleSheet("font-size: 48px; color: #6c757d;")
            self.status_text.setText(
                "<h3 style='color:#6c757d;'>غير مفعل</h3>"
                "<p>الرجاء تفعيل الترخيص لاستخدام التطبيق.</p>"
            )
            self.continue_btn.setEnabled(False)
            self.activate_btn.setVisible(True)
        
        # Update license info
        info_text = ""
        if state != LicenseState.UNLICENSED:
            if info.get('customer_name'):
                info_text += f"<b>العميل:</b> {info['customer_name']}<br>"
            if info.get('license_type'):
                info_text += f"<b>نوع الترخيص:</b> {info['license_type'].capitalize()}<br>"
            if info.get('issued_at'):
                info_text += f"<b>تاريخ الإصدار:</b> {info['issued_at'].split('T')[0]}<br>"
            if info.get('expires_at'):
                expires = info['expires_at'].split('T')[0]
                info_text += f"<b>تاريخ الانتهاء:</b> {expires}"
        
        self.license_info.setText(info_text)
    
    def show_activation_dialog(self):
        dialog = LicenseActivationDialog(self.license_manager, self)
        if dialog.exec_() == QDialog.Accepted:
            self.update_status()
            # If license is now valid, enable continue button
            if self.license_manager.get_license_state() in [LicenseState.ACTIVE, LicenseState.DEMO, LicenseState.GRACE]:
                self.continue_btn.setEnabled(True)
    
    def showEvent(self, event):
        # Center the dialog on the screen
        screen = self.screen().availableGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
        super().showEvent(event)
