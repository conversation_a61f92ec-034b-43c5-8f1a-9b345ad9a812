import hashlib
import platform
import uuid
import socket
import subprocess
import re
from typing import Dict, Any
import json

def get_mac_address() -> str:
    """
    الحصول على عنوان MAC للجهاز
    """
    try:
        # Windows
        if platform.system() == "Windows":
            output = subprocess.check_output("getmac").decode()
            mac = re.search(r"([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})", output)
            return mac.group(0) if mac else ""
        # Linux
        elif platform.system() == "Linux":
            with open('/sys/class/net/eth0/address') as f:
                return f.read().strip()
        # macOS
        else:
            output = subprocess.check_output(["ifconfig"]).decode()
            mac = re.search(r"ether\s+([0-9a-fA-F:]+)", output)
            return mac.group(1).strip() if mac else ""
    except Exception:
        return ""

def get_cpu_info() -> str:
    """
    الحصول على معلومات المعالج
    """
    try:
        if platform.system() == "Windows":
            return platform.processor()
        elif platform.system() == "Linux":
            with open('/proc/cpuinfo') as f:
                for line in f:
                    if line.strip() and line.partition(':')[0].strip() == 'model name':
                        return line.partition(':')[2].strip()
        elif platform.system() == "Darwin":
            return subprocess.check_output(['sysctl', '-n', 'machdep.cpu.brand_string']).strip().decode()
    except Exception:
        pass
    return platform.processor() or ""

def get_disk_serial() -> str:
    """
    الحصول على الرقم التسلسلي للقرص الصلب
    """
    try:
        if platform.system() == "Windows":
            result = subprocess.check_output(
                'wmic diskdrive get serialnumber', 
                shell=True
            ).decode().strip()
            return result.split('\n')[1] if '\n' in result else ""
        elif platform.system() == "Linux":
            # Try to get the root disk serial
            try:
                disk = subprocess.check_output("lsblk -d -o name,serial | grep -w $(df / --output=source | tail -1 | cut -d'/' -f3) | awk '{print $2}'", 
                                             shell=True).decode().strip()
                return disk if disk else ""
            except:
                pass
            
            # Fallback to disk ID
            disk_id = subprocess.check_output(
                "lsblk -d -o name,uuid | grep -w $(df / --output=source | tail -1 | cut -d'/' -f3) | awk '{print $2}'", 
                shell=True
            ).decode().strip()
            return disk_id if disk_id else ""
    except Exception:
        pass
    return ""

def get_system_info() -> Dict[str, str]:
    """
    جمع معلومات النظام
    """
    return {
        "system": platform.system(),
        "node": platform.node(),
        "release": platform.release(),
        "version": platform.version(),
        "machine": platform.machine(),
        "processor": get_cpu_info(),
        "mac_address": get_mac_address(),
        "disk_serial": get_disk_serial(),
        "hostname": socket.gethostname(),
    }

def generate_device_fingerprint() -> str:
    """
    إنشاء بصمة فريدة للجهاز
    """
    system_info = get_system_info()
    # استخدام معلومات النظام لإنشاء بصمة فريدة
    fingerprint_data = {
        "machine": system_info["machine"],
        "mac": system_info["mac_address"],
        "disk": system_info["disk_serial"],
        "hostname": system_info["hostname"]
    }
    
    # تحويل القاموس إلى سلسلة نصية ثم إنشاء بصمة فريدة
    fingerprint_str = json.dumps(fingerprint_data, sort_keys=True)
    return hashlib.sha256(fingerprint_str.encode('utf-8')).hexdigest()

# تخزين مؤقت للبصمة
_device_fingerprint = None

def get_device_fingerprint() -> str:
    """
    الحصول على بصمة الجهاز (تخزين مؤقت)
    """
    global _device_fingerprint
    if _device_fingerprint is None:
        _device_fingerprint = generate_device_fingerprint()
    return _device_fingerprint
