# -*- coding: utf-8 -*-
"""
مدير التحديث التلقائي
Auto Update Manager
"""

import os
import json
import logging
import requests
import zipfile
import shutil
import subprocess
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import hashlib
import tempfile
from packaging import version

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UpdateManager:
    """مدير التحديث التلقائي"""
    
    def __init__(self, config_file="update_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
        self.current_version = self.get_current_version()
        self.update_thread = None
        self.is_checking = False
        
        # معلومات التحديث
        self.update_info = None
        self.download_progress = 0
        
        # مسارات مهمة
        self.app_directory = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        self.backup_directory = os.path.join(self.app_directory, "backups", "updates")
        self.temp_directory = tempfile.mkdtemp(prefix="sms_update_")
        
        # إنشاء مجلدات النسخ الاحتياطية
        os.makedirs(self.backup_directory, exist_ok=True)
    
    def load_config(self) -> Dict[str, Any]:
        """تحميل إعدادات التحديث"""
        default_config = {
            "enabled": True,
            "auto_check": True,
            "check_interval_hours": 24,
            "update_server_url": "https://api.github.com/repos/your-repo/sms/releases",
            "backup_before_update": True,
            "restart_after_update": True,
            "beta_updates": False,
            "notification_enabled": True,
            "last_check": None,
            "ignored_versions": []
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # دمج مع الإعدادات الافتراضية
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            except Exception as e:
                logger.warning(f"فشل في تحميل إعدادات التحديث: {e}")
        
        # حفظ الإعدادات الافتراضية
        self.save_config(default_config)
        return default_config
    
    def save_config(self, config: Dict[str, Any]):
        """حفظ إعدادات التحديث"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"فشل في حفظ إعدادات التحديث: {e}")
    
    def get_current_version(self) -> str:
        """الحصول على الإصدار الحالي"""
        try:
            version_file = os.path.join(self.app_directory, "version_info.txt")
            if os.path.exists(version_file):
                with open(version_file, 'r', encoding='utf-8') as f:
                    return f.read().strip()
            else:
                # إنشاء ملف الإصدار إذا لم يكن موجوداً
                default_version = "1.0.0"
                with open(version_file, 'w', encoding='utf-8') as f:
                    f.write(default_version)
                return default_version
        except Exception as e:
            logger.error(f"فشل في الحصول على الإصدار الحالي: {e}")
            return "1.0.0"
    
    def check_for_updates(self) -> Optional[Dict[str, Any]]:
        """فحص التحديثات المتاحة"""
        try:
            logger.info("فحص التحديثات المتاحة...")
            
            # تحديث وقت آخر فحص
            self.config["last_check"] = datetime.now().isoformat()
            self.save_config(self.config)
            
            # الحصول على معلومات التحديثات من الخادم
            update_url = self.config.get("update_server_url", "")
            if not update_url:
                logger.warning("لم يتم تعيين رابط خادم التحديثات")
                return None
            
            response = requests.get(update_url, timeout=30)
            response.raise_for_status()
            
            releases = response.json()
            if not releases:
                logger.info("لا توجد إصدارات متاحة")
                return None
            
            # البحث عن أحدث إصدار
            latest_release = None
            for release in releases:
                if release.get("draft", False):
                    continue
                
                # تخطي الإصدارات التجريبية إذا لم تكن مفعلة
                if release.get("prerelease", False) and not self.config.get("beta_updates", False):
                    continue
                
                # تخطي الإصدارات المتجاهلة
                if release.get("tag_name", "") in self.config.get("ignored_versions", []):
                    continue
                
                latest_release = release
                break
            
            if not latest_release:
                logger.info("لا توجد تحديثات متاحة")
                return None
            
            latest_version = latest_release.get("tag_name", "").lstrip("v")
            
            # مقارنة الإصدارات
            if version.parse(latest_version) > version.parse(self.current_version):
                self.update_info = {
                    "version": latest_version,
                    "name": latest_release.get("name", ""),
                    "description": latest_release.get("body", ""),
                    "download_url": self._get_download_url(latest_release),
                    "size": self._get_download_size(latest_release),
                    "published_at": latest_release.get("published_at", ""),
                    "is_prerelease": latest_release.get("prerelease", False)
                }
                
                logger.info(f"تحديث متاح: {latest_version}")
                return self.update_info
            else:
                logger.info("النظام محدث لأحدث إصدار")
                return None
                
        except Exception as e:
            logger.error(f"فشل في فحص التحديثات: {e}")
            return None
    
    def _get_download_url(self, release: Dict[str, Any]) -> Optional[str]:
        """الحصول على رابط التحميل"""
        try:
            assets = release.get("assets", [])
            for asset in assets:
                name = asset.get("name", "").lower()
                if name.endswith(".zip") and "sms" in name:
                    return asset.get("browser_download_url")
            
            # إذا لم نجد ملف ZIP، نستخدم zipball_url
            return release.get("zipball_url")
            
        except Exception as e:
            logger.error(f"فشل في الحصول على رابط التحميل: {e}")
            return None
    
    def _get_download_size(self, release: Dict[str, Any]) -> int:
        """الحصول على حجم التحميل"""
        try:
            assets = release.get("assets", [])
            for asset in assets:
                name = asset.get("name", "").lower()
                if name.endswith(".zip") and "sms" in name:
                    return asset.get("size", 0)
            return 0
        except Exception as e:
            logger.error(f"فشل في الحصول على حجم التحميل: {e}")
            return 0
    
    def download_update(self, progress_callback=None) -> bool:
        """تحميل التحديث"""
        try:
            if not self.update_info:
                logger.error("لا توجد معلومات تحديث")
                return False
            
            download_url = self.update_info.get("download_url")
            if not download_url:
                logger.error("رابط التحميل غير متاح")
                return False
            
            logger.info(f"تحميل التحديث من: {download_url}")
            
            # تحميل الملف
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            update_file = os.path.join(self.temp_directory, "update.zip")
            
            with open(update_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # تحديث التقدم
                        if total_size > 0:
                            self.download_progress = (downloaded_size / total_size) * 100
                            if progress_callback:
                                progress_callback(self.download_progress)
            
            logger.info("تم تحميل التحديث بنجاح")
            
            # التحقق من سلامة الملف
            if self._verify_download(update_file):
                return True
            else:
                logger.error("فشل في التحقق من سلامة الملف المحمل")
                return False
                
        except Exception as e:
            logger.error(f"فشل في تحميل التحديث: {e}")
            return False
    
    def _verify_download(self, file_path: str) -> bool:
        """التحقق من سلامة الملف المحمل"""
        try:
            # فحص إذا كان الملف ZIP صالح
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                # اختبار سلامة الأرشيف
                zip_file.testzip()
                
                # فحص وجود ملفات مهمة
                file_list = zip_file.namelist()
                required_files = ["main.py", "requirements.txt"]
                
                for required_file in required_files:
                    if not any(required_file in f for f in file_list):
                        logger.warning(f"الملف المطلوب غير موجود: {required_file}")
                        return False
            
            logger.info("تم التحقق من سلامة الملف بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"فشل في التحقق من سلامة الملف: {e}")
            return False
    
    def create_backup(self) -> bool:
        """إنشاء نسخة احتياطية قبل التحديث"""
        try:
            if not self.config.get("backup_before_update", True):
                return True
            
            logger.info("إنشاء نسخة احتياطية...")
            
            backup_name = f"backup_{self.current_version}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            backup_path = os.path.join(self.backup_directory, f"{backup_name}.zip")
            
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as backup_zip:
                for root, dirs, files in os.walk(self.app_directory):
                    # تخطي مجلدات معينة
                    dirs[:] = [d for d in dirs if d not in ['__pycache__', '.git', 'backups', 'temp']]
                    
                    for file in files:
                        if file.endswith(('.pyc', '.pyo', '.log')):
                            continue
                        
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, self.app_directory)
                        backup_zip.write(file_path, arcname)
            
            logger.info(f"تم إنشاء النسخة الاحتياطية: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"فشل في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def apply_update(self) -> bool:
        """تطبيق التحديث"""
        try:
            if not self.update_info:
                logger.error("لا توجد معلومات تحديث")
                return False
            
            logger.info("تطبيق التحديث...")
            
            update_file = os.path.join(self.temp_directory, "update.zip")
            if not os.path.exists(update_file):
                logger.error("ملف التحديث غير موجود")
                return False
            
            # إنشاء نسخة احتياطية
            if not self.create_backup():
                logger.error("فشل في إنشاء النسخة الاحتياطية")
                return False
            
            # استخراج التحديث
            extract_path = os.path.join(self.temp_directory, "extracted")
            with zipfile.ZipFile(update_file, 'r') as zip_file:
                zip_file.extractall(extract_path)
            
            # العثور على مجلد التحديث الرئيسي
            update_source = self._find_update_source(extract_path)
            if not update_source:
                logger.error("لم يتم العثور على مجلد التحديث")
                return False
            
            # نسخ الملفات الجديدة
            self._copy_update_files(update_source, self.app_directory)
            
            # تحديث ملف الإصدار
            version_file = os.path.join(self.app_directory, "version_info.txt")
            with open(version_file, 'w', encoding='utf-8') as f:
                f.write(self.update_info["version"])
            
            logger.info("تم تطبيق التحديث بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"فشل في تطبيق التحديث: {e}")
            return False
    
    def _find_update_source(self, extract_path: str) -> Optional[str]:
        """العثور على مجلد المصدر في التحديث المستخرج"""
        try:
            # البحث عن main.py في المجلدات المستخرجة
            for root, dirs, files in os.walk(extract_path):
                if "main.py" in files:
                    return root
            return None
        except Exception as e:
            logger.error(f"فشل في العثور على مجلد التحديث: {e}")
            return None
    
    def _copy_update_files(self, source: str, destination: str):
        """نسخ ملفات التحديث"""
        try:
            for root, dirs, files in os.walk(source):
                # تخطي مجلدات معينة
                dirs[:] = [d for d in dirs if d not in ['__pycache__', '.git', 'backups', 'temp']]
                
                for file in files:
                    if file.endswith(('.pyc', '.pyo')):
                        continue
                    
                    src_file = os.path.join(root, file)
                    rel_path = os.path.relpath(src_file, source)
                    dst_file = os.path.join(destination, rel_path)
                    
                    # إنشاء المجلد إذا لم يكن موجوداً
                    os.makedirs(os.path.dirname(dst_file), exist_ok=True)
                    
                    # نسخ الملف
                    shutil.copy2(src_file, dst_file)
                    
        except Exception as e:
            logger.error(f"فشل في نسخ ملفات التحديث: {e}")
            raise
    
    def rollback_update(self, backup_name: str) -> bool:
        """التراجع عن التحديث"""
        try:
            logger.info(f"التراجع عن التحديث باستخدام النسخة الاحتياطية: {backup_name}")
            
            backup_path = os.path.join(self.backup_directory, f"{backup_name}.zip")
            if not os.path.exists(backup_path):
                logger.error(f"النسخة الاحتياطية غير موجودة: {backup_path}")
                return False
            
            # استخراج النسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'r') as backup_zip:
                backup_zip.extractall(self.app_directory)
            
            logger.info("تم التراجع عن التحديث بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"فشل في التراجع عن التحديث: {e}")
            return False
    
    def start_auto_check(self):
        """بدء فحص التحديثات التلقائي"""
        if not self.config.get("auto_check", True):
            return
        
        if self.update_thread and self.update_thread.is_alive():
            return
        
        self.is_checking = True
        self.update_thread = threading.Thread(target=self._auto_check_loop, daemon=True)
        self.update_thread.start()
        logger.info("تم بدء فحص التحديثات التلقائي")
    
    def stop_auto_check(self):
        """إيقاف فحص التحديثات التلقائي"""
        self.is_checking = False
        if self.update_thread:
            self.update_thread.join(timeout=5)
        logger.info("تم إيقاف فحص التحديثات التلقائي")
    
    def _auto_check_loop(self):
        """حلقة فحص التحديثات التلقائي"""
        check_interval = self.config.get("check_interval_hours", 24) * 3600
        
        while self.is_checking:
            try:
                # فحص إذا كان الوقت مناسب للفحص
                last_check = self.config.get("last_check")
                if last_check:
                    last_check_time = datetime.fromisoformat(last_check)
                    if datetime.now() - last_check_time < timedelta(seconds=check_interval):
                        time.sleep(3600)  # انتظار ساعة
                        continue
                
                # فحص التحديثات
                update_info = self.check_for_updates()
                if update_info and self.config.get("notification_enabled", True):
                    # إرسال إشعار (يمكن ربطه مع نظام الإشعارات)
                    logger.info(f"تحديث جديد متاح: {update_info['version']}")
                
                time.sleep(check_interval)
                
            except Exception as e:
                logger.error(f"خطأ في حلقة فحص التحديثات: {e}")
                time.sleep(3600)  # انتظار ساعة قبل المحاولة مرة أخرى
    
    def get_available_backups(self) -> List[Dict[str, Any]]:
        """الحصول على قائمة النسخ الاحتياطية المتاحة"""
        try:
            backups = []
            
            if not os.path.exists(self.backup_directory):
                return backups
            
            for file in os.listdir(self.backup_directory):
                if file.endswith('.zip') and file.startswith('backup_'):
                    file_path = os.path.join(self.backup_directory, file)
                    stat = os.stat(file_path)
                    
                    backups.append({
                        'name': file[:-4],  # إزالة .zip
                        'file_path': file_path,
                        'size': stat.st_size,
                        'created_at': datetime.fromtimestamp(stat.st_ctime),
                        'version': file.split('_')[1] if len(file.split('_')) > 1 else 'unknown'
                    })
            
            # ترتيب حسب تاريخ الإنشاء
            backups.sort(key=lambda x: x['created_at'], reverse=True)
            return backups
            
        except Exception as e:
            logger.error(f"فشل في الحصول على قائمة النسخ الاحتياطية: {e}")
            return []
    
    def cleanup_old_backups(self, keep_count: int = 5):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backups = self.get_available_backups()
            
            if len(backups) <= keep_count:
                return
            
            # حذف النسخ الزائدة
            for backup in backups[keep_count:]:
                os.remove(backup['file_path'])
                logger.info(f"تم حذف النسخة الاحتياطية القديمة: {backup['name']}")
                
        except Exception as e:
            logger.error(f"فشل في تنظيف النسخ الاحتياطية القديمة: {e}")
    
    def get_update_status(self) -> Dict[str, Any]:
        """الحصول على حالة التحديث"""
        return {
            "current_version": self.current_version,
            "update_available": self.update_info is not None,
            "update_info": self.update_info,
            "auto_check_enabled": self.config.get("auto_check", True),
            "is_checking": self.is_checking,
            "last_check": self.config.get("last_check"),
            "download_progress": self.download_progress
        }
    
    def cleanup(self):
        """تنظيف الملفات المؤقتة"""
        try:
            if os.path.exists(self.temp_directory):
                shutil.rmtree(self.temp_directory)
        except Exception as e:
            logger.error(f"فشل في تنظيف الملفات المؤقتة: {e}")

# دالة مساعدة لإنشاء مدير التحديث
def create_update_manager(config_file="update_config.json"):
    """إنشاء مدير التحديث التلقائي"""
    return UpdateManager(config_file)
