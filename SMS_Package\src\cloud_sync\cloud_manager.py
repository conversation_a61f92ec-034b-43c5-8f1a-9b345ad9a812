# -*- coding: utf-8 -*-
"""
مدير المزامنة السحابية
Cloud Sync Manager
"""

import os
import json
import logging
import threading
import time
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod
import zipfile
import tempfile

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CloudProvider(ABC):
    """واجهة موفر الخدمة السحابية"""
    
    @abstractmethod
    def upload_file(self, local_path: str, remote_path: str) -> bool:
        """رفع ملف للسحابة"""
        pass
    
    @abstractmethod
    def download_file(self, remote_path: str, local_path: str) -> bool:
        """تحميل ملف من السحابة"""
        pass
    
    @abstractmethod
    def list_files(self, remote_path: str = "") -> List[Dict[str, Any]]:
        """قائمة الملفات في السحابة"""
        pass
    
    @abstractmethod
    def delete_file(self, remote_path: str) -> bool:
        """حذف ملف من السحابة"""
        pass
    
    @abstractmethod
    def test_connection(self) -> bool:
        """اختبار الاتصال"""
        pass

class GoogleDriveProvider(CloudProvider):
    """موفر Google Drive"""
    
    def __init__(self, credentials_path: str):
        self.credentials_path = credentials_path
        self.service = None
        self._initialize()
    
    def _initialize(self):
        """تهيئة خدمة Google Drive"""
        try:
            from google.oauth2.credentials import Credentials
            from google_auth_oauthlib.flow import InstalledAppFlow
            from google.auth.transport.requests import Request
            from googleapiclient.discovery import build
            
            SCOPES = ['https://www.googleapis.com/auth/drive.file']
            
            creds = None
            token_path = 'token.json'
            
            if os.path.exists(token_path):
                creds = Credentials.from_authorized_user_file(token_path, SCOPES)
            
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    creds.refresh(Request())
                else:
                    flow = InstalledAppFlow.from_client_secrets_file(
                        self.credentials_path, SCOPES)
                    creds = flow.run_local_server(port=0)
                
                with open(token_path, 'w') as token:
                    token.write(creds.to_json())
            
            self.service = build('drive', 'v3', credentials=creds)
            logger.info("تم تهيئة Google Drive بنجاح")
            
        except Exception as e:
            logger.error(f"فشل في تهيئة Google Drive: {e}")
            self.service = None
    
    def upload_file(self, local_path: str, remote_path: str) -> bool:
        """رفع ملف لـ Google Drive"""
        try:
            if not self.service:
                return False
            
            from googleapiclient.http import MediaFileUpload
            
            file_metadata = {'name': os.path.basename(remote_path)}
            media = MediaFileUpload(local_path, resumable=True)
            
            file = self.service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id'
            ).execute()
            
            logger.info(f"تم رفع الملف بنجاح: {remote_path}")
            return True
            
        except Exception as e:
            logger.error(f"فشل في رفع الملف: {e}")
            return False
    
    def download_file(self, remote_path: str, local_path: str) -> bool:
        """تحميل ملف من Google Drive"""
        try:
            if not self.service:
                return False
            
            # البحث عن الملف
            results = self.service.files().list(
                q=f"name='{os.path.basename(remote_path)}'",
                fields="files(id, name)"
            ).execute()
            
            files = results.get('files', [])
            if not files:
                logger.warning(f"الملف غير موجود: {remote_path}")
                return False
            
            file_id = files[0]['id']
            
            # تحميل الملف
            import io
            from googleapiclient.http import MediaIoBaseDownload
            
            request = self.service.files().get_media(fileId=file_id)
            fh = io.BytesIO()
            downloader = MediaIoBaseDownload(fh, request)
            
            done = False
            while done is False:
                status, done = downloader.next_chunk()
            
            # حفظ الملف
            with open(local_path, 'wb') as f:
                f.write(fh.getvalue())
            
            logger.info(f"تم تحميل الملف بنجاح: {local_path}")
            return True
            
        except Exception as e:
            logger.error(f"فشل في تحميل الملف: {e}")
            return False
    
    def list_files(self, remote_path: str = "") -> List[Dict[str, Any]]:
        """قائمة الملفات في Google Drive"""
        try:
            if not self.service:
                return []
            
            results = self.service.files().list(
                fields="files(id, name, size, modifiedTime)"
            ).execute()
            
            return results.get('files', [])
            
        except Exception as e:
            logger.error(f"فشل في الحصول على قائمة الملفات: {e}")
            return []
    
    def delete_file(self, remote_path: str) -> bool:
        """حذف ملف من Google Drive"""
        try:
            if not self.service:
                return False
            
            # البحث عن الملف
            results = self.service.files().list(
                q=f"name='{os.path.basename(remote_path)}'",
                fields="files(id, name)"
            ).execute()
            
            files = results.get('files', [])
            if not files:
                return True  # الملف غير موجود أصلاً
            
            file_id = files[0]['id']
            self.service.files().delete(fileId=file_id).execute()
            
            logger.info(f"تم حذف الملف: {remote_path}")
            return True
            
        except Exception as e:
            logger.error(f"فشل في حذف الملف: {e}")
            return False
    
    def test_connection(self) -> bool:
        """اختبار الاتصال بـ Google Drive"""
        try:
            if not self.service:
                return False
            
            self.service.files().list(pageSize=1).execute()
            return True
            
        except Exception as e:
            logger.error(f"فشل اختبار الاتصال: {e}")
            return False

class DropboxProvider(CloudProvider):
    """موفر Dropbox"""
    
    def __init__(self, access_token: str):
        self.access_token = access_token
        self.client = None
        self._initialize()
    
    def _initialize(self):
        """تهيئة Dropbox"""
        try:
            import dropbox
            self.client = dropbox.Dropbox(self.access_token)
            logger.info("تم تهيئة Dropbox بنجاح")
        except Exception as e:
            logger.error(f"فشل في تهيئة Dropbox: {e}")
            self.client = None
    
    def upload_file(self, local_path: str, remote_path: str) -> bool:
        """رفع ملف لـ Dropbox"""
        try:
            if not self.client:
                return False
            
            with open(local_path, 'rb') as f:
                self.client.files_upload(f.read(), f"/{remote_path}")
            
            logger.info(f"تم رفع الملف بنجاح: {remote_path}")
            return True
            
        except Exception as e:
            logger.error(f"فشل في رفع الملف: {e}")
            return False
    
    def download_file(self, remote_path: str, local_path: str) -> bool:
        """تحميل ملف من Dropbox"""
        try:
            if not self.client:
                return False
            
            metadata, response = self.client.files_download(f"/{remote_path}")
            
            with open(local_path, 'wb') as f:
                f.write(response.content)
            
            logger.info(f"تم تحميل الملف بنجاح: {local_path}")
            return True
            
        except Exception as e:
            logger.error(f"فشل في تحميل الملف: {e}")
            return False
    
    def list_files(self, remote_path: str = "") -> List[Dict[str, Any]]:
        """قائمة الملفات في Dropbox"""
        try:
            if not self.client:
                return []
            
            result = self.client.files_list_folder(f"/{remote_path}")
            files = []
            
            for entry in result.entries:
                files.append({
                    'name': entry.name,
                    'size': getattr(entry, 'size', 0),
                    'modified_time': getattr(entry, 'client_modified', None)
                })
            
            return files
            
        except Exception as e:
            logger.error(f"فشل في الحصول على قائمة الملفات: {e}")
            return []
    
    def delete_file(self, remote_path: str) -> bool:
        """حذف ملف من Dropbox"""
        try:
            if not self.client:
                return False
            
            self.client.files_delete_v2(f"/{remote_path}")
            logger.info(f"تم حذف الملف: {remote_path}")
            return True
            
        except Exception as e:
            logger.error(f"فشل في حذف الملف: {e}")
            return False
    
    def test_connection(self) -> bool:
        """اختبار الاتصال بـ Dropbox"""
        try:
            if not self.client:
                return False
            
            self.client.users_get_current_account()
            return True
            
        except Exception as e:
            logger.error(f"فشل اختبار الاتصال: {e}")
            return False

class CloudSyncManager:
    """مدير المزامنة السحابية"""
    
    def __init__(self, config_file="cloud_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
        self.providers = {}
        self.sync_thread = None
        self.is_syncing = False
        
        # تهيئة الموفرين
        self._initialize_providers()
    
    def load_config(self) -> Dict[str, Any]:
        """تحميل إعدادات المزامنة السحابية"""
        default_config = {
            "enabled": False,
            "auto_sync_interval": 3600,  # كل ساعة
            "backup_retention_days": 30,
            "providers": {
                "google_drive": {
                    "enabled": False,
                    "credentials_path": "credentials.json"
                },
                "dropbox": {
                    "enabled": False,
                    "access_token": ""
                }
            },
            "sync_paths": {
                "database": "data/students.db",
                "exports": "exports/",
                "logs": "logs/"
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # دمج مع الإعدادات الافتراضية
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            except Exception as e:
                logger.warning(f"فشل في تحميل إعدادات المزامنة: {e}")
        
        # حفظ الإعدادات الافتراضية
        self.save_config(default_config)
        return default_config
    
    def save_config(self, config: Dict[str, Any]):
        """حفظ إعدادات المزامنة السحابية"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"فشل في حفظ إعدادات المزامنة: {e}")
    
    def _initialize_providers(self):
        """تهيئة موفري الخدمة السحابية"""
        try:
            providers_config = self.config.get("providers", {})
            
            # Google Drive
            if providers_config.get("google_drive", {}).get("enabled", False):
                credentials_path = providers_config["google_drive"]["credentials_path"]
                if os.path.exists(credentials_path):
                    self.providers["google_drive"] = GoogleDriveProvider(credentials_path)
            
            # Dropbox
            if providers_config.get("dropbox", {}).get("enabled", False):
                access_token = providers_config["dropbox"]["access_token"]
                if access_token:
                    self.providers["dropbox"] = DropboxProvider(access_token)
            
            logger.info(f"تم تهيئة {len(self.providers)} موفر سحابي")
            
        except Exception as e:
            logger.error(f"فشل في تهيئة الموفرين السحابيين: {e}")
    
    def start_auto_sync(self):
        """بدء المزامنة التلقائية"""
        if not self.config.get("enabled", False):
            logger.info("المزامنة السحابية معطلة")
            return
        
        if self.sync_thread and self.sync_thread.is_alive():
            logger.warning("المزامنة التلقائية تعمل بالفعل")
            return
        
        self.is_syncing = True
        self.sync_thread = threading.Thread(target=self._sync_loop, daemon=True)
        self.sync_thread.start()
        logger.info("تم بدء المزامنة التلقائية")
    
    def stop_auto_sync(self):
        """إيقاف المزامنة التلقائية"""
        self.is_syncing = False
        if self.sync_thread:
            self.sync_thread.join(timeout=5)
        logger.info("تم إيقاف المزامنة التلقائية")
    
    def _sync_loop(self):
        """حلقة المزامنة التلقائية"""
        interval = self.config.get("auto_sync_interval", 3600)
        
        while self.is_syncing:
            try:
                self.sync_all()
                time.sleep(interval)
            except Exception as e:
                logger.error(f"خطأ في حلقة المزامنة: {e}")
                time.sleep(60)  # انتظار دقيقة قبل المحاولة مرة أخرى
    
    def sync_all(self):
        """مزامنة جميع الملفات"""
        if not self.providers:
            logger.warning("لا توجد موفرين سحابيين متاحين")
            return
        
        logger.info("بدء مزامنة جميع الملفات")
        
        sync_paths = self.config.get("sync_paths", {})
        
        for path_name, local_path in sync_paths.items():
            if os.path.exists(local_path):
                self.backup_file(local_path, f"backup_{path_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        
        # تنظيف النسخ الاحتياطية القديمة
        self.cleanup_old_backups()
        
        logger.info("تم الانتهاء من المزامنة")
    
    def backup_file(self, local_path: str, remote_name: str):
        """إنشاء نسخة احتياطية من ملف"""
        try:
            # إنشاء أرشيف مضغوط إذا كان مجلد
            if os.path.isdir(local_path):
                with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_file:
                    with zipfile.ZipFile(temp_file.name, 'w', zipfile.ZIP_DEFLATED) as zipf:
                        for root, dirs, files in os.walk(local_path):
                            for file in files:
                                file_path = os.path.join(root, file)
                                arcname = os.path.relpath(file_path, local_path)
                                zipf.write(file_path, arcname)
                    
                    backup_path = temp_file.name
            else:
                backup_path = local_path
            
            # رفع للموفرين المتاحين
            for provider_name, provider in self.providers.items():
                try:
                    remote_path = f"{remote_name}_{provider_name}"
                    if os.path.isdir(local_path):
                        remote_path += ".zip"
                    
                    if provider.upload_file(backup_path, remote_path):
                        logger.info(f"تم رفع النسخة الاحتياطية لـ {provider_name}: {remote_path}")
                    else:
                        logger.error(f"فشل في رفع النسخة الاحتياطية لـ {provider_name}")
                        
                except Exception as e:
                    logger.error(f"خطأ في رفع النسخة الاحتياطية لـ {provider_name}: {e}")
            
            # حذف الملف المؤقت
            if os.path.isdir(local_path) and os.path.exists(backup_path):
                os.unlink(backup_path)
                
        except Exception as e:
            logger.error(f"فشل في إنشاء نسخة احتياطية: {e}")
    
    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            retention_days = self.config.get("backup_retention_days", 30)
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            for provider_name, provider in self.providers.items():
                try:
                    files = provider.list_files()
                    for file_info in files:
                        # فحص تاريخ الملف
                        if 'modified_time' in file_info and file_info['modified_time']:
                            file_date = datetime.fromisoformat(file_info['modified_time'].replace('Z', '+00:00'))
                            if file_date < cutoff_date and file_info['name'].startswith('backup_'):
                                if provider.delete_file(file_info['name']):
                                    logger.info(f"تم حذف النسخة الاحتياطية القديمة: {file_info['name']}")
                                    
                except Exception as e:
                    logger.error(f"خطأ في تنظيف النسخ الاحتياطية لـ {provider_name}: {e}")
                    
        except Exception as e:
            logger.error(f"فشل في تنظيف النسخ الاحتياطية القديمة: {e}")
    
    def test_all_connections(self) -> Dict[str, bool]:
        """اختبار جميع الاتصالات السحابية"""
        results = {}
        
        for provider_name, provider in self.providers.items():
            try:
                results[provider_name] = provider.test_connection()
            except Exception as e:
                logger.error(f"فشل اختبار الاتصال لـ {provider_name}: {e}")
                results[provider_name] = False
        
        return results
    
    def get_sync_status(self) -> Dict[str, Any]:
        """الحصول على حالة المزامنة"""
        return {
            "enabled": self.config.get("enabled", False),
            "is_syncing": self.is_syncing,
            "providers_count": len(self.providers),
            "providers": list(self.providers.keys()),
            "last_sync": "غير متاح",  # يمكن إضافة تتبع آخر مزامنة
            "auto_sync_interval": self.config.get("auto_sync_interval", 3600)
        }

# دالة مساعدة لإنشاء مدير المزامنة السحابية
def create_cloud_sync_manager(config_file="cloud_config.json"):
    """إنشاء مدير مزامنة سحابية"""
    return CloudSyncManager(config_file)
