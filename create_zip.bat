@echo off
chcp 65001 >nul
title ضغط نظام إدارة الطلاب

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ضغط نظام إدارة الطلاب                    ║
echo ║                   نظام إدارة الطلاب المتطور                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM إنشاء اسم الملف المضغوط مع التاريخ والوقت
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "datestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

set "ZIP_NAME=StudentManagementSystem_%datestamp%.zip"

echo 📦 اسم الملف المضغوط: %ZIP_NAME%
echo 📁 مجلد المشروع: %CD%
echo.

REM التحقق من وجود PowerShell
powershell -Command "Get-Command Compress-Archive" >nul 2>&1
if errorlevel 1 (
    echo ❌ PowerShell غير متوفر أو لا يدعم Compress-Archive
    echo سيتم استخدام طريقة بديلة...
    goto manual_zip
)

echo 🗜️ بدء ضغط المشروع باستخدام PowerShell...
echo.

REM إنشاء قائمة الملفات والمجلدات للضغط
echo 📄 الملفات المطلوب ضغطها:
echo   - main.py
echo   - run.py
echo   - launch.py
echo   - config.py
echo   - requirements.txt
echo   - run_app.bat
echo   - start.bat
echo   - build.bat
echo   - fix_imports.py
echo   - test_app.py
echo   - build_exe.py
echo   - compress_project.py
echo   - README.md
echo   - README_FINAL.md
echo   - USER_GUIDE.md
echo   - INSTALLATION.md
echo   - PROJECT_SUMMARY.md
echo   - src\ (مجلد كامل)
echo   - data\ (مجلد كامل)
echo   - assets\ (مجلد كامل)
echo.

REM إنشاء مجلد مؤقت للضغط
set "TEMP_DIR=temp_for_zip"
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"
mkdir "%TEMP_DIR%"

echo 📋 نسخ الملفات إلى المجلد المؤقت...

REM نسخ الملفات الرئيسية
for %%f in (main.py run.py launch.py config.py requirements.txt run_app.bat start.bat build.bat fix_imports.py test_app.py build_exe.py compress_project.py README.md README_FINAL.md USER_GUIDE.md INSTALLATION.md PROJECT_SUMMARY.md) do (
    if exist "%%f" (
        copy "%%f" "%TEMP_DIR%\" >nul 2>&1
        echo   ✅ %%f
    ) else (
        echo   ⚠️ %%f (غير موجود)
    )
)

REM نسخ المجلدات
echo.
echo 📁 نسخ المجلدات...
for %%d in (src data assets) do (
    if exist "%%d" (
        xcopy "%%d" "%TEMP_DIR%\%%d\" /E /I /Q >nul 2>&1
        echo   ✅ %%d\
    ) else (
        echo   ⚠️ %%d\ (غير موجود)
    )
)

REM إنشاء المجلدات الفارغة
echo.
echo 📂 إنشاء المجلدات الفارغة...
for %%d in (logs backups exports temp) do (
    mkdir "%TEMP_DIR%\%%d" >nul 2>&1
    echo. > "%TEMP_DIR%\%%d\.gitkeep"
    echo   ✅ %%d\
)

REM إنشاء ملف معلومات المشروع
echo.
echo 📝 إنشاء ملف معلومات المشروع...
(
echo # معلومات المشروع
echo.
echo ## نظام إدارة الطلاب لنظام إدارة الطلاب المتطور
echo **تاريخ الضغط:** %date% %time%
echo **الإصدار:** 1.0.0
echo.
echo ## طريقة التشغيل:
echo 1. استخرج الملفات من ZIP
echo 2. انقر مرتين على run_app.bat
echo 3. أو شغل: python main.py
echo.
echo ## بيانات تسجيل الدخول:
echo - اسم المستخدم: admin
echo - كلمة المرور: admin123
echo.
echo ## الملفات المهمة:
echo - run_app.bat: ملف التشغيل الرئيسي
echo - USER_GUIDE.md: دليل المستخدم
echo - INSTALLATION.md: دليل التثبيت
echo.
echo ## المطور:
echo مساعد الذكي - تم التطوير خصيصاً لنظام إدارة الطلاب المتطور
) > "%TEMP_DIR%\PROJECT_INFO.txt"
echo   ✅ PROJECT_INFO.txt

REM ضغط المجلد المؤقت
echo.
echo 🗜️ ضغط الملفات...
powershell -Command "Compress-Archive -Path '%TEMP_DIR%\*' -DestinationPath '%ZIP_NAME%' -Force"

if errorlevel 1 (
    echo ❌ فشل في ضغط الملفات
    goto cleanup
)

REM حذف المجلد المؤقت
rmdir /s /q "%TEMP_DIR%"

REM التحقق من إنشاء الملف المضغوط
if exist "%ZIP_NAME%" (
    echo.
    echo ═══════════════════════════════════════════════════════════════
    echo 🎉 تم ضغط المشروع بنجاح!
    echo ═══════════════════════════════════════════════════════════════
    echo.
    echo 📦 اسم الملف: %ZIP_NAME%
    
    REM حساب حجم الملف
    for %%A in ("%ZIP_NAME%") do set "file_size=%%~zA"
    set /a "file_size_mb=%file_size% / 1048576"
    echo 📏 حجم الملف: %file_size_mb% ميجابايت تقريباً
    echo 📍 المسار: %CD%\%ZIP_NAME%
    echo.
    echo 💡 تعليمات:
    echo 1. احفظ الملف المضغوط في مكان آمن
    echo 2. يمكنك نسخه على أي جهاز Windows
    echo 3. استخرج الملفات وشغل run_app.bat
    echo.
    echo 🎓 نظام إدارة الطلاب جاهز للاستخدام!
    echo.
    
    REM فتح مجلد الملف
    explorer /select,"%ZIP_NAME%"
    
) else (
    echo ❌ فشل في إنشاء الملف المضغوط
)

goto end

:manual_zip
echo.
echo 📋 تعليمات الضغط اليدوي:
echo.
echo 1. حدد جميع الملفات والمجلدات التالية:
echo    - جميع ملفات .py
echo    - جميع ملفات .bat
echo    - جميع ملفات .md
echo    - requirements.txt
echo    - مجلد src
echo    - مجلد data
echo    - مجلد assets
echo.
echo 2. انقر بالزر الأيمن واختر "إرسال إلى" ← "مجلد مضغوط"
echo.
echo 3. أعد تسمية الملف إلى: StudentManagementSystem.zip
echo.

:cleanup
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"

:end
echo.
pause
