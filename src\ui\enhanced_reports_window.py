# -*- coding: utf-8 -*-
"""
نافذة التقارير المحسنة
Enhanced Reports Window

تحتوي على تقارير الحضور والطلاب مع البيانات الكاملة والمعدلات
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QComboBox, QTableWidget, QTableWidgetItem,
                            QTabWidget, QMessageBox, QHeaderView, QTextEdit,
                            QGroupBox, QFormLayout, QDateEdit, QProgressBar,
                            QFrame, QScrollArea, QGridLayout)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QIcon
from datetime import datetime, timedelta
import os

from ..models.student import Student
from ..models.attendance import Attendance
from ..models.grades import Grades
from ..models.groups import Groups
from ..database.database_manager import DatabaseManager

class EnhancedReportsWindow(QWidget):
    """نافذة التقارير المحسنة"""
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
        self.student_model = Student(db_manager)
        self.attendance_model = Attendance(db_manager)
        self.grades_model = Grades(db_manager)
        self.groups_model = Groups(db_manager)
        
        self.init_ui()
        self.apply_styles()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("التقارير المحسنة")
        self.setGeometry(100, 100, 1200, 800)
        
        layout = QVBoxLayout()
        
        # عنوان النافذة
        title_label = QLabel("📊 التقارير المحسنة")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("titleLabel")
        layout.addWidget(title_label)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب تقارير الحضور
        attendance_tab = self.create_attendance_reports_tab()
        self.tabs.addTab(attendance_tab, "📅 تقارير الحضور")
        
        # تبويب تقارير الطلاب
        students_tab = self.create_students_reports_tab()
        self.tabs.addTab(students_tab, "👥 تقارير الطلاب")
        
        # تبويب التقرير الشامل للطالب
        student_detail_tab = self.create_student_detail_tab()
        self.tabs.addTab(student_detail_tab, "📋 التقرير الشامل")
        
        layout.addWidget(self.tabs)
        self.setLayout(layout)
    
    def create_attendance_reports_tab(self):
        """إنشاء تبويب تقارير الحضور"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # فلاتر التقرير
        filters_group = QGroupBox("فلاتر التقرير")
        filters_layout = QFormLayout()
        
        # فلتر المجموعة
        self.attendance_group_combo = QComboBox()
        self.attendance_group_combo.addItem("جميع المجموعات")
        self.populate_groups_combo(self.attendance_group_combo)
        filters_layout.addRow("المجموعة:", self.attendance_group_combo)
        
        # فلتر التاريخ
        date_layout = QHBoxLayout()
        self.attendance_from_date = QDateEdit()
        self.attendance_from_date.setDate(QDate.currentDate().addDays(-30))
        self.attendance_to_date = QDateEdit()
        self.attendance_to_date.setDate(QDate.currentDate())
        date_layout.addWidget(QLabel("من:"))
        date_layout.addWidget(self.attendance_from_date)
        date_layout.addWidget(QLabel("إلى:"))
        date_layout.addWidget(self.attendance_to_date)
        filters_layout.addRow("الفترة:", date_layout)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        generate_attendance_btn = QPushButton("📊 إنشاء التقرير")
        generate_attendance_btn.clicked.connect(self.generate_attendance_report)
        export_attendance_btn = QPushButton("📤 تصدير Excel")
        export_attendance_btn.clicked.connect(self.export_attendance_report)
        buttons_layout.addWidget(generate_attendance_btn)
        buttons_layout.addWidget(export_attendance_btn)
        filters_layout.addRow("", buttons_layout)
        
        filters_group.setLayout(filters_layout)
        layout.addWidget(filters_group)
        
        # جدول تقرير الحضور
        self.attendance_report_table = QTableWidget()
        self.attendance_report_table.setColumnCount(7)
        self.attendance_report_table.setHorizontalHeaderLabels([
            "الكود", "الاسم", "المجموعة", "أيام الحضور", "أيام الغياب", "نسبة الحضور", "الحالة"
        ])
        
        header = self.attendance_report_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.attendance_report_table)
        
        tab.setLayout(layout)
        return tab
    
    def create_students_reports_tab(self):
        """إنشاء تبويب تقارير الطلاب"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # فلاتر التقرير
        filters_group = QGroupBox("فلاتر التقرير")
        filters_layout = QFormLayout()
        
        # فلتر المجموعة
        self.students_group_combo = QComboBox()
        self.students_group_combo.addItem("جميع المجموعات")
        self.populate_groups_combo(self.students_group_combo)
        filters_layout.addRow("المجموعة:", self.students_group_combo)
        
        # فلتر الصف
        self.students_grade_combo = QComboBox()
        self.students_grade_combo.addItem("جميع الصفوف")
        self.populate_grades_combo(self.students_grade_combo)
        filters_layout.addRow("الصف:", self.students_grade_combo)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        generate_students_btn = QPushButton("📊 إنشاء التقرير")
        generate_students_btn.clicked.connect(self.generate_students_report)
        export_students_btn = QPushButton("📤 تصدير Excel")
        export_students_btn.clicked.connect(self.export_students_report)
        buttons_layout.addWidget(generate_students_btn)
        buttons_layout.addWidget(export_students_btn)
        filters_layout.addRow("", buttons_layout)
        
        filters_group.setLayout(filters_layout)
        layout.addWidget(filters_group)
        
        # جدول تقرير الطلاب
        self.students_report_table = QTableWidget()
        self.students_report_table.setColumnCount(8)
        self.students_report_table.setHorizontalHeaderLabels([
            "الكود", "الاسم", "المجموعة", "الصف", "متوسط الدرجات", "نسبة الحضور", "المعدل العام", "الحالة"
        ])
        
        # إضافة إمكانية النقر المزدوج لعرض التفاصيل
        self.students_report_table.itemDoubleClicked.connect(self.show_student_details)
        
        header = self.students_report_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.students_report_table)
        
        tab.setLayout(layout)
        return tab
    
    def create_student_detail_tab(self):
        """إنشاء تبويب التقرير الشامل للطالب"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # اختيار الطالب
        student_selection_group = QGroupBox("اختيار الطالب")
        student_selection_layout = QFormLayout()
        
        self.detail_student_combo = QComboBox()
        self.detail_student_combo.addItem("اختر طالباً", None)
        self.populate_students_combo(self.detail_student_combo)
        self.detail_student_combo.currentTextChanged.connect(self.load_student_detail_report)
        student_selection_layout.addRow("الطالب:", self.detail_student_combo)
        
        student_selection_group.setLayout(student_selection_layout)
        layout.addWidget(student_selection_group)
        
        # منطقة التفاصيل
        self.student_detail_scroll = QScrollArea()
        self.student_detail_widget = QWidget()
        self.student_detail_layout = QVBoxLayout()
        
        # معلومات أساسية
        self.basic_info_group = QGroupBox("المعلومات الأساسية")
        self.basic_info_layout = QFormLayout()
        self.basic_info_group.setLayout(self.basic_info_layout)
        self.student_detail_layout.addWidget(self.basic_info_group)
        
        # إحصائيات الحضور
        self.attendance_stats_group = QGroupBox("إحصائيات الحضور")
        self.attendance_stats_layout = QVBoxLayout()
        self.attendance_stats_group.setLayout(self.attendance_stats_layout)
        self.student_detail_layout.addWidget(self.attendance_stats_group)
        
        # إحصائيات الدرجات
        self.grades_stats_group = QGroupBox("إحصائيات الدرجات")
        self.grades_stats_layout = QVBoxLayout()
        self.grades_stats_group.setLayout(self.grades_stats_layout)
        self.student_detail_layout.addWidget(self.grades_stats_group)
        
        self.student_detail_widget.setLayout(self.student_detail_layout)
        self.student_detail_scroll.setWidget(self.student_detail_widget)
        self.student_detail_scroll.setWidgetResizable(True)
        
        layout.addWidget(self.student_detail_scroll)
        tab.setLayout(layout)
        return tab

    def populate_groups_combo(self, combo):
        """تعبئة قائمة المجموعات"""
        try:
            groups = self.groups_model.get_group_names()
            for group in groups:
                combo.addItem(group)
        except Exception as e:
            print(f"خطأ في تحميل المجموعات: {e}")

    def populate_grades_combo(self, combo):
        """تعبئة قائمة الصفوف"""
        try:
            grades = self.student_model.get_unique_grades()
            for grade in grades:
                combo.addItem(grade)
        except Exception as e:
            print(f"خطأ في تحميل الصفوف: {e}")

    def populate_students_combo(self, combo):
        """تعبئة قائمة الطلاب"""
        try:
            students = self.student_model.get_all_students()
            # ترتيب الطلاب حسب الكود
            students.sort(key=lambda x: int(x['student_code']) if x['student_code'].isdigit() else 0)

            for student in students:
                display_text = f"{student['student_code']} - {student['full_name']}"
                combo.addItem(display_text, student['id'])
        except Exception as e:
            print(f"خطأ في تحميل الطلاب: {e}")

    def generate_attendance_report(self):
        """إنشاء تقرير الحضور"""
        try:
            group_name = self.attendance_group_combo.currentText()
            from_date = self.attendance_from_date.date().toPyDate()
            to_date = self.attendance_to_date.date().toPyDate()

            # جلب الطلاب حسب المجموعة
            if group_name == "جميع المجموعات":
                students = self.student_model.get_all_students()
            else:
                students = self.groups_model.get_students_in_group(group_name)

            # ترتيب الطلاب حسب الكود
            students.sort(key=lambda x: int(x['student_code']) if x['student_code'].isdigit() else 0)

            self.attendance_report_table.setRowCount(len(students))

            for row, student in enumerate(students):
                # حساب إحصائيات الحضور
                attendance_stats = self.attendance_model.get_student_attendance_stats(
                    student['id'], from_date, to_date
                )

                present_days = attendance_stats.get('present_days', 0)
                absent_days = attendance_stats.get('absent_days', 0)
                total_days = present_days + absent_days
                attendance_rate = (present_days / total_days * 100) if total_days > 0 else 0

                # تحديد الحالة
                if attendance_rate >= 90:
                    status = "ممتاز"
                elif attendance_rate >= 80:
                    status = "جيد جداً"
                elif attendance_rate >= 70:
                    status = "جيد"
                elif attendance_rate >= 60:
                    status = "مقبول"
                else:
                    status = "ضعيف"

                # ملء الجدول
                self.attendance_report_table.setItem(row, 0, QTableWidgetItem(student['student_code']))
                self.attendance_report_table.setItem(row, 1, QTableWidgetItem(student['full_name']))
                self.attendance_report_table.setItem(row, 2, QTableWidgetItem(student.get('group_name', 'لا توجد مجموعة')))
                self.attendance_report_table.setItem(row, 3, QTableWidgetItem(str(present_days)))
                self.attendance_report_table.setItem(row, 4, QTableWidgetItem(str(absent_days)))
                self.attendance_report_table.setItem(row, 5, QTableWidgetItem(f"{attendance_rate:.1f}%"))
                self.attendance_report_table.setItem(row, 6, QTableWidgetItem(status))

            QMessageBox.information(self, "تم", "تم إنشاء تقرير الحضور بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء تقرير الحضور: {str(e)}")

    def generate_students_report(self):
        """إنشاء تقرير الطلاب"""
        try:
            group_name = self.students_group_combo.currentText()
            grade_name = self.students_grade_combo.currentText()

            # جلب الطلاب حسب الفلاتر
            if group_name == "جميع المجموعات":
                students = self.student_model.get_all_students()
            else:
                students = self.groups_model.get_students_in_group(group_name)

            # فلترة حسب الصف
            if grade_name != "جميع الصفوف":
                students = [s for s in students if s['grade'] == grade_name]

            # ترتيب الطلاب حسب الكود
            students.sort(key=lambda x: int(x['student_code']) if x['student_code'].isdigit() else 0)

            self.students_report_table.setRowCount(len(students))

            for row, student in enumerate(students):
                # حساب متوسط الدرجات
                grades_avg = self.grades_model.get_student_average(student['id'])
                avg_percentage = grades_avg.get('average_percentage', 0) if grades_avg else 0

                # حساب نسبة الحضور
                attendance_stats = self.attendance_model.get_student_attendance_stats(student['id'])
                present_days = attendance_stats.get('present_days', 0)
                absent_days = attendance_stats.get('absent_days', 0)
                total_days = present_days + absent_days
                attendance_rate = (present_days / total_days * 100) if total_days > 0 else 0

                # حساب المعدل العام
                overall_rating = self.calculate_overall_rating(avg_percentage, attendance_rate)

                # تحديد الحالة
                if overall_rating >= 85:
                    status = "ممتاز"
                elif overall_rating >= 75:
                    status = "جيد جداً"
                elif overall_rating >= 65:
                    status = "جيد مرتفع"
                elif overall_rating >= 50:
                    status = "جيد"
                elif overall_rating >= 40:
                    status = "مقبول"
                else:
                    status = "ضعيف"

                # ملء الجدول
                self.students_report_table.setItem(row, 0, QTableWidgetItem(student['student_code']))
                self.students_report_table.setItem(row, 1, QTableWidgetItem(student['full_name']))
                self.students_report_table.setItem(row, 2, QTableWidgetItem(student.get('group_name', 'لا توجد مجموعة')))
                self.students_report_table.setItem(row, 3, QTableWidgetItem(student['grade']))
                self.students_report_table.setItem(row, 4, QTableWidgetItem(f"{avg_percentage:.1f}%"))
                self.students_report_table.setItem(row, 5, QTableWidgetItem(f"{attendance_rate:.1f}%"))
                self.students_report_table.setItem(row, 6, QTableWidgetItem(status))
                self.students_report_table.setItem(row, 7, QTableWidgetItem(status))

            QMessageBox.information(self, "تم", "تم إنشاء تقرير الطلاب بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء تقرير الطلاب: {str(e)}")

    def calculate_overall_rating(self, grades_avg, attendance_rate):
        """حساب المعدل العام (70% درجات + 30% حضور)"""
        return (grades_avg * 0.7) + (attendance_rate * 0.3)

    def show_student_details(self, item):
        """عرض تفاصيل الطالب عند النقر المزدوج"""
        row = item.row()
        student_code = self.students_report_table.item(row, 0).text()

        # البحث عن الطالب في القائمة وتحديده
        for i in range(self.detail_student_combo.count()):
            if self.detail_student_combo.itemText(i).startswith(student_code):
                self.detail_student_combo.setCurrentIndex(i)
                self.tabs.setCurrentIndex(2)  # الانتقال لتبويب التفاصيل
                break

    def load_student_detail_report(self):
        """تحميل التقرير الشامل للطالب"""
        student_id = self.detail_student_combo.currentData()
        if not student_id:
            self.clear_student_details()
            return

        try:
            # جلب بيانات الطالب
            student = self.student_model.get_student_by_id(student_id)
            if not student:
                return

            # مسح التفاصيل السابقة
            self.clear_student_details()

            # المعلومات الأساسية
            self.load_basic_info(student)

            # إحصائيات الحضور
            self.load_attendance_stats(student_id)

            # إحصائيات الدرجات
            self.load_grades_stats(student_id)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل تفاصيل الطالب: {str(e)}")

    def clear_student_details(self):
        """مسح تفاصيل الطالب"""
        # مسح المعلومات الأساسية
        for i in reversed(range(self.basic_info_layout.count())):
            self.basic_info_layout.itemAt(i).widget().setParent(None)

        # مسح إحصائيات الحضور
        for i in reversed(range(self.attendance_stats_layout.count())):
            self.attendance_stats_layout.itemAt(i).widget().setParent(None)

        # مسح إحصائيات الدرجات
        for i in reversed(range(self.grades_stats_layout.count())):
            self.grades_stats_layout.itemAt(i).widget().setParent(None)

    def load_basic_info(self, student):
        """تحميل المعلومات الأساسية"""
        info_items = [
            ("الاسم:", student['full_name']),
            ("الكود:", student['student_code']),
            ("المجموعة:", student.get('group_name', 'لا توجد مجموعة')),
            ("الصف:", student['grade']),
            ("المرحلة:", student['stage']),
            ("الجنس:", student['gender']),
            ("رقم الهاتف:", student.get('phone', 'غير محدد')),
            ("رقم ولي الأمر:", student.get('parent_phone', 'غير محدد'))
        ]

        for label, value in info_items:
            self.basic_info_layout.addRow(QLabel(label), QLabel(str(value)))

    def load_attendance_stats(self, student_id):
        """تحميل إحصائيات الحضور"""
        try:
            # إحصائيات عامة
            stats = self.attendance_model.get_student_attendance_stats(student_id)
            present_days = stats.get('present_days', 0)
            absent_days = stats.get('absent_days', 0)
            total_days = present_days + absent_days
            attendance_rate = (present_days / total_days * 100) if total_days > 0 else 0

            # عرض الإحصائيات
            stats_text = f"""
            📊 الإحصائيات العامة:
            • عدد أيام الحضور: {present_days} يوم
            • عدد أيام الغياب: {absent_days} يوم
            • إجمالي الأيام: {total_days} يوم
            • نسبة الحضور: {attendance_rate:.1f}%
            """

            stats_label = QLabel(stats_text)
            stats_label.setWordWrap(True)
            self.attendance_stats_layout.addWidget(stats_label)

            # شريط التقدم
            progress_bar = QProgressBar()
            progress_bar.setValue(int(attendance_rate))
            progress_bar.setFormat(f"نسبة الحضور: {attendance_rate:.1f}%")
            self.attendance_stats_layout.addWidget(progress_bar)

            # تفاصيل الحضور والغياب
            attendance_details = self.attendance_model.get_student_attendance_details(student_id)

            if attendance_details:
                details_text = QTextEdit()
                details_text.setMaximumHeight(200)

                present_dates = []
                absent_dates = []

                for record in attendance_details:
                    if record['status'] == 'حاضر':
                        present_dates.append(record['date'])
                    else:
                        absent_dates.append(record['date'])

                content = ""
                if present_dates:
                    content += "✅ أيام الحضور:\n"
                    content += ", ".join(present_dates[:10])  # أول 10 أيام
                    if len(present_dates) > 10:
                        content += f" ... و {len(present_dates) - 10} يوم آخر"
                    content += "\n\n"

                if absent_dates:
                    content += "❌ أيام الغياب:\n"
                    content += ", ".join(absent_dates)

                details_text.setPlainText(content)
                self.attendance_stats_layout.addWidget(details_text)

        except Exception as e:
            error_label = QLabel(f"خطأ في تحميل إحصائيات الحضور: {str(e)}")
            self.attendance_stats_layout.addWidget(error_label)

    def load_grades_stats(self, student_id):
        """تحميل إحصائيات الدرجات"""
        try:
            # جلب درجات الطالب
            grades = self.grades_model.get_student_grades(student_id)

            if not grades:
                no_grades_label = QLabel("لا توجد درجات مسجلة لهذا الطالب")
                self.grades_stats_layout.addWidget(no_grades_label)
                return

            # حساب الإحصائيات
            total_score = sum(grade['score'] for grade in grades)
            total_max = sum(grade['max_score'] for grade in grades)
            average_percentage = (total_score / total_max * 100) if total_max > 0 else 0

            # تحديد المعدل العام
            if average_percentage >= 90:
                rating = "ممتاز"
            elif average_percentage >= 80:
                rating = "جيد جداً"
            elif average_percentage >= 70:
                rating = "جيد مرتفع"
            elif average_percentage >= 60:
                rating = "جيد"
            elif average_percentage >= 50:
                rating = "مقبول"
            else:
                rating = "ضعيف"

            # عرض الإحصائيات
            stats_text = f"""
            📊 الإحصائيات العامة:
            • عدد الامتحانات: {len(grades)}
            • إجمالي الدرجات: {total_score:.1f} من {total_max:.1f}
            • المتوسط العام: {average_percentage:.1f}%
            • المعدل العام: {rating}
            """

            stats_label = QLabel(stats_text)
            stats_label.setWordWrap(True)
            self.grades_stats_layout.addWidget(stats_label)

            # شريط التقدم
            progress_bar = QProgressBar()
            progress_bar.setValue(int(average_percentage))
            progress_bar.setFormat(f"المتوسط العام: {average_percentage:.1f}%")
            self.grades_stats_layout.addWidget(progress_bar)

            # جدول الدرجات
            grades_table = QTableWidget()
            grades_table.setColumnCount(5)
            grades_table.setHorizontalHeaderLabels([
                "المادة", "نوع الامتحان", "الدرجة", "من", "النسبة"
            ])
            grades_table.setRowCount(len(grades))

            for row, grade in enumerate(grades):
                percentage = (grade['score'] / grade['max_score']) * 100
                grades_table.setItem(row, 0, QTableWidgetItem(grade['subject']))
                grades_table.setItem(row, 1, QTableWidgetItem(grade['exam_type']))
                grades_table.setItem(row, 2, QTableWidgetItem(f"{grade['score']:.1f}"))
                grades_table.setItem(row, 3, QTableWidgetItem(f"{grade['max_score']:.1f}"))
                grades_table.setItem(row, 4, QTableWidgetItem(f"{percentage:.1f}%"))

            grades_table.setMaximumHeight(300)
            header = grades_table.horizontalHeader()
            header.setSectionResizeMode(QHeaderView.Stretch)
            self.grades_stats_layout.addWidget(grades_table)

        except Exception as e:
            error_label = QLabel(f"خطأ في تحميل إحصائيات الدرجات: {str(e)}")
            self.grades_stats_layout.addWidget(error_label)

    def export_attendance_report(self):
        """تصدير تقرير الحضور إلى Excel"""
        try:
            from openpyxl import Workbook

            wb = Workbook()
            ws = wb.active
            ws.title = "تقرير الحضور"

            # العناوين
            headers = ["الكود", "الاسم", "المجموعة", "أيام الحضور", "أيام الغياب", "نسبة الحضور", "الحالة"]
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # البيانات
            for row in range(self.attendance_report_table.rowCount()):
                for col in range(self.attendance_report_table.columnCount()):
                    item = self.attendance_report_table.item(row, col)
                    if item:
                        ws.cell(row=row+2, column=col+1, value=item.text())

            # حفظ الملف
            filename = f"تقرير_الحضور_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            wb.save(filename)

            QMessageBox.information(self, "تم", f"تم تصدير التقرير إلى: {filename}")

        except ImportError:
            QMessageBox.warning(self, "تحذير", "يرجى تثبيت مكتبة openpyxl لتصدير Excel")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير: {str(e)}")

    def export_students_report(self):
        """تصدير تقرير الطلاب إلى Excel"""
        try:
            from openpyxl import Workbook

            wb = Workbook()
            ws = wb.active
            ws.title = "تقرير الطلاب"

            # العناوين
            headers = ["الكود", "الاسم", "المجموعة", "الصف", "متوسط الدرجات", "نسبة الحضور", "المعدل العام", "الحالة"]
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # البيانات
            for row in range(self.students_report_table.rowCount()):
                for col in range(self.students_report_table.columnCount()):
                    item = self.students_report_table.item(row, col)
                    if item:
                        ws.cell(row=row+2, column=col+1, value=item.text())

            # حفظ الملف
            filename = f"تقرير_الطلاب_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            wb.save(filename)

            QMessageBox.information(self, "تم", f"تم تصدير التقرير إلى: {filename}")

        except ImportError:
            QMessageBox.warning(self, "تحذير", "يرجى تثبيت مكتبة openpyxl لتصدير Excel")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير: {str(e)}")

    def apply_styles(self):
        """تطبيق الأنماط"""
        style = """
        QWidget {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            font-size: 12px;
            background-color: #f8f9fa;
        }

        #titleLabel {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
            background-color: white;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #2c3e50;
        }

        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: bold;
        }

        QPushButton:hover {
            background-color: #2980b9;
        }

        QPushButton:pressed {
            background-color: #21618c;
        }

        QTableWidget {
            background-color: white;
            border: 1px solid #bdc3c7;
            border-radius: 6px;
            gridline-color: #ecf0f1;
        }

        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #ecf0f1;
        }

        QTableWidget::item:selected {
            background-color: #3498db;
            color: white;
        }

        QHeaderView::section {
            background-color: #34495e;
            color: white;
            padding: 10px;
            border: none;
            font-weight: bold;
        }

        QComboBox, QDateEdit {
            padding: 8px;
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            background-color: white;
        }

        QComboBox:focus, QDateEdit:focus {
            border-color: #3498db;
        }

        QProgressBar {
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
        }

        QProgressBar::chunk {
            background-color: #27ae60;
            border-radius: 3px;
        }

        QTextEdit {
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            background-color: white;
            padding: 5px;
        }
        """

        self.setStyleSheet(style)
