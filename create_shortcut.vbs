Set WshShell = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' الحصول على مسار المجلد الحالي
currentDir = fso.GetParentFolderName(WScript.ScriptFullName)

' إنشاء اختصار على سطح المكتب
desktopPath = WshShell.SpecialFolders("Desktop")
shortcutPath = desktopPath & "\نظام إدارة الطلاب المتطور.lnk"

Set shortcut = WshShell.CreateShortcut(shortcutPath)
shortcut.TargetPath = currentDir & "\RUN_INVISIBLE.vbs"
shortcut.WorkingDirectory = currentDir
shortcut.Description = "نظام إدارة الطلاب المتطور - تشغيل مباشر"
shortcut.Save

' إنشاء اختصار في قائمة ابدأ
startMenuPath = WshShell.SpecialFolders("StartMenu") & "\Programs"
startShortcutPath = startMenuPath & "\نظام إدارة الطلاب المتطور.lnk"

Set startShortcut = WshShell.CreateShortcut(startShortcutPath)
startShortcut.TargetPath = currentDir & "\RUN_INVISIBLE.vbs"
startShortcut.WorkingDirectory = currentDir
startShortcut.Description = "نظام إدارة الطلاب المتطور"
startShortcut.Save

MsgBox "تم إنشاء اختصارات البرنامج بنجاح!" & vbCrLf & vbCrLf & "• اختصار على سطح المكتب" & vbCrLf & "• اختصار في قائمة ابدأ" & vbCrLf & vbCrLf & "يمكنك الآن تشغيل البرنامج مباشرة بدون أي نوافذ إضافية", vbInformation, "تم بنجاح"
